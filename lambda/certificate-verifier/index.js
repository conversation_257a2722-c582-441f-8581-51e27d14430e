const { X509Certificate } = await import('node:crypto');

import * as https from 'https';

const successResponse = {
  "isAuthorized": true
}

export const handler = async (event) => {
  console.log(event);

  let clientCert = event.requestContext.authentication.clientCert;
  if (clientCert == null) {
    return {
      "isAuthorized": false,
      "context": {
        "exception": "Missing client certificate!"
      }
    }
  }

  const x509 = new X509Certificate(clientCert.clientCertPem);
  console.log(x509);

  const endpoint = process.env.DMS_PUBLIC_ENDPOINT;
  if (endpoint == null) {
    return successResponse;
  }

  const caName = x509.issuer.replace("CN=", "");
  const serialNumber = x509.serialNumber;

  const ts = new Date().getTime();

  try {
    const certStatusUrl = `${endpoint}/public/pki/authority/${caName}/certificate/${serialNumber}/status?ts=${ts}`;
    console.log(certStatusUrl);
    const result = await getRequest(certStatusUrl);
    console.log("result", result);
    return {
      "isAuthorized": result
    };
  }
  catch (e) {
    console.log(e);
  }

  return successResponse;
};


function getRequest(url) {

  return new Promise((resolve, reject) => {
    const req = https.get(url, res => {

      let rawData = '';

      res.on('data', chunk => {
        rawData += chunk;
      });

      res.on('end', () => {
        console.log("data", rawData);

        switch (rawData) {
          case 'GOOD':
            resolve(true);
            break;
          case 'REVOKED':
            resolve(false);
            break;
          case 'NOTFOUND':
            resolve(false);
            break;
          default:
            resolve(true);
            break;
        }

      });
    });

    req.on('error', err => {
      console.log(err)
      reject(true);
    });
  });
}
