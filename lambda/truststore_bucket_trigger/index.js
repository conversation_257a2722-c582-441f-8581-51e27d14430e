import { S3Client, GetObjectCommand, PutObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import crypto from 'crypto';


const region = process.env.S3_REGION;
const destinationBucket = process.env.S3_DESTINATION_BUCKET;
const destinationObject = process.env.S3_DESTINATION_OBJECT;

const client = new S3Client();
    
export const handler = async (event, context) => {
    
    const bucket = event.Records[0].s3.bucket.name;

    const sourceParams = {
        Bucket: bucket,
    }; 
    
    const destParams = {
        Bucket: destinationBucket,
        Key: destinationObject
    }

      const listObjectsData = async (params, getData) => {
        const listSourceObjectsCommand = new ListObjectsV2Command(params);
        let listData = [];
        try {
          let isTruncated = true;
          while (isTruncated) {
            const { Contents, IsTruncated, NextContinuationToken } = await client.send(listSourceObjectsCommand);
            listData = [...listData, ...(await Promise.all(Contents.map(async (c) => {
              const key = `${c.Key}`;
              const data = await getData({ ...params, Key: key });
              if(data != null) {
                return data;
              }
            })))];
            isTruncated = IsTruncated;
            listSourceObjectsCommand.input.ContinuationToken = NextContinuationToken;
          }
        } catch (err) {
          console.log(err)
          throw err;
        }
        return listData.join("\n");
      };
    
    const getObjectData = async (params) => {
      const object = await client.send(new GetObjectCommand(params));
      const data = await object.Body.transformToString();
      try {
        const certificate =  new crypto.X509Certificate(data);
        const certificateExpiryTime = Date.parse(certificate.validTo);
        const timeNow = Date.now();
        if(certificateExpiryTime > timeNow) {
          return null;
        }
      } catch (err) {
        console.log(err.toString());
        return null;
      }
      return data;
    };

    const putObject = async (params, data) => {
        params.Body = new Buffer.from(data, 'utf8');
        const putDestCommand = new PutObjectCommand(params);
        const response = await client.send(putDestCommand);
        return response;
    };
    
    try {
        const data = await listObjectsData(sourceParams, getObjectData);
        const response = await putObject(destParams, data);

        const truststoreInfo = {
          uri: "arn",
          objectVersion: response.VersionId
        }
        return truststoreInfo;
    } catch (err) {
        console.log(err);
         throw err;
    }
};

