maven-package:
  image: maven:3.8.5-openjdk-17
  stage: package
  only:
   - tags
  tags:
    - package
  script: 
    - "mvn clean package -Dmaven.test.skip=true"
  artifacts:
    paths:
      - ./target/*.jar
    expire_in: "30 days"

# docker-build-dev:
#   stage: build_and_push_dev
#   image: 
#     name: gcr.io/kaniko-project/executor:v1.9.0-debug
#     entrypoint: [""]
#   variables:
#     REGISTRY: "nexus-docker.styl.solutions/repository/docker-hosted/caribbean-rebuild/dev-image/styl-dms"
#     TAG_LATEST: $REGISTRY/$CI_COMMIT_REF_NAME:latest
#     TAG_COMMIT: $REGISTRY/$CI_COMMIT_REF_NAME:$CI_COMMIT_SHORT_SHA
#   tags:
#     - build
#   script:
#     - |
#       mkdir -p /kaniko/.docker
#       echo "{\"auths\":{\"${CI_REGISTRY}\":{\"auth\":\"$(printf "%s:%s" "${CI_REGISTRY_USER}" "${CI_REGISTRY_PASSWORD}" | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
#     - >-
#       /kaniko/executor
#       --context "$CI_PROJECT_DIR"
#       --dockerfile "$CI_PROJECT_DIR/docker/Dockerfile"
#       --destination "${TAG_LATEST}"
#       --destination "${TAG_COMMIT}"
#       --cache=true
#       --cache-repo "${TAG_LATEST}"
#     - mkdir -p /workspace
#   dependencies:
#     - maven-package
#   needs:
#     - maven-package

docker-build-aws:
  stage: build_and_push_aws
  image: 
    name: gcr.io/kaniko-project/executor:v1.9.0-debug
    entrypoint: [""]
  variables:
    AWS_DEFAULT_REGION: "ap-southeast-1"
    ECR_NAME: dms
    ECR_URL: "406972242998.dkr.ecr.ap-southeast-1.amazonaws.com"
    AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID_SIT}
    AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY_SIT}
    TAG_LATEST: $ECR_URL/$ECR_NAME:latest
    TAG_COMMIT: $ECR_URL/$ECR_NAME:$CI_COMMIT_TAG
  tags:
    - build
  script:
    - |
      mkdir -p /kaniko/.docker
      echo "{\"credHelpers\":{\"${ECR_URL}\":\"ecr-login\"}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor
      --context "$CI_PROJECT_DIR"
      --dockerfile "$CI_PROJECT_DIR/docker/Dockerfile"
      --destination "${TAG_LATEST}"
      --destination "${TAG_COMMIT}"
      --cache=true
      --cache-repo "${TAG_LATEST}"
    - mkdir -p /workspace
  dependencies:
    - maven-package
  needs:
    - maven-package
