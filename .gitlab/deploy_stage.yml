# deploy-app-dev:
#   image: 
#     name: alpine:latest
#     entrypoint: [""]
#   stage: deploy
#   rules:
#     - if: $CI_COMMIT_BRANCH == "develop"
#   tags:
#     - deploy
#   extends:
#     - .pre-deploy-prod
#   script:
#     - scp docker/docker-compose.yml $SSH_USER@$VM_IPADDRESS:/home/<USER>/dms-data
#     - scp docker/docker-start.sh $SSH_USER@$VM_IPADDRESS:/home/<USER>/dms-data
#     - scp docker/krakend.json $SSH_USER@$VM_IPADDRESS:/home/<USER>/dms-data/krakend
#     - ssh $SSH_USER@$VM_IPADDRESS "sudo docker login nexus-docker.styl.solutions -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}"
#     - ssh $SSH_USER@$VM_IPADDRESS "cd /home/<USER>/dms-data && sh docker-start.sh"
#   environment:
#     name: development
#     url: https://oceans-dev.styl.solutions/portal
#   needs:
#     - trivy_container_scanning

deploy-app-uat:
  stage: deploy_uat
  image: 
    name: amazon/aws-cli:2.11.0
  variables:
    AWS_EZ_CLUSTER_NAME: DMS-SIT-Cluster
    AWS_EZ_SERVICE_NAME: DMS-SIT-Service
    AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID_SIT}
    AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY_SIT}
    AWS_DEFAULT_REGION: "ap-southeast-1"
    AWS_EZ_TASK_NAME: dms
    ECS_IMAGE_PULL_BEHAVIOR: always
  rules:
    - if: $CI_COMMIT_TAG =~ "/^[vV]\d+\.\d+\.\d+(-rc\d+)?$/"
    - if: $CI_COMMIT_BRANCH == "upgrade/spring-boot-3"
  tags:
    - deploy
  script:
    # - |
    #   export MANIFEST=$(aws ecr batch-get-image --repository-name amazonlinux --image-ids imageTag=${CI_COMMIT_SHORT_SHA} --output json | jq --raw-output --join-output '.images[0].imageManifest')
    #   echo $MANIFEST
    - |
      aws ecs update-service \
          --cluster $AWS_EZ_CLUSTER_NAME \
          --service $AWS_EZ_SERVICE_NAME \
          --task-definition $AWS_EZ_TASK_NAME \
          --force-new-deployment \
          --region $AWS_DEFAULT_REGION
  environment:
    name: uat
    url: https://oceans-uat.styl.solutions
  needs:
    - docker-build-aws
  when: manual

deploy-app-prod:
  stage: deploy_prod
  image: 
    name: amazon/aws-cli:2.11.0
  variables:
    AWS_EZ_CLUSTER_NAME: DMS-PROD-Cluster
    AWS_EZ_SERVICE_NAME: DMS-PROD-Service
    AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID_PROD}
    AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY_PROD}
    AWS_DEFAULT_REGION: "ap-southeast-1"
    AWS_EZ_TASK_NAME: dms
    ECS_IMAGE_PULL_BEHAVIOR: always
  rules:
    - if: $CI_COMMIT_TAG =~ "/^[vV]\d+\.\d+\.\d+$/"
  tags:
    - deploy
  script:
    # - |
    #   export MANIFEST=$(aws ecr batch-get-image --repository-name amazonlinux --image-ids imageTag=${CI_COMMIT_SHORT_SHA} --output json | jq --raw-output --join-output '.images[0].imageManifest')
    #   echo $MANIFEST
    - |
      aws ecs update-service \
          --cluster $AWS_EZ_CLUSTER_NAME \
          --service $AWS_EZ_SERVICE_NAME \
          --task-definition $AWS_EZ_TASK_NAME \
          --force-new-deployment \
          --region $AWS_DEFAULT_REGION
  environment:
    name: prod
    url: https://oceans.styl.solutions
  needs:
    - deploy-app-uat
  when: manual