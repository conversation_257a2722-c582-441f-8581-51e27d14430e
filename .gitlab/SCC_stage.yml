# trivy_container_scanning:
#   stage: SCC 
#   image: alpine:3.11
#   tags:
#     - SCA
#   rules:
#     - if: $CI_COMMIT_BRANCH == "develop"
#   extends:
#     - .pre-build-dev
#   script:
#     - export TRIVY_VERSION=${TRIVY_VERSION:-v0.19.2}
#     - apk add --no-cache curl docker-cli
#     - docker login -u "${CI_REGISTRY_USER}" -p "${CI_REGISTRY_PASSWORD}" ${CI_REGISTRY}
#     - curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin ${TRIVY_VERSION}
#     - curl -sSL -o /tmp/trivy-gitlab.tpl https://github.com/aquasecurity/trivy/raw/${TRIVY_VERSION}/contrib/gitlab.tpl
#     # - trivy exit-code 0 --cache-dir .trivycache/ --no-progress --format template --template "@/tmp/trivy-gitlab.tpl" -o ${CI_PROJECT_NAME}-container-scanning-report.json ${TAG_LATEST}
#     - trivy image --no-progress --exit-code 0 --format template --template "@/tmp/trivy-gitlab.tpl" -o ${CI_PROJECT_NAME}-container-scanning-report.json ${TAG_LATEST}
#     # - trivy image --no-progress --exit-code 0 --format template --template "@/usr/local/share/trivy/templates/html.tpl" -o ${CI_PROJECT_NAME}-container-scanning-report.htlm ${TAG_LATEST}
#   artifacts:
#     reports:
#       container_scanning: ${CI_PROJECT_NAME}-container-scanning-report.json
#     expire_in: "30 days"
#   dependencies: []
#   needs:
#     - docker-build