/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.rest.service.platform;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Calendar;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

import org.apache.commons.lang3.BooleanUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.device.management.auth.service.platform.AbstractControllerTest;
import com.styl.device.management.auth.service.platform.PreConditionUtil;
import com.styl.device.management.persistence.api.key.ApiKey;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.software.update.mode.UpdateMode;
import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.utils.CryptoUtils;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Yee
 *
 */
@Disabled
@Transactional
public class ServicePlatformControllerTest extends AbstractControllerTest {

	@PersistenceContext
	private EntityManager entityManager;

	private static final String SERVICE_PLATFORM_NAME = "Caribbean";

//	 @Disabled
	@Test
//	@Commit
	public void testPrepareData() throws InvalidKeyException, NoSuchAlgorithmException {
		// String userId = "user";
//		ServicePlatform sp = testGetTestServicePlatform();
//		DeviceModel model = PreConditionUtil.addDeviceModel(entityManager, "dm1");
//		Device device = PreConditionUtil.addDevice(entityManager, model, sp, "test-device");
//		Software software = PreConditionUtil.addSoftware(entityManager, sp, model, "test-software");
//		UpdateMode um = entityManager.find(UpdateMode.class, 1);
//		SoftwareVersion sv = PreConditionUtil.addSoftwareVersion(entityManager, software, "test-softwareversion",
//				"1.0.0", um);
//		PreConditionUtil.addSoftwarePackages(entityManager, device, software, sv, 1, userId, um.getModeId());

		HttpHeaders headers1 = getHeaders(null);

		System.out.println(headers1.get(PreConditionUtil.API_KEY));
		System.out.println(headers1.get(PreConditionUtil.NONCE));
		System.out.println(headers1.get(PreConditionUtil.SIGNATURE));
	}

	private HttpHeaders getHeaders(String queryParams, boolean printNote)
			throws InvalidKeyException, NoSuchAlgorithmException {
		return getHeaders(null, queryParams, printNote);
	}

	private HttpHeaders getHeaders(String queryParams) throws InvalidKeyException, NoSuchAlgorithmException {
		return getHeaders(null, queryParams, false);
	}

	private HttpHeaders getHeaders(String body, String queryParams, Boolean printNote)
			throws InvalidKeyException, NoSuchAlgorithmException {
		ServicePlatform sp = testGetTestServicePlatform();
		ApiKey apiKey = testGetApiKey(sp.getId());

		String nonce = String.format("%s%s%s", System.currentTimeMillis(), "#", UUID.randomUUID());
		StringBuilder signatureData = new StringBuilder();
		if (body != null) {
			signatureData.append(body);
		}
		if (queryParams != null) {
			signatureData.append(queryParams);
		}
		signatureData.append(nonce);

		HttpHeaders headers = new HttpHeaders();
		headers.add(PreConditionUtil.API_KEY, apiKey.getApiKey());
		headers.add(PreConditionUtil.NONCE, nonce);
		headers.add(PreConditionUtil.SIGNATURE,
				CryptoUtils.hmacSHA256(apiKey.getSecretKey(), signatureData.toString()));
		if (BooleanUtils.isTrue(printNote)) {
			System.out.println(queryParams);
			System.out.println(body);
			System.out.println(apiKey.getApiKey());
			System.out.println(nonce);
			System.out.println(CryptoUtils.hmacSHA256(apiKey.getSecretKey(), signatureData.toString()));
		}

		return headers;
	}

	private ServicePlatform testGetTestServicePlatform() {
		TypedQuery<ServicePlatform> query = entityManager
				.createQuery("SELECT sp FROM ServicePlatform sp WHERE sp.name=:name", ServicePlatform.class);
		query.setParameter("name", SERVICE_PLATFORM_NAME);
		List<ServicePlatform> result = query.getResultList();
		if (result != null && !result.isEmpty()) {
			return result.get(0);
		} else {
			return PreConditionUtil.addServicePlatform(entityManager, SERVICE_PLATFORM_NAME);
		}
	}

	private ApiKey testGetApiKey(Integer servicePlatformId) {
		TypedQuery<ApiKey> query = entityManager.createQuery(
				"SELECT k FROM ApiKey k WHERE k.servicePlatformId=:servicePlatformId and k.expiredTime >:now",
				ApiKey.class);
		query.setParameter("servicePlatformId", servicePlatformId);
		query.setParameter("now", System.currentTimeMillis());
		List<ApiKey> result = query.getResultList();
		if (result != null && !result.isEmpty()) {
			return result.get(0);
		} else {
			return PreConditionUtil.addApiKey(entityManager, servicePlatformId, "user");
		}
	}

	@Test
	public void testSignatureGeneration() throws InvalidKeyException, NoSuchAlgorithmException {

		HttpHeaders headers1 = getHeaders(null);

		HttpHeaders headers2 = getHeaders("page=0&pageSize=8");

		System.out.println(headers1.get(PreConditionUtil.API_KEY));
		System.out.println(headers1.get(PreConditionUtil.NONCE));
		System.out.println(headers1.get(PreConditionUtil.SIGNATURE));

		System.out.println(headers2.get(PreConditionUtil.API_KEY));
		System.out.println(headers2.get(PreConditionUtil.NONCE));
		System.out.println(headers2.get(PreConditionUtil.SIGNATURE));
	}

	public String convertObjectToJson(Object object) throws JsonProcessingException {
		if (object == null) {
			return null;
		}
		ObjectMapper mapper = new ObjectMapper();
		return mapper.writeValueAsString(object);
	}

	@SuppressWarnings("unchecked")
	public Map<String, String> convertObjectToMap(Object obj) {

		ObjectMapper objectMapper = new ObjectMapper();

		return objectMapper.convertValue(obj, Map.class);
	}

	public <T> T getObject(String data, Class<T> type) {
		T target = null;
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			target = objectMapper.readValue(data, type);
			// JAXBUtils.marshal(target);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return target;
	}

	@Test
	public void testGetDetail() throws UnsupportedEncodingException, Exception {
		HttpHeaders headers = getHeaders(null);
		String result = this.mvc.perform(get("/serviceplatform/detail").headers(headers)).andExpect(status().isOk())
				.andReturn().getResponse().getContentAsString();
		assertEquals(mapToJson(new ServicePlatformDetailResponse(testGetTestServicePlatform())), result);
	}

	@Test
	public void testGetListDevice() throws Exception {

		Calendar calTwoDayBef = Calendar.getInstance();
		calTwoDayBef.add(Calendar.DAY_OF_YEAR, -2);
		Calendar calYesterday = Calendar.getInstance();
		calYesterday.add(Calendar.DAY_OF_YEAR, -1);

		ServicePlatform sp = testGetTestServicePlatform();
		ServicePlatform spOther = PreConditionUtil.addServicePlatform(entityManager, "test");
		DeviceModel model = PreConditionUtil.addDeviceModel(entityManager, "dm1");
		DeviceModel model2 = PreConditionUtil.addDeviceModel(entityManager, "dm2");

		Device device = PreConditionUtil.addDevice(entityManager, model, sp, "test-device1");
		Device device2 = PreConditionUtil.addDevice(entityManager, model, sp, "test-device2");
		assertEquals("test-device2-hardwareId", device2.getHardwareId());
		Device device3 = PreConditionUtil.addDevice(entityManager, model, sp, "test-device3");
		assertEquals("test-device3-sim", device3.getSimId());
		Device device4 = PreConditionUtil.addDevice(entityManager, model, sp, "test-device4");
		Device device5 = PreConditionUtil.addDevice(entityManager, model2, sp, "test-device5");
		assertEquals("test-device5-imei", device5.getImei());
		Device device6 = PreConditionUtil.addDevice(entityManager, model2, sp, "test-device6");
		device6.setState(2);
		Device device7 = PreConditionUtil.addDevice(entityManager, model2, sp, "test-device7");
		Device device8 = PreConditionUtil.addDevice(entityManager, model2, sp, "test-device8");
		device8.setCreatedTime(calYesterday.getTimeInMillis());
		PreConditionUtil.addDevice(entityManager, model2, spOther, "test-device9");

		MockHttpServletResponse resultFilterId = this.mvc
				.perform(
						get("/serviceplatform/device?id=" + device.getId()).headers(getHeaders("id=" + device.getId())))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterId = getObject(resultFilterId.getContentAsString(),
				Pagination.class);
		assertEquals(1, dataFilterId.getData().size());
		assertEquals(device.getId(), dataFilterId.getData().get(0).get("id"));

		MockHttpServletResponse resultFilterModel = this.mvc
				.perform(get("/serviceplatform/device?model=dm1").headers(getHeaders("model=dm1")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterModel = getObject(resultFilterModel.getContentAsString(),
				Pagination.class);
		assertEquals(4, dataFilterModel.getData().size());
		assertEquals(device.getId(), dataFilterModel.getData().get(0).get("id"));
		assertEquals(device2.getId(), dataFilterModel.getData().get(1).get("id"));
		assertEquals(device3.getId(), dataFilterModel.getData().get(2).get("id"));
		assertEquals(device4.getId(), dataFilterModel.getData().get(3).get("id"));

		MockHttpServletResponse resultFilterHardware = this.mvc
				.perform(get("/serviceplatform/device?hardwareId=test-device2-hardwareId")
						.headers(getHeaders("hardwareId=test-device2-hardwareId")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterHardware = getObject(
				resultFilterHardware.getContentAsString(), Pagination.class);
		assertEquals(1, dataFilterHardware.getData().size());
		assertEquals(device2.getId(), dataFilterHardware.getData().get(0).get("id"));

		MockHttpServletResponse resultFilterSim = this.mvc
				.perform(get("/serviceplatform/device?simId=test-device3-sim")
						.headers(getHeaders("simId=test-device3-sim")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterSim = getObject(resultFilterSim.getContentAsString(),
				Pagination.class);
		assertEquals(1, dataFilterSim.getData().size());
		assertEquals(device3.getId(), dataFilterSim.getData().get(0).get("id"));

		MockHttpServletResponse resultFilterImei = this.mvc
				.perform(get("/serviceplatform/device?imei=test-device5-imei")
						.headers(getHeaders("imei=test-device5-imei")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterImei = getObject(resultFilterImei.getContentAsString(),
				Pagination.class);
		assertEquals(1, dataFilterImei.getData().size());
		assertEquals(device5.getId(), dataFilterImei.getData().get(0).get("id"));

		MockHttpServletResponse resultFilterState = this.mvc
				.perform(get("/serviceplatform/device?state=2").headers(getHeaders("state=2")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterState = getObject(resultFilterState.getContentAsString(),
				Pagination.class);
		assertEquals(1, dataFilterState.getData().size());
		assertEquals(device6.getId(), dataFilterState.getData().get(0).get("id"));

		Long now = System.currentTimeMillis();
		MockHttpServletResponse resultFilterRegisterTime = this.mvc.perform(get(
				"/serviceplatform/device?registrationFrom=" + calTwoDayBef.getTimeInMillis() + "&registrationTo=" + now)
				.headers(getHeaders("registrationFrom=" + calTwoDayBef.getTimeInMillis() + "&registrationTo=" + now)))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterRegisterTime = getObject(
				resultFilterRegisterTime.getContentAsString(), Pagination.class);
		assertEquals(1, dataFilterRegisterTime.getData().size());
		assertEquals(device8.getId(), dataFilterRegisterTime.getData().get(0).get("id"));

		MockHttpServletResponse resultSort = this.mvc
				.perform(get("/serviceplatform/device?sort=state&ordering=desc")
						.headers(getHeaders("sort=state&ordering=desc")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataSort = getObject(resultSort.getContentAsString(),
				Pagination.class);
		assertEquals(8, dataSort.getData().size());
		assertEquals(device6.getId(), dataSort.getData().get(0).get("id"));
		assertEquals(device.getId(), dataSort.getData().get(1).get("id"));
		assertEquals(device2.getId(), dataSort.getData().get(2).get("id"));
		assertEquals(device3.getId(), dataSort.getData().get(3).get("id"));
		assertEquals(device4.getId(), dataSort.getData().get(4).get("id"));
		assertEquals(device5.getId(), dataSort.getData().get(5).get("id"));
		assertEquals(device7.getId(), dataSort.getData().get(6).get("id"));
		assertEquals(device8.getId(), dataSort.getData().get(7).get("id"));

		MockHttpServletResponse resultSortModel = this.mvc
				.perform(get("/serviceplatform/device?sort=model&ordering=desc")
						.headers(getHeaders("sort=model&ordering=desc")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataSortModel = getObject(resultSortModel.getContentAsString(),
				Pagination.class);
		assertEquals(8, dataSortModel.getData().size());
		assertEquals(device5.getId(), dataSortModel.getData().get(0).get("id"));
		assertEquals(device6.getId(), dataSortModel.getData().get(1).get("id"));
		assertEquals(device7.getId(), dataSortModel.getData().get(2).get("id"));
		assertEquals(device8.getId(), dataSortModel.getData().get(3).get("id"));
		assertEquals(device.getId(), dataSortModel.getData().get(4).get("id"));
		assertEquals(device2.getId(), dataSortModel.getData().get(5).get("id"));
		assertEquals(device3.getId(), dataSortModel.getData().get(6).get("id"));
		assertEquals(device4.getId(), dataSortModel.getData().get(7).get("id"));

		MockHttpServletResponse resultPagginate = this.mvc
				.perform(get("/serviceplatform/device?page=0&pageSize=3").headers(getHeaders("page=0&pageSize=3")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataPagginate = getObject(resultPagginate.getContentAsString(),
				Pagination.class);
		assertEquals(3, dataPagginate.getData().size());
		assertEquals(device.getId(), dataPagginate.getData().get(0).get("id"));
		assertEquals(device2.getId(), dataPagginate.getData().get(1).get("id"));
		assertEquals(device3.getId(), dataPagginate.getData().get(2).get("id"));
	}

	@Test
	public void testGetListSoftware() throws Exception {

		ServicePlatform sp = testGetTestServicePlatform();
		Set<DeviceModel> model = Set.of(PreConditionUtil.addDeviceModel(entityManager, "dm1"));
		Set<DeviceModel> model2 = Set.of(PreConditionUtil.addDeviceModel(entityManager, "dm2"));

		Software software = PreConditionUtil.addSoftware(entityManager, sp, model, "test-software1");
		Software software2 = PreConditionUtil.addSoftware(entityManager, sp, model, "test-software2");
		assertEquals("test-software2-packageId", software2.getPackageName());
		Software software3 = PreConditionUtil.addSoftware(entityManager, sp, model, "test-software3");
		Software software4 = PreConditionUtil.addSoftware(entityManager, sp, model2, "test-software4");
		assertEquals("test-software4-desc", software4.getDescription());
		Software software5 = PreConditionUtil.addSoftware(entityManager, sp, model2, "test-software5");
		Software software6 = PreConditionUtil.addSoftware(entityManager, sp, model2, "test-software6");

		UpdateMode um = entityManager.find(UpdateMode.class, 1);
		UpdateMode um2 = entityManager.find(UpdateMode.class, 2);
		PreConditionUtil.addSoftwareVersion(entityManager, software, "test-softwareversion11", "1.0.0", um);
		PreConditionUtil.addSoftwareVersion(entityManager, software, "test-softwareversion12", "2.0.0", um2);
		PreConditionUtil.addSoftwareVersion(entityManager, software2, "test-softwareversion21", "1.0.0", um2);
		PreConditionUtil.addSoftwareVersion(entityManager, software2, "test-softwareversion22", "2.0.0", um);

		MockHttpServletResponse resultFilterId = this.mvc
				.perform(get("/serviceplatform/software?id=" + software6.getId())
						.headers(getHeaders("id=" + software6.getId())))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterId = getObject(resultFilterId.getContentAsString(),
				Pagination.class);
		assertEquals(1, dataFilterId.getData().size());
		assertEquals(software6.getId(), Long.valueOf((Integer) dataFilterId.getData().get(0).get("id")));

		MockHttpServletResponse resultFilterPackageName = this.mvc
				.perform(get("/serviceplatform/software?packageName=test-software2-packageId")
						.headers(getHeaders("packageName=test-software2-packageId")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterPackageName = getObject(
				resultFilterPackageName.getContentAsString(), Pagination.class);
		assertEquals(1, dataFilterPackageName.getData().size());
		assertEquals(software2.getId(), Long.valueOf((Integer) dataFilterPackageName.getData().get(0).get("id")));

		MockHttpServletResponse resultFilterName = this.mvc
				.perform(
						get("/serviceplatform/software?name=test-software3").headers(getHeaders("name=test-software3")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterName = getObject(resultFilterName.getContentAsString(),
				Pagination.class);
		assertEquals(1, dataFilterName.getData().size());
		assertEquals(software3.getId(), Long.valueOf((Integer) dataFilterName.getData().get(0).get("id")));

		MockHttpServletResponse resultFilterDesc = this.mvc
				.perform(get("/serviceplatform/software?description=test-software4-desc")
						.headers(getHeaders("description=test-software4-desc")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterDesc = getObject(resultFilterDesc.getContentAsString(),
				Pagination.class);
		assertEquals(1, dataFilterDesc.getData().size());
		assertEquals(software4.getId(), Long.valueOf((Integer) dataFilterDesc.getData().get(0).get("id")));

		MockHttpServletResponse resultFilterDeviceModel = this.mvc
				.perform(get("/serviceplatform/software?deviceModels=dm1").headers(getHeaders("deviceModels=dm1")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterDeviceModel = getObject(
				resultFilterDeviceModel.getContentAsString(), Pagination.class);
		assertEquals(3, dataFilterDeviceModel.getData().size());
		assertEquals(software.getId(), Long.valueOf((Integer) dataFilterDeviceModel.getData().get(0).get("id")));
		assertEquals(software2.getId(), Long.valueOf((Integer) dataFilterDeviceModel.getData().get(1).get("id")));
		assertEquals(software3.getId(), Long.valueOf((Integer) dataFilterDeviceModel.getData().get(2).get("id")));

		MockHttpServletResponse resultSort = this.mvc
				.perform(get("/serviceplatform/software?sort=deviceModels&ordering=desc")
						.headers(getHeaders("sort=deviceModels&ordering=desc")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataSort = getObject(resultSort.getContentAsString(),
				Pagination.class);
		assertEquals(6, dataSort.getData().size());
		assertEquals(software4.getId(), Long.valueOf((Integer) dataSort.getData().get(0).get("id")));
		assertEquals(software5.getId(), Long.valueOf((Integer) dataSort.getData().get(1).get("id")));
		assertEquals(software6.getId(), Long.valueOf((Integer) dataSort.getData().get(2).get("id")));
		assertEquals(software.getId(), Long.valueOf((Integer) dataSort.getData().get(3).get("id")));
		assertEquals(software2.getId(), Long.valueOf((Integer) dataSort.getData().get(4).get("id")));
		assertEquals(software3.getId(), Long.valueOf((Integer) dataSort.getData().get(5).get("id")));

		MockHttpServletResponse resultPagginate = this.mvc
				.perform(get("/serviceplatform/software?page=0&pageSize=3")
						.headers(getHeaders("page=0&pageSize=3", true)))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataPagginate = getObject(resultPagginate.getContentAsString(),
				Pagination.class);
		assertEquals(3, dataPagginate.getData().size());
		assertEquals(software.getId(), Long.valueOf((Integer) dataPagginate.getData().get(0).get("id")));
		assertEquals(software2.getId(), Long.valueOf((Integer) dataPagginate.getData().get(1).get("id")));
		assertEquals(software3.getId(), Long.valueOf((Integer) dataPagginate.getData().get(2).get("id")));
	}

	@Test
	public void testGetListSoftwarePackage() throws Exception {
		String userId = "user";

		ServicePlatform sp = testGetTestServicePlatform();
		ServicePlatform spOther = PreConditionUtil.addServicePlatform(entityManager, "test");
		DeviceModel model = PreConditionUtil.addDeviceModel(entityManager, "dm1");
		Set<DeviceModel> models = Set.of(model);
		Device device = PreConditionUtil.addDevice(entityManager, model, sp, "test-device");
		Device device2 = PreConditionUtil.addDevice(entityManager, model, sp, "test-device2");
		Software software = PreConditionUtil.addSoftware(entityManager, sp, models, "test-software");
		Software software2 = PreConditionUtil.addSoftware(entityManager, sp, models, "test-software2");
		UpdateMode um = entityManager.find(UpdateMode.class, 1);
		UpdateMode um2 = entityManager.find(UpdateMode.class, 2);
		SoftwareVersion sv11 = PreConditionUtil.addSoftwareVersion(entityManager, software, "test-softwareversion11",
				"v1.0.0", um);
		SoftwareVersion sv12 = PreConditionUtil.addSoftwareVersion(entityManager, software, "test-softwareversion12",
				"v2.0.0", um2);
		SoftwareVersion sv21 = PreConditionUtil.addSoftwareVersion(entityManager, software2, "test-softwareversion21",
				"v1.0.0", um2);
		SoftwareVersion sv22 = PreConditionUtil.addSoftwareVersion(entityManager, software2, "test-softwareversion22",
				"v2.0.0", um);

		Device deviceOther = PreConditionUtil.addDevice(entityManager, model, spOther, "test-device-other");
		Software softwareOther = PreConditionUtil.addSoftware(entityManager, spOther, models, "test-software-other");
		SoftwareVersion svOther = PreConditionUtil.addSoftwareVersion(entityManager, softwareOther,
				"test-softwareversion-other", "1.0.0", um);

		SoftwarePackages sp1 = PreConditionUtil.addSoftwarePackages(entityManager, device, software, sv11, 1, userId,
				um.getModeId());
		SoftwarePackages sp2 = PreConditionUtil.addSoftwarePackages(entityManager, device, software2, sv21, 1, userId,
				um2.getModeId());
		SoftwarePackages sp3 = PreConditionUtil.addSoftwarePackages(entityManager, device2, software, sv12, 1, userId,
				um2.getModeId());
		SoftwarePackages sp4 = PreConditionUtil.addSoftwarePackages(entityManager, device2, software2, sv22, 1, userId,
				um.getModeId());
		SoftwarePackages sp5 = PreConditionUtil.addSoftwarePackages(entityManager, device, software2, sv21, 2, userId,
				um.getModeId());
		SoftwarePackages sp6 = PreConditionUtil.addSoftwarePackages(entityManager, device2, software, sv11, 2, userId,
				um2.getModeId());

		PreConditionUtil.addSoftwarePackages(entityManager, deviceOther, softwareOther, svOther, 1, userId,
				um.getModeId());

		MockHttpServletResponse resultFilterDeviceUId = this.mvc
				.perform(get("/serviceplatform/software/packages?deviceUId=" + device.getId())
						.headers(getHeaders("deviceUId=" + device.getId())))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterDeviceUId = getObject(
				resultFilterDeviceUId.getContentAsString(), Pagination.class);
		assertEquals(3, dataFilterDeviceUId.getData().size());
		assertEquals(sp1.getId(), Long.valueOf((Integer) dataFilterDeviceUId.getData().get(0).get("id")));
		assertEquals(sp2.getId(), Long.valueOf((Integer) dataFilterDeviceUId.getData().get(1).get("id")));
		assertEquals(sp5.getId(), Long.valueOf((Integer) dataFilterDeviceUId.getData().get(2).get("id")));

		MockHttpServletResponse resultFilterSoftwareId = this.mvc
				.perform(get("/serviceplatform/software/packages?softwareId=" + software.getId())
						.headers(getHeaders("softwareId=" + software.getId())))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterSoftwareId = getObject(
				resultFilterSoftwareId.getContentAsString(), Pagination.class);
		assertEquals(3, dataFilterSoftwareId.getData().size());
		assertEquals(sp1.getId(), Long.valueOf((Integer) dataFilterSoftwareId.getData().get(0).get("id")));
		assertEquals(sp3.getId(), Long.valueOf((Integer) dataFilterSoftwareId.getData().get(1).get("id")));
		assertEquals(sp6.getId(), Long.valueOf((Integer) dataFilterSoftwareId.getData().get(2).get("id")));

		MockHttpServletResponse resultFilterSoftwareName = this.mvc
				.perform(get("/serviceplatform/software/packages?softwareName=test-software2")
						.headers(getHeaders("softwareName=test-software2")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterSoftwareName = getObject(
				resultFilterSoftwareName.getContentAsString(), Pagination.class);
		assertEquals(3, dataFilterSoftwareName.getData().size());
		assertEquals(sp2.getId(), Long.valueOf((Integer) dataFilterSoftwareName.getData().get(0).get("id")));
		assertEquals(sp4.getId(), Long.valueOf((Integer) dataFilterSoftwareName.getData().get(1).get("id")));
		assertEquals(sp5.getId(), Long.valueOf((Integer) dataFilterSoftwareName.getData().get(2).get("id")));

		MockHttpServletResponse resultFilterSoftwareVersion = this.mvc
				.perform(get("/serviceplatform/software/packages?softwareVersion=v2.0.0")
						.headers(getHeaders("softwareVersion=v2.0.0")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterSoftwareVersion = getObject(
				resultFilterSoftwareVersion.getContentAsString(), Pagination.class);
		assertEquals(2, dataFilterSoftwareVersion.getData().size());
		assertEquals(sp3.getId(), Long.valueOf((Integer) dataFilterSoftwareVersion.getData().get(0).get("id")));
		assertEquals(sp4.getId(), Long.valueOf((Integer) dataFilterSoftwareVersion.getData().get(1).get("id")));

		MockHttpServletResponse resultFilterState = this.mvc
				.perform(get("/serviceplatform/software/packages?state=2").headers(getHeaders("state=2")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterState = getObject(resultFilterState.getContentAsString(),
				Pagination.class);
		assertEquals(2, dataFilterState.getData().size());
		assertEquals(sp5.getId(), Long.valueOf((Integer) dataFilterState.getData().get(0).get("id")));
		assertEquals(sp6.getId(), Long.valueOf((Integer) dataFilterState.getData().get(1).get("id")));

		MockHttpServletResponse resultFilterUpdateMode = this.mvc
				.perform(get("/serviceplatform/software/packages?updateMode=" + um2.getModeId())
						.headers(getHeaders("updateMode=" + um2.getModeId())))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataFilterUpdateMode = getObject(
				resultFilterUpdateMode.getContentAsString(), Pagination.class);
		assertEquals(3, dataFilterUpdateMode.getData().size());
		assertEquals(sp2.getId(), Long.valueOf((Integer) dataFilterUpdateMode.getData().get(0).get("id")));
		assertEquals(sp3.getId(), Long.valueOf((Integer) dataFilterUpdateMode.getData().get(1).get("id")));
		assertEquals(sp6.getId(), Long.valueOf((Integer) dataFilterUpdateMode.getData().get(2).get("id")));

		MockHttpServletResponse resultSort = this.mvc
				.perform(get("/serviceplatform/software/packages?sort=softwareName&ordering=desc")
						.headers(getHeaders("sort=softwareName&ordering=desc")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataSort = getObject(resultSort.getContentAsString(),
				Pagination.class);
		assertEquals(6, dataSort.getData().size());
		assertEquals(sp2.getId(), Long.valueOf((Integer) dataSort.getData().get(0).get("id")));
		assertEquals(sp4.getId(), Long.valueOf((Integer) dataSort.getData().get(1).get("id")));
		assertEquals(sp5.getId(), Long.valueOf((Integer) dataSort.getData().get(2).get("id")));
		assertEquals(sp1.getId(), Long.valueOf((Integer) dataSort.getData().get(3).get("id")));
		assertEquals(sp3.getId(), Long.valueOf((Integer) dataSort.getData().get(4).get("id")));
		assertEquals(sp6.getId(), Long.valueOf((Integer) dataSort.getData().get(5).get("id")));

		MockHttpServletResponse resultPagginate = this.mvc.perform(
				get("/serviceplatform/software/packages?page=0&pageSize=3").headers(getHeaders("page=0&pageSize=3")))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Pagination<LinkedHashMap<String, Object>> dataPagginate = getObject(resultPagginate.getContentAsString(),
				Pagination.class);
		assertEquals(3, dataPagginate.getData().size());
		assertEquals(sp1.getId(), Long.valueOf((Integer) dataPagginate.getData().get(0).get("id")));
		assertEquals(sp2.getId(), Long.valueOf((Integer) dataPagginate.getData().get(1).get("id")));
		assertEquals(sp3.getId(), Long.valueOf((Integer) dataPagginate.getData().get(2).get("id")));

	}

}
