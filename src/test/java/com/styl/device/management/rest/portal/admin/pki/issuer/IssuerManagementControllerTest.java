package com.styl.device.management.rest.portal.admin.pki.issuer;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.exception.advice.ExceptionAdvice;
import com.styl.device.management.pki.PkiService;

@SpringBootTest
@ActiveProfiles("test")
public class IssuerManagementControllerTest {

	private MockMvc mockMvc;

	@Mock
	private PkiService pkiService;

	@InjectMocks
	IssuerManagementController issuerManagementController;

	private final String END_POINT = "/api/management/pki/authority/{caName}/issuer/";

	@BeforeEach
	public void setUp() {
		this.mockMvc = MockMvcBuilders.standaloneSetup(issuerManagementController)
				.setControllerAdvice(ExceptionAdvice.class).build();
	}

	@Test
	public void givenValidParams_whenCallApiUploadIssuer_thenReceiveStatusCode200() throws Exception {
		// given
		String caName = "valid_caName";
		String issuerId = "valid_issuerId";
		// when
		doNothing().when(pkiService).uploadIssuer(anyString(), anyString());

		// then
		RequestBuilder requestBuilder = MockMvcRequestBuilders.post(END_POINT + "{issuerId}/manual", caName, issuerId)
				.contentType(MediaType.APPLICATION_JSON);
		mockMvc.perform(requestBuilder).andExpect(status().isOk()).andDo(print());
	}

	@Test
	public void givenInalidCaNameAndIssuerId_whenCallApiUploadIssuer_thenReceiveStatusCode400() throws Exception {
		// given
		String caName = "invalid_caName";
		String issuerId = "invalid_issuerId";

		// when
		doThrow(new ServiceException(ErrorCode.PKI_ISSUER_NOT_FOUND)).when(pkiService).uploadIssuer(anyString(),
				anyString());

		// then
		RequestBuilder requestBuilder = MockMvcRequestBuilders.post(END_POINT + "{issuerId}/manual", caName, issuerId)
				.contentType(MediaType.APPLICATION_JSON);
		mockMvc.perform(requestBuilder).andExpect(status().isBadRequest()).andExpect(MockMvcResultMatchers
				.jsonPath("errors[0].errorCode").value(ErrorCode.PKI_ISSUER_NOT_FOUND.getErrorCode())).andDo(print());

	}

	@Test
	public void givenValidParams_whenCallApiRemoveIssuer_thenReceiveStatusCode200() throws Exception {
		// given
		String caName = "valid_caName";
		String issuerId = "valid_issuerId";
		// when
		doNothing().when(pkiService).removeIssuer(anyString(), anyString());

		// then
		RequestBuilder requestBuilder = MockMvcRequestBuilders.delete(END_POINT + "{issuerId}/manual", caName, issuerId)
				.contentType(MediaType.APPLICATION_JSON);
		mockMvc.perform(requestBuilder).andExpect(status().isOk()).andDo(print());

	}

	@Test
	public void givenInalidCaNameAndIssuerId_whenCallApiRemoveIssuer_thenReceiveStatusCode400() throws Exception {
		// given
		String caName = "invalid_caName";
		String issuerId = "invalid_issuerId";

		// when
		doThrow(new ServiceException(ErrorCode.PKI_ISSUER_NOT_FOUND)).when(pkiService).removeIssuer(anyString(),
				anyString());

		// then
		RequestBuilder requestBuilder = MockMvcRequestBuilders.delete(END_POINT + "{issuerId}/manual", caName, issuerId)
				.contentType(MediaType.APPLICATION_JSON);
		mockMvc.perform(requestBuilder).andExpect(status().isBadRequest()).andExpect(MockMvcResultMatchers
				.jsonPath("errors[0].errorCode").value(ErrorCode.PKI_ISSUER_NOT_FOUND.getErrorCode())).andDo(print());
	}
}
