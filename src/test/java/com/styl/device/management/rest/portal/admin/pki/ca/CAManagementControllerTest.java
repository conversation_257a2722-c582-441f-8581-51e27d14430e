package com.styl.device.management.rest.portal.admin.pki.ca;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.styl.device.management.config.aws.s3.AwsS3StorageService;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.exception.advice.ExceptionAdvice;
import com.styl.device.management.pki.PkiService;
import com.styl.device.management.utils.Utils;

@SpringBootTest()
@ActiveProfiles("test")
public class CAManagementControllerTest {

	private MockMvc mockMvc;

	@Mock
	PkiService pkiService;

	@Mock
	AwsS3StorageService awsS3StorageService;

	@InjectMocks
	CAManagementController caManagementController;

	private final String END_POINT = "/api/management/pki/authority/{caName}/truststore/";

	@BeforeEach
	public void setUp() {
		this.mockMvc = MockMvcBuilders.standaloneSetup(caManagementController)
				.setControllerAdvice(ExceptionAdvice.class).build();
	}

	@Test
	public void givenValidTestConnectionRequest_whenCallApiTestS3Connection_thenGotStatusCode200() throws Exception {
		// given
		String caName = "caName";
		TestS3ConnectionRequest request = new TestS3ConnectionRequest();
		request.setBucket("bucket");
		request.setRegion("region");
		request.setUseAccessKey(false);

		// when
		doNothing().when(awsS3StorageService).testConnection(any());

		// then
		mockMvc.perform(post(END_POINT + "testConnection", caName).contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(request))).andExpect(status().isOk());

		verify(awsS3StorageService, atLeastOnce()).testConnection(any());
	}

	@Test
	public void givenInvalidBucket_whenCallApiTestS3Connection_thenGotTruststoreBucketNotFoundError() throws Exception {
		// given
		String caName = "caName";
		TestS3ConnectionRequest request = new TestS3ConnectionRequest();
		request.setBucket("invalidBucket");
		request.setRegion("region");
		request.setUseAccessKey(false);

		// when
		doThrow(new ServiceException(ErrorCode.PKI_TRUSTSTORE_BUCKET_NOT_FOUND)).when(awsS3StorageService)
				.testConnection(any());

		// then
		mockMvc.perform(post(END_POINT + "testConnection", caName).contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(request))).andExpect(status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("errors[0].errorCode")
						.value(ErrorCode.PKI_TRUSTSTORE_BUCKET_NOT_FOUND.getErrorCode()))
				.andDo(print());

		verify(awsS3StorageService, atLeastOnce()).testConnection(any());
	}

	@Test
	public void givenInvalidCredentials_whenCallApiTestS3Connection_thenGotTruststoreCredentialsInvalidError()
			throws Exception {
		// given
		String caName = "caName";
		TestS3ConnectionRequest request = new TestS3ConnectionRequest();
		request.setBucket("bucket");
		request.setRegion("region");
		request.setUseAccessKey(true);
		request.setAccessKeyId("invalidAccessKeyId");
		request.setSecretAccesKey("invalidSecretAccessKey");

		// when
		doThrow(new ServiceException(ErrorCode.PKI_TRUSTSTORE_CREDENTIALS_INVALID)).when(awsS3StorageService)
				.testConnection(any());

		// then
		mockMvc.perform(post(END_POINT + "testConnection", caName).contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(request))).andExpect(status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("errors[0].errorCode")
						.value(ErrorCode.PKI_TRUSTSTORE_CREDENTIALS_INVALID.getErrorCode()))
				.andDo(print());

		verify(awsS3StorageService, atLeastOnce()).testConnection(any());
	}

	@Test
	public void givenBucketAndWrongRegion_whenCallApiTestS3Connection_thenGotErrorGeneralError() throws Exception {
		// given
		String caName = "caName";
		TestS3ConnectionRequest request = new TestS3ConnectionRequest();
		request.setBucket("bucket");
		request.setRegion("wrongRegion");
		request.setUseAccessKey(false);

		// when
		doThrow(new ServiceException(ErrorCode.ERROR_GENERAL)).when(awsS3StorageService).testConnection(any());

		// then
		mockMvc.perform(post(END_POINT + "testConnection", caName).contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(request))).andExpect(status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("errors[0].errorCode")
						.value(ErrorCode.ERROR_GENERAL.getErrorCode()))
				.andDo(print());

		verify(awsS3StorageService, atLeastOnce()).testConnection(any());
	}

	@Test
	public void cannotConnectToTruststore_whenCallApiTestS3Connection_thenGotTruststoreConnectionError()
			throws Exception {
		// given
		String caName = "caName";
		TestS3ConnectionRequest request = new TestS3ConnectionRequest();
		request.setBucket("bucket");
		request.setRegion("region");
		request.setUseAccessKey(false);

		// when
		doThrow(new ServiceException(ErrorCode.PKI_TRUSTSTORE_CONNECTION_ERROR)).when(awsS3StorageService)
				.testConnection(any());

		// then
		mockMvc.perform(post(END_POINT + "testConnection", caName).contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(request))).andExpect(status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("errors[0].errorCode")
						.value(ErrorCode.PKI_TRUSTSTORE_CONNECTION_ERROR.getErrorCode()))
				.andDo(print());

		verify(awsS3StorageService, atLeastOnce()).testConnection(any());
	}

	@Test
	public void givenValidTruststoreConfig_whenCallApiSaveTruststoreConfig_thenSaveTruststoreConfigSuccessfully()
			throws Exception {
		String caName = "caName";
		String bucket = "bucket";
		String region = "region";
		TruststoreConfiguration config = new TruststoreConfiguration();
		config.setAutoSync(true);
		config.setBucket(bucket);
		config.setRegion(region);
		config.setUseAccessKey(false);

		when(pkiService.saveTruststoreConfiguration(anyString(), any())).thenReturn(config);

		RequestBuilder requestBuilder = MockMvcRequestBuilders.post(END_POINT + "config", caName)
				.contentType(MediaType.APPLICATION_JSON).content(Utils.mapToJson(
						new TruststoreConfiguration(true, "bucket", "region", true, "accessKey", "secretKey")));

		mockMvc.perform(requestBuilder).andExpect(status().isOk()).andDo(print());

		verify(pkiService, atLeastOnce()).saveTruststoreConfiguration(anyString(), any());
	}

	@Test
	public void givenInvalidCaName_whenCallApiSaveTruststoreConfig_thenGotErrorCaNotFound() throws Exception {
		String caName = "invalidCaName";
		String bucket = "bucket";
		String region = "region";
		TruststoreConfiguration config = new TruststoreConfiguration();
		config.setAutoSync(true);
		config.setBucket(bucket);
		config.setRegion(region);
		config.setUseAccessKey(false);

		when(pkiService.saveTruststoreConfiguration(anyString(), any()))
				.thenThrow(new ServiceException(ErrorCode.PKI_CA_NOT_FOUND));

		RequestBuilder requestBuilder = MockMvcRequestBuilders.post(END_POINT + "config", caName)
				.contentType(MediaType.APPLICATION_JSON).content(Utils.mapToJson(config));

		mockMvc.perform(requestBuilder).andExpect(status().isBadRequest())
				.andExpect(jsonPath("errors[0].errorCode").value(ErrorCode.PKI_CA_NOT_FOUND.getErrorCode()))
				.andDo(print());

		verify(pkiService, atLeastOnce()).saveTruststoreConfiguration(anyString(), any());
	}

	@Test
	public void givenValidCaName_whenGetTruststoreConfig_thenGetTruststoreConfigSuccessfully() throws Exception {
		String caName = "caName";
		when(pkiService.findTruststoreConfigurationByCaName(anyString()))
				.thenReturn(new TruststoreConfiguration(true, "bucket", "region", true, "accessKey", "secretKey"));

		RequestBuilder requestBuilder = MockMvcRequestBuilders.get(END_POINT + "config", caName);
		mockMvc.perform(requestBuilder).andExpect(status().isOk());

		verify(pkiService, atLeastOnce()).findTruststoreConfigurationByCaName(anyString());
	}

	@Test
	public void givenInvalidCaName_whenGetTruststoreConfig_thenGetDefaultTruststoreConfig() throws Exception {
		String caName = "invalidCaName";

		when(pkiService.findTruststoreConfigurationByCaName(anyString()))
				.thenReturn(new TruststoreConfiguration(false, null, null, null, null, null));

		RequestBuilder requestBuilder = MockMvcRequestBuilders.get(END_POINT + "config", caName);
		mockMvc.perform(requestBuilder).andExpect(status().isOk())
				.andExpectAll(jsonPath("$.autoSync").value(false), jsonPath("$.bucket").doesNotExist(),
						jsonPath("$.region").doesNotExist(), jsonPath("$.useAccessKey").doesNotExist(),
						jsonPath("$.accessKeyId").doesNotExist(), jsonPath("$.secretAccessKey").doesNotExist())
				.andDo(print());

		verify(pkiService, atLeastOnce()).findTruststoreConfigurationByCaName(anyString());
	}
}
