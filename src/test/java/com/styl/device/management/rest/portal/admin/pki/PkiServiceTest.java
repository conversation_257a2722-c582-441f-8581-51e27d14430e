package com.styl.device.management.rest.portal.admin.pki;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.only;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.pki.certificate.authority.CertificateAuthority;
import com.styl.device.management.persistence.pki.certificate.authority.CertificateAuthorityRepository;
import com.styl.device.management.persistence.pki.certificate.issuer.CertificateIssuer;
import com.styl.device.management.persistence.pki.certificate.issuer.CertificateIssuerRepository;
import com.styl.device.management.pki.PkiServiceImpl;
import com.styl.device.management.pki.PkiTruststoreService;
import com.styl.device.management.rest.portal.admin.pki.ca.TruststoreConfiguration;
import com.styl.device.management.service.vault.pki.VaultPkiService;

@ExtendWith(MockitoExtension.class)
@SpringBootTest
@ActiveProfiles("test")
public class PkiServiceTest {

	@Mock
	CertificateIssuerRepository certificateIssuerRepository;

	@Mock
	PkiTruststoreService pkiTruststoreService;

	@Mock
	VaultPkiService vaultPkiService;

	@Mock
	CertificateAuthorityRepository certificateAuthorityRepository;

	@InjectMocks
	PkiServiceImpl pkiService;

	@Test
	public void givenValidCaNameAndIssuerId_whenInvokedUploadIssuer_thenNothingHappend() {
		// given
		String caName = "valid_caName";
		String issuerId = "valid_issuerId";

		// when
		CertificateIssuer certificateIssuer = new CertificateIssuer();
		when(certificateIssuerRepository.findByCaNameAndIssuerId(anyString(), anyString()))
				.thenReturn(certificateIssuer);
		doNothing().when(pkiTruststoreService).uploadCertificateIssuerSync(anyString(), any());

		// then
		pkiService.uploadIssuer(caName, issuerId);
		verify(certificateIssuerRepository, only()).findByCaNameAndIssuerId(anyString(), anyString());
		verify(pkiTruststoreService, only()).uploadCertificateIssuerSync(anyString(), any());
	}

	@Test
	public void givenInValidCaNameAndIssuerId_whenInvokedUploadIssuer_thenGotIssuerNotFoundException() {
		// given
		String caName = "valid_caName";
		String issuerId = "valid_issuerId";

		// when
		CertificateIssuer certificateIssuer = new CertificateIssuer();
		when(certificateIssuerRepository.findByCaNameAndIssuerId(anyString(), anyString()))
				.thenReturn(certificateIssuer);
		doNothing().when(pkiTruststoreService).uploadCertificateIssuerSync(anyString(), any());

		// then
		pkiService.uploadIssuer(caName, issuerId);
		verify(certificateIssuerRepository, only()).findByCaNameAndIssuerId(anyString(), anyString());
		verify(pkiTruststoreService, only()).uploadCertificateIssuerSync(anyString(), any());
	}

	@Test
	public void givenValidCaNameAndIssuerId_whenInvokedRemoveIssuer_thenNothingHappend() {
		// given
		String caName = "valid_caName";
		String issuerId = "valid_issuerId";

		// when
		CertificateIssuer certificateIssuer = new CertificateIssuer();
		when(certificateIssuerRepository.findByCaNameAndIssuerId(anyString(), anyString()))
				.thenReturn(certificateIssuer);
		doNothing().when(pkiTruststoreService).deleteCertificateIssuerSync(anyString());

		// then
		pkiService.removeIssuer(caName, issuerId);
		verify(certificateIssuerRepository, only()).findByCaNameAndIssuerId(anyString(), anyString());
		verify(pkiTruststoreService, only()).deleteCertificateIssuerSync(anyString());
	}

	@Test
	public void givenValidCaNameAndIssuerId_whenInvokedRemoveIssuer_thenGotIssuerNotFoundException() {
		// given
		String caName = "valid_caName";
		String issuerId = "valid_issuerId";

		// when
		CertificateIssuer certificateIssuer = new CertificateIssuer();
		when(certificateIssuerRepository.findByCaNameAndIssuerId(anyString(), anyString()))
				.thenReturn(certificateIssuer);
		doNothing().when(pkiTruststoreService).deleteCertificateIssuerSync(anyString());

		// then
		pkiService.removeIssuer(caName, issuerId);
		verify(certificateIssuerRepository, only()).findByCaNameAndIssuerId(anyString(), anyString());
		verify(pkiTruststoreService, only()).deleteCertificateIssuerSync(anyString());
	}

	@Test
	public void givenValidCaNameAndTruststoreConfig_whenInvokedSaveTruststoreConfiguration_thenSaveTruststoreConfigSuccessfully() {
		// given
		String caName = "valid_caName";
		String bucket = "bucket";
		String region = "region";

		TruststoreConfiguration config = new TruststoreConfiguration();
		config.setAutoSync(true);
		config.setBucket(bucket);
		config.setRegion(region);
		config.setUseAccessKey(false);

		// when
		com.styl.device.management.service.vault.pki.VaultCertificateAuthority certificateAuthority = new com.styl.device.management.service.vault.pki.VaultCertificateAuthority(
				caName, "type");
		Collection<com.styl.device.management.service.vault.pki.VaultCertificateAuthority> certificateAuthorities = new ArrayList<>();
		certificateAuthorities.add(certificateAuthority);

		CertificateAuthority ca = new CertificateAuthority();

		when(vaultPkiService.getAuthorities()).thenReturn(certificateAuthorities);
		when(certificateAuthorityRepository.findByCaName(anyString())).thenReturn(Optional.of(ca));
		when(certificateAuthorityRepository.save(any())).thenReturn(ca);

		// then
		TruststoreConfiguration actualResult = pkiService.saveTruststoreConfiguration(caName, config);

		TruststoreConfiguration expectedResult = new TruststoreConfiguration(true, "bucket", "region", false, null,
				null);
		assertEquals(expectedResult.isAutoSync(), actualResult.isAutoSync());
		assertEquals(expectedResult.getBucket(), actualResult.getBucket());
		assertEquals(expectedResult.getRegion(), actualResult.getRegion());
		assertEquals(expectedResult.getUseAccessKey(), actualResult.getUseAccessKey());
		assertEquals(expectedResult.getAccessKeyId(), actualResult.getAccessKeyId());
		assertEquals(expectedResult.getSecretAccesKey(), actualResult.getSecretAccesKey());
	}

	@Test
	public void givenInvalidCaName_whenInvokedSaveTruststoreConfiguration_thenGotErrorCaNotFound() {
		// given
		String caName = "invalid_caName";
		String bucket = "bucket";
		String region = "region";

		TruststoreConfiguration config = new TruststoreConfiguration();
		config.setAutoSync(true);
		config.setBucket(bucket);
		config.setRegion(region);
		config.setUseAccessKey(false);

		// when
		Collection<com.styl.device.management.service.vault.pki.VaultCertificateAuthority> certificateAuthorities = new ArrayList<>();

		when(vaultPkiService.getAuthorities()).thenReturn(certificateAuthorities);

		ServiceException actualException = Assertions.assertThrows(ServiceException.class,
				() -> pkiService.saveTruststoreConfiguration(caName, config));
		assertEquals(new ServiceException(ErrorCode.PKI_CA_NOT_FOUND).getErrorCode(), actualException.getErrorCode());
	}

	@Test
	public void givenValidCaName_whenInvokedFindTruststoreConfigurationByCaName_thenGotTruststoreConfig() {
		// given
		String caName = "valid_caName";
		String bucket = "bucket";
		String region = "region";

		TruststoreConfiguration config = new TruststoreConfiguration();
		config.setAutoSync(true);
		config.setBucket(bucket);
		config.setRegion(region);
		config.setUseAccessKey(false);

		// when
		CertificateAuthority ca = new CertificateAuthority(caName, config);

		when(certificateAuthorityRepository.findByCaName(anyString())).thenReturn(Optional.of(ca));

		TruststoreConfiguration actualResult = pkiService.findTruststoreConfigurationByCaName(caName);
		TruststoreConfiguration expectedResult = new TruststoreConfiguration(true, "bucket", "region", false, null,
				null);
		assertEquals(expectedResult.isAutoSync(), actualResult.isAutoSync());
		assertEquals(expectedResult.getBucket(), actualResult.getBucket());
		assertEquals(expectedResult.getRegion(), actualResult.getRegion());
		assertEquals(expectedResult.getUseAccessKey(), actualResult.getUseAccessKey());
		assertEquals(expectedResult.getAccessKeyId(), actualResult.getAccessKeyId());
		assertEquals(expectedResult.getSecretAccesKey(), actualResult.getSecretAccesKey());
	}

	@Test
	public void givenInvalidCaName_whenInvokedFindTruststoreConfiguration_thenGotDefaultTruststoreConfig() {
		// given
		String caName = "invalid_caName";

		// when
		when(certificateAuthorityRepository.findByCaName(anyString())).thenReturn(Optional.ofNullable(null));

		// then
		TruststoreConfiguration actualResult = pkiService.findTruststoreConfigurationByCaName(caName);
		TruststoreConfiguration expectedResult = new TruststoreConfiguration(false, null, null, null, null, null);

		assertEquals(expectedResult.isAutoSync(), actualResult.isAutoSync());
		assertEquals(expectedResult.getBucket(), actualResult.getBucket());
		assertEquals(expectedResult.getRegion(), actualResult.getRegion());
		assertEquals(expectedResult.getUseAccessKey(), actualResult.getUseAccessKey());
		assertEquals(expectedResult.getAccessKeyId(), actualResult.getAccessKeyId());
		assertEquals(expectedResult.getSecretAccesKey(), actualResult.getSecretAccesKey());
	}
}
