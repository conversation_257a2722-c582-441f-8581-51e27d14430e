/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.software.version;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.TestContants;
import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.persistence.software.version.SoftwareVersionRepository;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class SoftwareVersionRepositoryTest {

	private static final Logger logger = LoggerFactory.getLogger(SoftwareVersionRepositoryTest.class);

	@Autowired
	private SoftwareVersionRepository sfvRepository;

	@Test
	public void testFindSoftwareByVersionAndPackageId() {
		Optional<SoftwareVersion> result = sfvRepository.findByVerionAndPackageName(TestContants.SOFTWARE_VERSION_0_1_7,
				TestContants.SOFTWARE_PACKAGE_NAME);
		logger.info("PackageId: {}; Version: {}", TestContants.SOFTWARE_PACKAGE_NAME,
				TestContants.SOFTWARE_VERSION_0_1_7);
		logger.info("Present {}", result.isPresent());
		assertEquals(true, result.isPresent());
		assertEquals(TestContants.SOFTWARE_VERSION_0_1_7, result.get().getVersion());
		assertEquals(TestContants.SOFTWARE_PACKAGE_NAME, result.get().getSoftware().getPackageName());
	}

	@Test
	public void testCountByPackageId() {
		Long countByPackage = sfvRepository.countByPackageName(TestContants.SOFTWARE_PACKAGE_NAME);
		assertEquals(10, countByPackage);
	}

	@Test
	public void testFindSoftwareVersionByPagination() {
		Pageable paging = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id"));
		Page<SoftwareVersion> result = sfvRepository.findByPackageIdAndVersionAndServicePlatform(null, "", null,
				paging);
		Long countAll = sfvRepository.count();
		assertEquals(countAll, result.getTotalElements());

	}

	@Test
	public void testFindSoftwareVersionByPaginationByPackageId() {
		Pageable paging = PageRequest.of(0, 2, Sort.by(Sort.Direction.DESC, "id"));
		Page<SoftwareVersion> result = sfvRepository
				.findByPackageIdAndVersionAndServicePlatform(TestContants.SOFTWARE_PACKAGE_NAME, "", null, paging);
		Long countByPackage = sfvRepository.countByPackageName(TestContants.SOFTWARE_PACKAGE_NAME);

		assertEquals(countByPackage, result.getTotalElements());

	}

	@Test
	public void testFindSoftwareVersionByPaginationByPackageIdAndServicePlatformId() {
		Pageable paging = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id"));
		Page<SoftwareVersion> result = sfvRepository
				.findByPackageIdAndVersionAndServicePlatform(TestContants.SOFTWARE_PACKAGE_NAME, "", 1, paging);
		assertEquals(10, result.getTotalElements());

	}
}
