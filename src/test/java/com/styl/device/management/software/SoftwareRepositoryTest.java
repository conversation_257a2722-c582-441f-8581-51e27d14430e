/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.software;

import static org.junit.jupiter.api.Assertions.assertEquals;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.SoftwareRepository;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class SoftwareRepositoryTest {

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private SoftwareRepository softwareRepository;

	@Test
	public void testListSoftwareFilterByPackageASC() {
		addDataTestList();
		Pageable paging = PageRequest.of(0, 10, Sort.by(Sort.Direction.ASC, "packageName"));
		Page<Software> listSoftware = softwareRepository.findList("com.package.", null, null, paging);
		for (int i = 0; i < 10; i++) {
			assertEquals("com.package." + i, listSoftware.getContent().get(i).getPackageName());
		}
	}

	@Test
	public void testListSoftwareFilterByPackageDESC() {
		addDataTestList();
		Pageable paging = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "packageName"));
		Page<Software> listSoftware = softwareRepository.findList("com.package.", null, null, paging);
		for (int i = 0; i < 10; i++) {
			assertEquals("com.package." + i, listSoftware.getContent().get(9 - i).getPackageName());
		}
	}

	@Test
	public void testListSoftwareFilterByPackage1ValueDESC() {
		addDataTestList();
		Pageable paging = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "packageName"));
		Page<Software> listSoftware = softwareRepository.findList("com.package.1", null, null, paging);
		assertEquals(1, listSoftware.getTotalElements());
		assertEquals("com.package.1", listSoftware.getContent().get(0).getPackageName());
	}

	@Test
	public void testListSoftwareFilterByNameASC() {
		addDataTestList();
		Pageable paging = PageRequest.of(0, 10, Sort.by(Sort.Direction.ASC, "name"));
		Page<Software> listSoftware = softwareRepository.findList(null, "Nam", null, paging);
		for (int i = 0; i < 10; i++) {
			assertEquals("Name " + i, listSoftware.getContent().get(i).getName());
		}
	}

	@Test
	public void testListSoftwareFilterByNameDESC() {
		addDataTestList();
		Pageable paging = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "name"));
		Page<Software> listSoftware = softwareRepository.findList(null, "Nam", null, paging);
		for (int i = 0; i < 10; i++) {
			assertEquals("Name " + i, listSoftware.getContent().get(9 - i).getName());
		}
	}

	@Test
	public void testListSoftwareFilterByName1Value() {
		addDataTestList();
		Pageable paging = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "name"));
		Page<Software> listSoftware = softwareRepository.findList(null, "Name 1", null, paging);
		assertEquals(1, listSoftware.getTotalElements());
		assertEquals("Name 1", listSoftware.getContent().get(0).getName());

	}

	private void addDataTestList() {
		for (int i = 0; i < 10; i++) {
			Software newSoftware = new Software();
			newSoftware.setName("Name " + i);
			newSoftware.setDescription("Description " + i);
			newSoftware.setPackageName("com.package." + i);
			entityManager.persist(newSoftware);
			entityManager.flush();
		}
	}

}
