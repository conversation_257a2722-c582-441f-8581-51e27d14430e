package com.styl.device.management.software.ota.history;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.styl.device.management.persistence.software.packages.SoftwarePackagesService;
import com.styl.device.management.rest.portal.admin.software.ota.history.OTAHistoryController;
import com.styl.device.management.rest.portal.admin.software.ota.history.OTAHistoryResponse;
import com.styl.device.management.rest.portal.admin.software.ota.history.ServicePlatformOtaResponse;
import com.styl.device.management.utils.Pagination;

@ExtendWith(MockitoExtension.class)
public class OTAHistoryControllerTest {

	@Autowired
	MockMvc mockMvc;

	@Mock
	SoftwarePackagesService softwarePackagesService;

	@InjectMocks
	OTAHistoryController otaHistoryController;

	private final String END_POINT = "/api/management/software/ota/history";

	List<OTAHistoryResponse> listData;

	@BeforeEach
	public void setUp() {
		this.mockMvc = MockMvcBuilders.standaloneSetup(otaHistoryController).build();

		OTAHistoryResponse otaHistoryResponse1 = new OTAHistoryResponse("device1", "model1",
				new ServicePlatformOtaResponse("ServicePlatform1", "SPF1", "serviceplatform1.com"), "package1", "0.0.1",
				"1.0.0", "aaa", 123l, "CANCELLED", "", null, new HashSet<String>(Arrays.asList("AAA")));
		OTAHistoryResponse otaHistoryResponse2 = new OTAHistoryResponse("device2", "model1",
				new ServicePlatformOtaResponse("ServicePlatform2", "SPF2", "serviceplatform2.com"), "package2", "0.0.1",
				"1.0.0", "bbb", 123l, "PENDING", "", null, new HashSet<String>(Arrays.asList("BBB")));
		OTAHistoryResponse otaHistoryResponse3 = new OTAHistoryResponse("device3", "model2",
				new ServicePlatformOtaResponse("ServicePlatform1", "SPF1", "serviceplatform1.com"), "package1", "0.0.1",
				"1.0.0", "aaa", 123l, "DOWNLOADING", "", null, new HashSet<String>(Arrays.asList("CCC")));

		listData = new ArrayList<OTAHistoryResponse>();
		listData.add(otaHistoryResponse1);
		listData.add(otaHistoryResponse2);
		listData.add(otaHistoryResponse3);
	}

	@Test
	public void callOtaHistoryApi_expectPaginationResponseData() throws Exception {
		// given
		Pagination<OTAHistoryResponse> paginationResponse = new Pagination<OTAHistoryResponse>(3, 0, 10, listData);

		// when
		when(softwarePackagesService.getOtaHistory(any(), any(), any(), any(), any(), any(), any(), any(), any(),
				anyInt(), anyInt(), anyString(), anyString())).thenReturn(paginationResponse);

		// then
		RequestBuilder requestBuilder = MockMvcRequestBuilders.get(END_POINT).contentType(MediaType.APPLICATION_JSON);
		mockMvc.perform(requestBuilder).andExpect(status().isOk()).andExpectAll(
				content().contentType(MediaType.APPLICATION_JSON), jsonPath("$.total").value(3),
				jsonPath("$.page").value(0), jsonPath("$.pageSize").value(10),
				jsonPath("$.data[0].deviceId").value("device1"), jsonPath("$.data[0].deviceModel").value("model1"),
				jsonPath("$.data[0].servicePlatform.name").value("ServicePlatform1"),
				jsonPath("$.data[0].servicePlatform.shortName").value("SPF1"),
				jsonPath("$.data[0].servicePlatform.url").value("serviceplatform1.com"),
				jsonPath("$.data[0].softwareName").value("package1"),
				jsonPath("$.data[0].originalVersion").value("0.0.1"),
				jsonPath("$.data[0].assignedVersion").value("1.0.0"), jsonPath("$.data[0].assignedBy").value("aaa"),
				jsonPath("$.data[0].assignedTime").value(123l), jsonPath("$.data[0].otaState").value("CANCELLED"),
				jsonPath("$.data[0].otaRemarks").value(""), jsonPath("$.data[0].lastUpdated").isEmpty(),
				jsonPath("$.data[0].tags[0]").value("AAA"),

				jsonPath("$.data[1].deviceId").value("device2"), jsonPath("$.data[1].deviceModel").value("model1"),
				jsonPath("$.data[1].servicePlatform.name").value("ServicePlatform2"),
				jsonPath("$.data[1].servicePlatform.shortName").value("SPF2"),
				jsonPath("$.data[1].servicePlatform.url").value("serviceplatform2.com"),
				jsonPath("$.data[1].softwareName").value("package2"),
				jsonPath("$.data[1].originalVersion").value("0.0.1"),
				jsonPath("$.data[1].assignedVersion").value("1.0.0"), jsonPath("$.data[1].assignedBy").value("bbb"),
				jsonPath("$.data[1].assignedTime").value(123l), jsonPath("$.data[1].otaState").value("PENDING"),
				jsonPath("$.data[1].otaRemarks").value(""), jsonPath("$.data[1].lastUpdated").isEmpty(),
				jsonPath("$.data[1].tags[0]").value("BBB"),

				jsonPath("$.data[2].deviceId").value("device3"), jsonPath("$.data[2].deviceModel").value("model2"),
				jsonPath("$.data[2].servicePlatform.name").value("ServicePlatform1"),
				jsonPath("$.data[2].servicePlatform.shortName").value("SPF1"),
				jsonPath("$.data[2].servicePlatform.url").value("serviceplatform1.com"),
				jsonPath("$.data[2].softwareName").value("package1"),
				jsonPath("$.data[2].originalVersion").value("0.0.1"),
				jsonPath("$.data[2].assignedVersion").value("1.0.0"), jsonPath("$.data[2].assignedBy").value("aaa"),
				jsonPath("$.data[2].assignedTime").value(123l), jsonPath("$.data[2].otaState").value("DOWNLOADING"),
				jsonPath("$.data[2].otaRemarks").value(""), jsonPath("$.data[2].lastUpdated").isEmpty(),
				jsonPath("$.data[2].tags[0]").value("CCC"))

				.andDo(print());
	}
}
