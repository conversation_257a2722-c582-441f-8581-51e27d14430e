/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.software.version.control;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.TestContants;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.DeviceService;
import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.device.model.DeviceModelService;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesService;
import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.persistence.software.version.SoftwareVersionService;
import com.styl.device.management.rest.portal.admin.device.DeviceAssignRequest;
import com.styl.device.management.rest.portal.admin.device.DevicePortalResponse;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class VersionControlServiceTest {

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private SoftwarePackagesService versionControlService;

	@Autowired
	private SoftwareVersionService softwareVersionService;

	@Autowired
	private DeviceModelService deviceModelService;

	@Autowired
	private DeviceService deviceService;

	@Test
	public void testAssignSoftwareToUnassignedDevice() {
		SoftwareVersion softwareVersion = softwareVersionService
				.findByVersionAndPackageId(TestContants.SOFTWARE_VERSION_0_1_7, TestContants.SOFTWARE_PACKAGE_NAME);
		List<String> listDeviceUids = new ArrayList<String>();
		listDeviceUids.add(TestContants.DEVICE_UID);
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			versionControlService.assignBySoftwareVersion(softwareVersion, listDeviceUids, "");
		});

		assertEquals(ErrorCode.DEVICE_NEED_ASSIGN_SP.getErrorCode(), exception.getErrorCode());
	}

	@Test
	public void testAssignSoftwareToAssignedDevice() {
		SoftwareVersion softwareVersion = softwareVersionService
				.findByVersionAndPackageId(TestContants.SOFTWARE_VERSION_0_1_7, TestContants.SOFTWARE_PACKAGE_NAME);
		List<String> listDeviceUids = new ArrayList<String>();
		listDeviceUids.add(TestContants.DEVICE_UID);

		Device device = entityManager.find(Device.class, TestContants.DEVICE_UID);
		assertNotNull(device);

		DeviceAssignRequest assignRequest = new DeviceAssignRequest();
		assignRequest.setDeviceUids(Arrays.asList(TestContants.DEVICE_UID));
		assignRequest.setServicePlatformId(softwareVersion.getServicePlatformId());
		deviceService.assignServicePlatform(assignRequest);

		versionControlService.assignBySoftwareVersion(softwareVersion, listDeviceUids, "");

		Set<DevicePortalResponse> listDeviceBySoftwareVersion = softwareVersionService
				.listDeviceBySoftwareVersion(softwareVersion.getId());
		assertEquals(1, listDeviceBySoftwareVersion.size());
		listDeviceBySoftwareVersion.forEach(i -> {
			assertTrue(i.getDeviceUid().contains(TestContants.DEVICE_UID));
		});
	}

	@Test
	public void testReassignSoftwareVersion() {
		Device device = addDevice(1);
		addAssignStateInstalled(device, TestContants.SOFTWARE_VERSION_0_1_7);
		addAssignStateInstalled(device, TestContants.SOFTWARE_VERSION_0_1_8);
		SoftwareVersion softwareVersion = softwareVersionService
				.findByVersionAndPackageId(TestContants.SOFTWARE_VERSION_0_1_7, TestContants.SOFTWARE_PACKAGE_NAME);
		List<String> listDeviceUids = new ArrayList<String>();
		listDeviceUids.add(device.getId());

		DeviceAssignRequest assignRequest = new DeviceAssignRequest();
		assignRequest.setDeviceUids(Arrays.asList(device.getId()));
		assignRequest.setServicePlatformId(softwareVersion.getServicePlatformId());
		deviceService.assignServicePlatform(assignRequest);

		versionControlService.assignBySoftwareVersion(softwareVersion, listDeviceUids, "");
		Set<DevicePortalResponse> listDeviceBySoftwareVersion = softwareVersionService
				.listDeviceBySoftwareVersion(softwareVersion.getId());
		listDeviceBySoftwareVersion.forEach(i -> {
			System.out.println(i.getDeviceUid());
		});
	}

	private void addAssignStateInstalled(Device device, String version) {
		SoftwareVersion softwareVersion = softwareVersionService.findByVersionAndPackageId(version,
				TestContants.SOFTWARE_PACKAGE_NAME);

		SoftwarePackages versionControl = new SoftwarePackages();
		versionControl.setDevice(device);
		versionControl.setSoftware(softwareVersion.getSoftware());
		versionControl.setSoftwareVersion(softwareVersion);
		versionControl.setState(SoftwarePackages.STATE_6_UPDATED);
		versionControl.setAssignedTime(System.currentTimeMillis());
		versionControl.setAssignedBy("");
		versionControl.setUpdateMode(softwareVersion.getUpdateMode().getModeId());
		entityManager.persist(versionControl);
		entityManager.flush();
	}

	private Device addDevice(int numberDevice) {
		DeviceModel model = deviceModelService.findByModel(TestContants.MODEL);
		Device device = new Device();
		device.setHardwareId("hardwareId-" + numberDevice);
		device.setCreatedBy("Dino");
		device.setCreatedTime(121131313L);
		device.setFirstRegistrationTime(121131313L);
		device.setImei("Imeiiiiiiiii");
		device.setSimId("12222222");
		device.setModel(model);
		device.setState(Device.ASSIGNED);
		entityManager.persist(device);
		entityManager.flush();
		return device;
	}
}
