/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.software.version.control;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.TestContants;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.SoftwareService;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesRepository;
import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.persistence.software.version.SoftwareVersionService;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class VersionControlRepositoryTest {

	@Autowired
	private SoftwarePackagesRepository versionControlRepository;

	@Autowired
	private SoftwareService softwareService;

	@Autowired
	private SoftwareVersionService softwareVersionService;

	@Test
	public void testCountBySoftware() {
		Software software = softwareService.findByPackageId(TestContants.SOFTWARE_PACKAGE_NAME);
		Long softwareNumbers = versionControlRepository.countBySoftware(software);
		assertEquals(0, softwareNumbers);
	}

	@Test
	public void testGetDeviceBySoftwareVersion() {
		SoftwareVersion softwareVersion = softwareVersionService
				.findByVersionAndPackageId(TestContants.SOFTWARE_VERSION_0_1_7, TestContants.SOFTWARE_PACKAGE_NAME);
		List<Device> listDevice = versionControlRepository.findBySoftwareVersion(softwareVersion.getId());
		assertEquals(true, listDevice.isEmpty());
	}

}
