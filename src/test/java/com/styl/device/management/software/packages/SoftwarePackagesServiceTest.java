package com.styl.device.management.software.packages;

import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Arrays;
import java.util.List;

import jakarta.persistence.EntityManager;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.DeviceRepository;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.SoftwareRepository;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesRepository;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesService;
import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.persistence.software.version.SoftwareVersionRepository;
import com.styl.device.management.rest.portal.admin.software.ota.history.OTAHistoryResponse;
import com.styl.device.management.utils.Pagination;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class SoftwarePackagesServiceTest {

	@Autowired
	EntityManager entityManager;

	@Autowired
	SoftwarePackagesRepository softwarePackagesRepository;

	@Autowired
	SoftwarePackagesService softwarePackagesService;

	@Autowired
	DeviceRepository deviceRepository;

	@Autowired
	SoftwareRepository softwareRepository;

	@Autowired
	SoftwareVersionRepository softwareVersionRepository;

	Page<SoftwarePackages> pageSoftwarePackages;

	@BeforeEach
	public void setUp() {
		Device device1 = deviceRepository.findById("T-0001").get();
		Device device2 = deviceRepository.findById("T-0002").get();

		Software software1 = softwareRepository.findByName("Caribbean-POS").get();
		Software software2 = softwareRepository.findByPackageName("test.remove-serviceplatform").get();

		SoftwareVersion softwareVersion1 = softwareVersionRepository.findById(1L).get();
		SoftwareVersion softwareVersion2 = softwareVersionRepository.findById(2L).get();

		SoftwarePackages softwarePackages1 = new SoftwarePackages(device1, software1, softwareVersion1, 1, 1);
		softwarePackages1.setAppVersion("0.0.1");
		softwarePackages1.setAssignedBy("aaa");
		softwarePackages1.setAssignedTime(123l);
		softwarePackages1.setUpdateMode(1);
		SoftwarePackages softwarePackages2 = new SoftwarePackages(device1, software1, softwareVersion1, 2, 1);
		softwarePackages2.setAppVersion("0.0.1");
		softwarePackages2.setAssignedBy("bbb");
		softwarePackages2.setAssignedTime(123l);
		softwarePackages2.setUpdateMode(1);
		SoftwarePackages softwarePackages3 = new SoftwarePackages(device2, software1, softwareVersion1, 2, 1);
		softwarePackages3.setAppVersion("0.0.1");
		softwarePackages3.setAssignedBy("aaa");
		softwarePackages3.setAssignedTime(123l);
		softwarePackages3.setUpdateMode(1);
		SoftwarePackages softwarePackages4 = new SoftwarePackages(device2, software2, softwareVersion2, 3, 1);
		softwarePackages4.setAppVersion("0.0.1");
		softwarePackages4.setAssignedBy("bbb");
		softwarePackages4.setAssignedTime(123l);
		softwarePackages4.setUpdateMode(1);
		SoftwarePackages softwarePackages5 = new SoftwarePackages(device2, software2, softwareVersion2, 1, 1);
		softwarePackages5.setAppVersion("0.0.2");
		softwarePackages5.setAssignedBy("aaa");
		softwarePackages5.setAssignedTime(123l);
		softwarePackages5.setUpdateMode(1);

		entityManager.persist(softwarePackages1);
		entityManager.persist(softwarePackages2);
		entityManager.persist(softwarePackages3);
		entityManager.persist(softwarePackages4);
		entityManager.persist(softwarePackages5);

	}

	@AfterEach
	public void tearDown() {
		entityManager.clear();
	}

	@Test
	public void givenDefaultValues_whenGetSoftwarePackages_thenReceiveAllItemsWithPagination() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "lastUpdated";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);
		System.out.println("Page: " + actualResult.getNumber());

		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
	}

	@Test
	public void givenPageNumberAndPageSize_whenGetSoftwarePackages_thenReceiveItemsAtNumberPage() {
		// given
		int page = 2;
		int pageSize = 3;
		String sortBy = "deviceId";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		assertEquals(5, actualResult.getTotalElements());
		assertEquals(2, actualResult.getTotalPages());
		assertEquals(1, actualResult.getNumber());
		assertEquals(2, actualResult.getNumberOfElements());
	}

	@Test
	public void givenDeviceId_whenGetSoftwarePackages_thenReceiveItemsWithDeviceId() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceId";
		String orderBy = "";
		String deviceUId = "T-0001";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		assertEquals(2, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(2, actualResult.getNumberOfElements());
		actualResult.getContent().forEach((softwarePackage) -> {
			assertEquals("T-0001", softwarePackage.getDevice().getId());
		});
	}

	@Test
	public void givenDeviceModel_whenGetSoftwarePackages_thenReceiveItemsWithDeviceModel() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceId";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "E700";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		actualResult.getContent().forEach((softwarePackage) -> {
			assertEquals("E700", softwarePackage.getDevice().getModel().getModel());
		});
	}

	@Test
	public void givenSoftwarePlatformId_whenGetSoftwarePackages_thenReceiveItemsWithSoftwarePlatform() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceId";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = 1;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		actualResult.getContent().forEach((softwarePackage) -> {
			assertEquals(1, softwarePackage.getSoftware().getServicePlatformId());
		});
	}

	@Test
	public void givenSoftwareId_whenGetSoftwarePackages_thenReceiveItemsWithSoftware() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceId";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = 1L;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		assertEquals(3, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(3, actualResult.getNumberOfElements());
		actualResult.getContent().forEach((softwarePackage) -> {
			assertEquals(1, softwarePackage.getSoftware().getId());
			assertEquals("Caribbean-POS", softwarePackage.getSoftware().getName());
			assertEquals("com.styl.caribbean.pos.amex", softwarePackage.getPackageName());
		});
	}

	@Test
	public void givenOneState_whenGetSoftwarePackages_thenReceiveItemsWithState() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceId";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = Arrays.asList(1);
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		assertEquals(2, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(2, actualResult.getNumberOfElements());
		actualResult.getContent().forEach((softwarePackage) -> {
			assertEquals(1, softwarePackage.getState());
		});
	}

	@Test
	public void givenListState_whenGetSoftwarePackages_thenReceiveItemsWithStates() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceId";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = Arrays.asList(2, 3);
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		assertEquals(3, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(3, actualResult.getNumberOfElements());
		actualResult.getContent().forEach((softwarePackage) -> {
			assertTrue(softwarePackage.getState() == 2 || softwarePackage.getState() == 3);
		});
	}

	@Test
	public void givenOneTag_whenGetSoftwarePackages_thenReceiveItemsWithTag() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceId";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = Arrays.asList(2);
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		assertEquals(3, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(3, actualResult.getNumberOfElements());
	}

	@Test
	public void givenListTag_whenGetSoftwarePackages_thenReceiveItemsWithTags() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceId";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = Arrays.asList(1, 2);
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
	}

	@Test
	public void givenOriginalVersion_whenGetSoftwarePackages_thenReceiveItemsWithOriginalVersion() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceId";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "0.0.1";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		assertEquals(4, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(4, actualResult.getNumberOfElements());
		actualResult.getContent().forEach((softwarePackage) -> {
			assertEquals("0.0.1", softwarePackage.getAppVersion());
		});
	}

	@Test
	public void givenAssignedVersion_whenGetSoftwarePackages_thenReceiveItemsWithAssignedVersion() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceId";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "0.1.1";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		assertEquals(3, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(3, actualResult.getNumberOfElements());
		actualResult.getContent().forEach((softwarePackage) -> {
			assertEquals("0.1.1", softwarePackage.getSoftwareVersion().getVersion());
		});
	}

	@Test
	public void givenOrderByDescending_whenGetSoftwarePackages_thenReceiveIalltemsOrderByDescending() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceId";
		String orderBy = "DESC";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);

		List<SoftwarePackages> actualData = actualResult.getContent();
		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		assertTrue(actualData.get(0).getDevice().getId()
				.compareTo(actualData.get(actualData.size() - 1).getDevice().getId()) > 0);
	}

	@Test
	public void givenSortByDeviceModel_whenGetSoftwarePackages_thenReceiveAllItemsSortByDeviceModel() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "deviceModel";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then

		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);
		System.out.println("Page: " + actualResult.getNumber());

		List<SoftwarePackages> actualData = actualResult.getContent();
		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		assertTrue(actualData.get(0).getDevice().getModel().getModel()
				.compareTo(actualData.get(actualData.size() - 1).getDevice().getModel().getModel()) >= 0);
	}

	@Test
	public void givenSortByAssignedBy_whenGetSoftwarePackages_thenReceiveAllItemsSortByAssignedBy() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "assignedBy";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);
		System.out.println("Page: " + actualResult.getNumber());

		List<SoftwarePackages> actualData = actualResult.getContent();
		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		assertTrue(actualData.get(0).getAssignedBy()
				.compareTo(actualData.get(actualData.size() - 1).getAssignedBy()) >= 0);
	}

	@Test
	public void givenSortByAssignedTime_whenGetSoftwarePackages_thenReceiveAllItemsSortByAssignedTime() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "assignedTime";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);
		System.out.println("Page: " + actualResult.getNumber());

		List<SoftwarePackages> actualData = actualResult.getContent();
		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		assertTrue(actualData.get(0).getAssignedTime()
				.compareTo(actualData.get(actualData.size() - 1).getAssignedTime()) >= 0);
	}

	@Test
	public void givenSortByLastUpdated_whenGetSoftwarePackages_thenReceiveAllItemsSortByLastUpdated() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "lastUpdated";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);
		System.out.println("Page: " + actualResult.getNumber());

		List<SoftwarePackages> actualData = actualResult.getContent();
		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		assertTrue(actualData.get(0).getLastUpdated()
				.compareTo(actualData.get(actualData.size() - 1).getLastUpdated()) >= 0);
	}

	@Test
	public void givenSortByState_whenGetSoftwarePackages_thenReceiveAllItemsSortByState() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "state";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);
		System.out.println("Page: " + actualResult.getNumber());

		List<SoftwarePackages> actualData = actualResult.getContent();
		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		assertTrue(actualData.get(0).getState().compareTo(actualData.get(actualData.size() - 1).getState()) >= 0);
	}

	@Test
	public void givenSortByServicePlatformId_whenGetSoftwarePackages_thenReceiveAllItemsSortByServicePlatformName() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "servicePlatformName";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);
		System.out.println("Page: " + actualResult.getNumber());

		List<SoftwarePackages> actualData = actualResult.getContent();
		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		if (actualData.get(0).getSoftware().getServicePlatform() != null) {
			assertTrue(actualData.get(0).getSoftware().getServicePlatform().getName().compareTo(
					actualData.get(actualData.size() - 1).getSoftware().getServicePlatform().getName()) >= 0);
		}
	}

	@Test
	public void givenSortBySoftwareName_whenGetSoftwarePackages_thenReceiveAllItemsSortBySoftwareName() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "softwareName";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);
		System.out.println("Page: " + actualResult.getNumber());

		List<SoftwarePackages> actualData = actualResult.getContent();
		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		assertTrue(actualData.get(0).getSoftware().getName()
				.compareTo(actualData.get(actualData.size() - 1).getSoftware().getName()) >= 0);
	}

	@Test
	public void givenSortByOriginalVersion_whenGetSoftwarePackages_thenReceiveAllItemsSortByOriginalVersion() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "originalVersion";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);
		System.out.println("Page: " + actualResult.getNumber());

		List<SoftwarePackages> actualData = actualResult.getContent();
		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		assertTrue(actualData.get(0).getAppVersion()
				.compareTo(actualData.get(actualData.size() - 1).getAppVersion()) >= 0);
	}

	@Test
	public void givenSortByAssignedVersion_whenGetSoftwarePackages_thenReceiveAllItemsSortByAssignedVersion() {
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "assignedVersion";
		String orderBy = "";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		Page<SoftwarePackages> actualResult = softwarePackagesService.getSoftwarePackages(page, pageSize, sortBy,
				orderBy, deviceUId, deviceModel, servicePlatformId, softwareId, states, tags, originalVersion,
				assignedVersion, assignedBy);
		System.out.println("Page: " + actualResult.getNumber());

		List<SoftwarePackages> actualData = actualResult.getContent();
		assertEquals(5, actualResult.getTotalElements());
		assertEquals(1, actualResult.getTotalPages());
		assertEquals(0, actualResult.getNumber());
		assertEquals(5, actualResult.getNumberOfElements());
		assertTrue(actualData.get(0).getSoftwareVersion().getVersion()
				.compareTo(actualData.get(actualData.size() - 1).getSoftwareVersion().getVersion()) >= 0);
	}

	@Test
	public void givenDefaultValues_whenGetOtaHistory_thenReceiveExactPaginationData() { // test ota history service
		// given
		int page = 1;
		int pageSize = 10;
		String sortBy = "lastUpdated";
		String orderBy = "DESC";
		String deviceUId = "";
		String deviceModel = "";
		Integer servicePlatformId = null;
		Long softwareId = null;
		List<Integer> states = null;
		List<Integer> tags = null;
		String originalVersion = "";
		String assignedVersion = "";
		String assignedBy = "";

		// then
		List<OTAHistoryResponse> listData = Arrays.asList(new OTAHistoryResponse[5]);

		Pagination<OTAHistoryResponse> expectedResult = new Pagination<OTAHistoryResponse>(5, 1, 10, listData);

		Pagination<OTAHistoryResponse> actualResult = softwarePackagesService.getOtaHistory(deviceUId, deviceModel,
				servicePlatformId, softwareId, states, tags, originalVersion, assignedVersion, assignedBy, page,
				pageSize, sortBy, orderBy);

		assertEquals(expectedResult.getPage(), actualResult.getPage());
		assertEquals(expectedResult.getPageSize(), actualResult.getPageSize());
		assertEquals(expectedResult.getTotal(), actualResult.getTotal());

	}
}
