/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.software;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.Optional;
import java.util.Set;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.TestContants;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.device.model.DeviceModelService;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.SoftwareRepository;
import com.styl.device.management.persistence.software.SoftwareService;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.software.update.mode.UpdateMode;
import com.styl.device.management.persistence.software.update.mode.UpdateModeRepository;
import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.persistence.software.version.SoftwareVersionService;
import com.styl.device.management.rest.portal.admin.software.SoftwareAddRequest;
import com.styl.device.management.rest.portal.admin.software.SoftwareResponse;
import com.styl.device.management.rest.portal.admin.software.SoftwareUpdateRequest;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class SoftwareServiceTest {

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private SoftwareService softwareService;

	@Autowired
	private SoftwareRepository softwareRepository;

	@Autowired
	private DeviceModelService deviceModelService;

	@Autowired
	private UpdateModeRepository updateModeRepository;

	@Autowired
	private SoftwareVersionService softwareVersionService;

	@Test
	public void testAddSoftware() {
		SoftwareAddRequest softwareAdd = new SoftwareAddRequest();
		softwareAdd.setName("TEST ADD SOFTWARE");
		softwareAdd.setPackageName("com.styl.test");
		softwareAdd.setDescription("TEST without service-platform and deviceModel");
		softwareAdd.setServicePlatformId(TestContants.SERVICE_PLATFORM_ID);
		Software result = softwareService.addSoftware(softwareAdd);
		assertEquals("TEST ADD SOFTWARE", result.getName());
		assertEquals("com.styl.test", result.getPackageName());
		assertEquals("TEST without service-platform and deviceModel", result.getDescription());
	}

	@Test
	public void testAddSoftwareWithServicePlatform() {
		SoftwareAddRequest softwareAdd = new SoftwareAddRequest();
		softwareAdd.setName("TEST ADD SOFTWARE WITH PLATFORM");
		softwareAdd.setPackageName("com.styl.test.add.with.service-platform");
		softwareAdd.setDescription("TEST without service-platform and deviceModel");
		softwareAdd.setServicePlatformId(TestContants.SERVICE_PLATFORM_ID);
		Software result = softwareService.addSoftware(softwareAdd);
		assertEquals("TEST ADD SOFTWARE WITH PLATFORM", result.getName());
		assertEquals("com.styl.test.add.with.service-platform", result.getPackageName());
		assertEquals("TEST without service-platform and deviceModel", result.getDescription());
		assertEquals("STYL SOLUTION", result.getServicePlatform().getName());
		assertEquals(TestContants.SERVICE_PLATFORM_ID, result.getServicePlatformId());
	}

	@Test
	public void testAddSoftwareWithDeviceModel() {
		SoftwareAddRequest softwareAdd = new SoftwareAddRequest();
		softwareAdd.setName("TEST ADD SOFTWARE WITH MODEL");
		softwareAdd.setPackageName("com.styl.test.add.with.model");
		softwareAdd.setDescription("TEST without service-platform and deviceModel");
		softwareAdd.setServicePlatformId(TestContants.SERVICE_PLATFORM_ID);
		softwareAdd.setDeviceModels(Set.of(TestContants.MODEL));
		Software result = softwareService.addSoftware(softwareAdd);
		assertEquals("TEST ADD SOFTWARE WITH MODEL", result.getName());
		assertEquals("com.styl.test.add.with.model", result.getPackageName());
		assertEquals("TEST without service-platform and deviceModel", result.getDescription());
		assertEquals("STYL SOLUTION", result.getServicePlatform().getName());
		assertEquals(TestContants.SERVICE_PLATFORM_ID, result.getServicePlatformId());
		assertEquals(1, result.getDeviceModels().size());
	}

	@Test
	public void testAddSoftwareThrowSoftwarePackageIdExisted() {
		SoftwareAddRequest softwareAdd = new SoftwareAddRequest();
		softwareAdd.setName("Test software existed");
		softwareAdd.setPackageName(TestContants.SOFTWARE_PACKAGE_NAME);
		softwareAdd.setDescription("TEST without service-platform and deviceModel");
		softwareAdd.setServicePlatformId(TestContants.SERVICE_PLATFORM_ID);
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			softwareService.addSoftware(softwareAdd);
		});

		assertEquals(ErrorCode.SOFTWARE_EXISTED.getErrorCode(), exception.getErrorCode());
	}

	@Test
	public void testAddSoftwareThrowSoftwareNameExisted() {
		SoftwareAddRequest softwareAdd = new SoftwareAddRequest();
		softwareAdd.setName(TestContants.SOFTWARE_NAME);
		softwareAdd.setPackageName("testAddSoftwareThrowSoftwareNameExisted");
		softwareAdd.setDescription("TEST without service-platform and deviceModel");
		softwareAdd.setServicePlatformId(TestContants.SERVICE_PLATFORM_ID);
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			softwareService.addSoftware(softwareAdd);
		});

		assertEquals(ErrorCode.SOFTWARE_EXISTED.getErrorCode(), exception.getErrorCode());
	}

	@Test
	public void testUpdateSoftware() {
		Software software = softwareService.findByPackageId(TestContants.SOFTWARE_PACKAGE_NAME);
		assertEquals(0, software.getDeviceModels().size());
		assertNotNull(software.getServicePlatform());
		assertNotEquals("Update Name", software.getName());
		assertNotEquals("Update Description", software.getDescription());

		SoftwareUpdateRequest softwareUpdate = new SoftwareUpdateRequest();
		softwareUpdate.setId(software.getId());
		softwareUpdate.setName("Update Name");
		softwareUpdate.setDescription("Update Description");

		Software result = softwareService.updateSoftware(softwareUpdate);
		assertEquals("Update Name", result.getName());
		assertEquals("Update Description", result.getDescription());
	}

	@Test
	public void testUpdateSoftwareThrowSoftwareNotFound() {
		SoftwareUpdateRequest softwareUpdate = new SoftwareUpdateRequest();
		softwareUpdate.setId(121212121L);
		softwareUpdate.setName("Update Name");
		softwareUpdate.setDescription("Update Description");
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			softwareService.updateSoftware(softwareUpdate);
		});

		assertEquals(ErrorCode.SOFTWARE_NOT_FOUND.getErrorCode(), exception.getErrorCode());
	}

	@Test
	public void testUpdateSoftwareThrowSoftwareNameExisted() {
		ServicePlatform sp = addServicePlatform();
		Software sw1 = addSoftware(sp, 1);
		SoftwareUpdateRequest softwareUpdate = new SoftwareUpdateRequest();
		softwareUpdate.setId(sw1.getId());
		softwareUpdate.setName(TestContants.SOFTWARE_NAME);
		softwareUpdate.setDescription("Update Description");
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			softwareService.updateSoftware(softwareUpdate);
		});

		assertEquals(ErrorCode.SOFTWARE_NAME_EXISTED.getErrorCode(), exception.getErrorCode());
	}

	@Test
	public void testRemoveSoftwareNotAssignYet() {
		Software software = softwareService.findByPackageId(TestContants.SOFTWARE_PACKAGE_NAME_TEST_REMOVE);

		Boolean resultRemove = softwareService.removeSoftware(software.getId());
		assertEquals(true, resultRemove);

		Optional<Software> softwareAfterRemove = softwareRepository
				.findByPackageName(TestContants.SOFTWARE_PACKAGE_NAME_TEST_REMOVE);
		assertEquals(true, softwareAfterRemove.isEmpty());
	}

	@Test
	public void testListSoftwareByPaginationWithSortByVersionDESC() {
		Pagination<SoftwareResponse> result = softwareService.listSoftware(null, null, null, null, "id", "DESC", 0, 10);
		Long countByPackageId = softwareRepository.count();
		assertEquals(countByPackageId, result.getTotal());
	}

	@Test
	public void testListSoftwareByPagination() {
		ServicePlatform sp = addServicePlatform();
		addDataTestList(sp);

		Pagination<SoftwareResponse> result = softwareService.listSoftware(null, null, null, null, "id", "DESC", 0, 10);
		Long countByPackageId = softwareRepository.count();
		assertEquals(countByPackageId, result.getTotal());

		// Filter by name: "name 1"
		Pagination<SoftwareResponse> filterByName = softwareService.listSoftware(null, "Name 1", null, null, "id",
				"DESC", 0, 10);
		assertEquals(1, filterByName.getTotal());

		// Filter by package id: com.package
		Pagination<SoftwareResponse> filterByPackage = softwareService.listSoftware("com.package", null, null, null,
				"id", "DESC", 0, 10);
		assertEquals(10, filterByPackage.getTotal());

		// Filter by package id: com.package.6
		Pagination<SoftwareResponse> filterByPackage6 = softwareService.listSoftware("com.package.6", null, null, null,
				"id", "DESC", 0, 10);
		assertEquals(1, filterByPackage6.getTotal());

		// Filter by service-platform
		Pagination<SoftwareResponse> filterByServicePlatform = softwareService.listSoftware(null, null, null,
				sp.getId(), "id", "DESC", 0, 10);
		assertEquals(10, filterByServicePlatform.getTotal());

	}

	@Test
	public void testRemoveSoftwareWhichAlreadyAssign() {
		Device device = addDevice(1);
		ServicePlatform sp = addServicePlatform();
		Software sw1 = addSoftware(sp, 1);
		addSoftwareVersion(sw1, 5);
		// Assign software version 4 (of sw1) to device
		addAssignVersionControl(device, sw1.getPackageName(), "Version " + 4, SoftwarePackages.STATE_1_ASSIGNED);

		Optional<Software> currentSoftware = softwareRepository.findById(sw1.getId());
		assertEquals(true, currentSoftware.isPresent());

		softwareService.removeSoftware(sw1.getId());

		Software updatedSoftware = softwareRepository.findById(sw1.getId()).orElse(null);
		assertNull(updatedSoftware);

	}

	private SoftwarePackages addAssignVersionControl(Device device, String packageId, String version,
			Integer stateAssign) {
		SoftwareVersion softwareVersion = softwareVersionService.findByVersionAndPackageId(version, packageId);
		SoftwarePackages versionControl = new SoftwarePackages();
		versionControl.setDevice(device);
		versionControl.setSoftware(softwareVersion.getSoftware());
		versionControl.setSoftwareVersion(softwareVersion);
		versionControl.setState(stateAssign);
		versionControl.setAssignedTime(System.currentTimeMillis());
		versionControl.setAssignedBy("");
		versionControl.setUpdateMode(softwareVersion.getUpdateMode().getModeId());
		entityManager.persist(versionControl);
		entityManager.flush();
		return versionControl;
	}

	private void addDataTestList(ServicePlatform spId1) {
		for (int i = 0; i < 10; i++) {
			Software newSoftware = new Software();
			newSoftware.setName("Name " + i);
			newSoftware.setDescription("Description " + i);
			newSoftware.setPackageName("com.package." + i);
			entityManager.persist(newSoftware);
			entityManager.flush();
		}
		for (int i = 10; i < 20; i++) {
			Software newSoftware = new Software();
			newSoftware.setName("Name Test " + i);
			newSoftware.setDescription("Description " + i);
			newSoftware.setPackageName("com.add.service.platform" + i);
			newSoftware.setServicePlatform(spId1);
			entityManager.persist(newSoftware);
			entityManager.flush();
		}
	}

	private void addSoftwareVersion(Software software, int numberSVAdd) {
		UpdateMode updateMode = updateModeRepository.findByMode(UpdateMode.MANUAL_MODE).orElse(null);
		for (int i = 1; i <= numberSVAdd; i++) {
			SoftwareVersion newSV = new SoftwareVersion();
			newSV.setVersion("Version " + i);
			newSV.setSoftware(software);
			newSV.setUpdateMode(updateMode);
			newSV.setFilePath("OTA/software/token/" + i);
			newSV.setSoftwareSize(12121212L);
			newSV.setChecksum("adsdad");
			newSV.setCreatedTime(1667788630977L);
			newSV.setCreatedBy("Dino");
			newSV.setReleaseNote("Test");
			entityManager.persist(newSV);
			entityManager.flush();
		}
	}

	private Device addDevice(int numberDevice) {
		DeviceModel model = deviceModelService.findByModel(TestContants.MODEL);
		Device device = new Device();
		device.setHardwareId("hardwareId-" + numberDevice);
		device.setCreatedBy("Dino");
		device.setCreatedTime(121131313L);
		device.setFirstRegistrationTime(121131313L);
		device.setImei("Imeiiiiiiiii");
		device.setSimId("12222222");
		device.setModel(model);
		entityManager.persist(device);
		entityManager.flush();
		return device;
	}

	private Software addSoftware(ServicePlatform spId1, int softwareId) {
		Software newSoftware = new Software();
		newSoftware.setName("Name " + softwareId);
		newSoftware.setDescription("Description " + softwareId);
		newSoftware.setPackageName("com.package." + softwareId);
		entityManager.persist(newSoftware);
		entityManager.flush();
		return newSoftware;
	}

	private ServicePlatform addServicePlatform() {
		ServicePlatform newSp = new ServicePlatform();
		newSp.setName("STYL SOLUTIONS TEST");
		newSp.setShortName("STYL_TEST");
		newSp.setCreatedTime(123456789L);
		newSp.setCreatedBy("Test");
		newSp.setUrl("https://styl.solutions");
		entityManager.persist(newSp);
		entityManager.flush();
		return newSp;
	}
}
