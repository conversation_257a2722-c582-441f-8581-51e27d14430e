/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.software;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

import java.util.Arrays;
import java.util.HashSet;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.SoftwareService;
import com.styl.device.management.rest.portal.admin.software.SoftwareAddRequest;
import com.styl.device.management.rest.portal.admin.software.SoftwareManagementController;
import com.styl.device.management.utils.Utils;

/**
 * <AUTHOR> Lam
 */
@SpringBootTest(properties = {"spring.sql.init.mode=never", "security.api.enabled=false"})
@ActiveProfiles("test")
@AutoConfigureMockMvc
public class SoftwareControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private SoftwareManagementController softwareController;

    @MockBean
    SoftwareService softwareService;

    @Test
    public void testAddSoftware_Success() throws Exception {
        SoftwareAddRequest softwareAdd = new SoftwareAddRequest();
        softwareAdd.setName("TEST ADD SOFTWARE");
        softwareAdd.setPackageName("com.styl.test");
        softwareAdd.setDescription("TEST without service-platform and deviceModel");
        softwareAdd.setServicePlatformId(1);
        softwareAdd.setDeviceModels(new HashSet<>(Arrays.asList("E700", "E800")));
        softwareController.addSoftware(softwareAdd);

        Software newSof = new Software();
        newSof.setId(0L);
        when(softwareService.addSoftware(any())).thenReturn(newSof);

        MockHttpServletResponse response = mockMvc.perform(post("/api/management/software")
                        .contentType(MediaType.APPLICATION_JSON).content(Utils.mapToJson(softwareAdd))).andReturn()
                .getResponse();
        assertEquals(200, response.getStatus());

    }

}
