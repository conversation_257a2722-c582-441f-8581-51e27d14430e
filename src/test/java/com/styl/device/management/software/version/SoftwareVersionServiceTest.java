/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.software.version;

import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doNothing;

import java.io.IOException;
import java.net.URL;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.TestContants;
import com.styl.device.management.config.aws.s3.AwsS3StorageService;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.device.model.DeviceModelService;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesRepository;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesService;
import com.styl.device.management.persistence.software.update.mode.UpdateMode;
import com.styl.device.management.persistence.software.update.mode.UpdateModeRepository;
import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.persistence.software.version.SoftwareVersionRepository;
import com.styl.device.management.persistence.software.version.SoftwareVersionService;
import com.styl.device.management.rest.device.software.SoftwareResponse;
import com.styl.device.management.rest.device.software.StateOTARequest;
import com.styl.device.management.rest.device.software.UpgradeRequest;
import com.styl.device.management.rest.portal.admin.device.DevicePortalResponse;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareUrlResponse;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareVersionAddRequest;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareVersionResponse;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class SoftwareVersionServiceTest {

	private static final Logger logger = LoggerFactory.getLogger(SoftwareVersionServiceTest.class);

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private SoftwareVersionService softwareVersionService;

	@Autowired
	private SoftwareVersionRepository softwareVersionRepository;

	@Mock
	private AwsS3StorageService awsS3StorageService;

	@Autowired
	private DeviceModelService deviceModelService;

	@Autowired
	private UpdateModeRepository updateModeRepository;

	@Autowired
	private SoftwarePackagesRepository softwarePackagesRepository;

	@Autowired
	private SoftwarePackagesService softwarePackagesService;

	@Test
	public void testFindSoftwareByVersionAndPackageId() {
		SoftwareVersion result = softwareVersionService.findByVersionAndPackageId(TestContants.SOFTWARE_VERSION_0_1_7,
				TestContants.SOFTWARE_PACKAGE_NAME);
		logger.info("PackageId: {}; Version: {}", TestContants.SOFTWARE_PACKAGE_NAME,
				TestContants.SOFTWARE_VERSION_0_1_7);
		assertEquals(TestContants.SOFTWARE_VERSION_0_1_7, result.getVersion());
		assertEquals(TestContants.SOFTWARE_PACKAGE_NAME, result.getSoftware().getPackageName());
		assertEquals("Software Test", result.getSoftware().getDescription());
		assertEquals("Caribbean-POS", result.getSoftware().getName());
		assertEquals("ota/software/123456789", result.getFilePath());
		assertEquals(29428723, result.getSoftwareSize());
		assertEquals("100000", result.getChecksum());
		assertEquals(null, result.getUpdatedBy());
		assertEquals(null, result.getUpdatedTime());
		assertEquals(UpdateMode.MANUAL_MODE, result.getUpdateMode().getMode());
		assertEquals("Dino_Lam", result.getCreatedBy());
		assertEquals(1666585880410L, result.getCreatedTime());
	}

	@Test
	public void testSoftwareVersionNotFound() {
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			softwareVersionService.findByVersionAndPackageId("Version not found", "Package not found");
		});

		assertEquals(ErrorCode.SOFTWARE_VERSION_NOT_FOUND.getErrorCode(), exception.getErrorCode());
	}

	@Test
	public void testGenerateUrlToUploadSoftware() throws IOException {
		URL urlMock = new URL("https://www.presigned-example.com");
		ReflectionTestUtils.setField(softwareVersionService, "awsS3StorageService", awsS3StorageService);
		Mockito.<URL>when(awsS3StorageService.requestUploadUrl(ArgumentMatchers.<String>any(), anyLong(),
				ArgumentMatchers.<String>any())).thenReturn(urlMock);
		SoftwareUrlResponse urlResponse = softwareVersionService.generateURLUploadSoftware("md5");
		assertEquals("https://www.presigned-example.com", urlResponse.getUrlUpload());

	}

	@Disabled
	@Test
	public void testGenerateUrlToUploadSoftwareThrowSoftwareExisted() throws IOException {
		URL urlMock = new URL("https://www.presigned-example.com");
		ReflectionTestUtils.setField(softwareVersionService, "awsS3StorageService", awsS3StorageService);
		Mockito.<URL>when(awsS3StorageService.requestUploadUrl(ArgumentMatchers.<String>any(), anyLong(),
				ArgumentMatchers.<String>any())).thenReturn(urlMock);

		softwareVersionService.generateURLUploadSoftware("md5");
	}

	@Test
	public void testGenerateUrlToUploadSoftwareThrowGenerateUrlError() throws IOException {
		ReflectionTestUtils.setField(softwareVersionService, "awsS3StorageService", awsS3StorageService);
		Mockito.<URL>when(awsS3StorageService.requestUploadUrl(ArgumentMatchers.<String>any(), anyLong(),
				ArgumentMatchers.<String>any())).thenThrow(Mockito.mock(IOException.class));
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			softwareVersionService.generateURLUploadSoftware("md5");
		});
		assertEquals(ErrorCode.SOFTWARE_GENERATE_URL_ERROR.getErrorCode(), exception.getErrorCode());

	}

	@Test
	public void testAddSoftwareVersion() {
		ReflectionTestUtils.setField(softwareVersionService, "awsS3StorageService", awsS3StorageService);
		Mockito.<Boolean>when(awsS3StorageService.fileExist(ArgumentMatchers.<String>any())).thenReturn(true);
		SoftwareVersionAddRequest sfvAdd = new SoftwareVersionAddRequest();
		sfvAdd.setVersion("9.9.9");
		sfvAdd.setFilePath("ota/software/pos-software/version-1");
		sfvAdd.setPackageName(TestContants.SOFTWARE_PACKAGE_NAME);
		sfvAdd.setReleaseNote("Release version 1");
		sfvAdd.setChecksum("abcdssad");
		sfvAdd.setUpdateModeId(2);
		sfvAdd.setSoftwareSize(1000L);

		SoftwareVersionResponse result = softwareVersionService.addSoftwareVersion(sfvAdd);
		assertEquals("Caribbean-POS", result.getName());
		assertEquals("com.styl.caribbean.pos.amex", result.getPackageName());
		assertEquals("9.9.9", result.getVersion());
		assertEquals(2, result.getUpdateMode());
	}

	@Test
	public void testAddSoftwareVersionThrowSoftwareVersionExisted() throws IOException {
		SoftwareVersionAddRequest sfvAdd = new SoftwareVersionAddRequest();
		sfvAdd.setVersion(TestContants.SOFTWARE_VERSION_0_1_7);
		sfvAdd.setFilePath("ota/software/pos-software/version-1");
		sfvAdd.setPackageName(TestContants.SOFTWARE_PACKAGE_NAME);
		sfvAdd.setReleaseNote("Release version 1");
		sfvAdd.setChecksum("abcdssad");
		sfvAdd.setUpdateModeId(2);
		sfvAdd.setSoftwareSize(1000L);

		ReflectionTestUtils.setField(softwareVersionService, "awsS3StorageService", awsS3StorageService);
		Mockito.<Boolean>when(awsS3StorageService.fileExist(ArgumentMatchers.<String>any())).thenReturn(true);
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			softwareVersionService.addSoftwareVersion(sfvAdd);
		});
		assertEquals(ErrorCode.SOFTWARE_VERSION_EXISTED.getErrorCode(), exception.getErrorCode());
	}

	@Test
	public void testAddSoftwareVersionThrowSoftwareVersionFileNotFound() throws IOException {
		SoftwareVersionAddRequest sfvAdd = new SoftwareVersionAddRequest();
		sfvAdd.setVersion("Version Test File Not Found");
		sfvAdd.setFilePath("ota/software/pos-software/version-1");
		sfvAdd.setPackageName(TestContants.SOFTWARE_PACKAGE_NAME);
		sfvAdd.setReleaseNote("Release version 1");
		sfvAdd.setChecksum("abcdssad");
		sfvAdd.setUpdateModeId(2);
		sfvAdd.setSoftwareSize(1000L);

		ReflectionTestUtils.setField(softwareVersionService, "awsS3StorageService", awsS3StorageService);
		Mockito.<Boolean>when(awsS3StorageService.fileExist(ArgumentMatchers.<String>any())).thenReturn(false);
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			softwareVersionService.addSoftwareVersion(sfvAdd);
		});
		assertEquals(ErrorCode.SOFTWARE_VERSION_CAN_NOT_ADD.getErrorCode(), exception.getErrorCode());
	}

	@Test
	public void testAddSoftwareVersionThrowUpdateModeNotFound() throws IOException {
		SoftwareVersionAddRequest sfvAdd = new SoftwareVersionAddRequest();
		sfvAdd.setVersion("Test version update mode not correct");
		sfvAdd.setFilePath("ota/software/pos-software/version-1");
		sfvAdd.setPackageName(TestContants.SOFTWARE_PACKAGE_NAME);
		sfvAdd.setReleaseNote("Release version 1");
		sfvAdd.setChecksum("abcdssad");
		sfvAdd.setUpdateModeId(1100);
		sfvAdd.setSoftwareSize(1000L);

		ReflectionTestUtils.setField(softwareVersionService, "awsS3StorageService", awsS3StorageService);
		Mockito.<Boolean>when(awsS3StorageService.fileExist(ArgumentMatchers.<String>any())).thenReturn(true);
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			softwareVersionService.addSoftwareVersion(sfvAdd);
		});
		assertEquals(ErrorCode.UPDATE_MODE_NOT_FOUND.getErrorCode(), exception.getErrorCode());
	}

	@Test
	public void testListSoftwareVersionByPaginationWithSortByVersionASC() {
		Pagination<SoftwareVersionResponse> result = softwareVersionService.findSoftwareVersionByPagination(null,
				TestContants.SOFTWARE_PACKAGE_NAME, null, null, null, null, null, "version", "ASC", 0, 3);
		Long countByPackageName = softwareVersionRepository.countByPackageName(TestContants.SOFTWARE_PACKAGE_NAME);
		assertEquals(countByPackageName, result.getTotal());
		assertEquals(1, result.getData().get(0).getId());
		assertEquals("Caribbean-POS", result.getData().get(0).getName());
		assertEquals("com.styl.caribbean.pos.amex", result.getData().get(0).getPackageName());
		assertEquals("0.1.1", result.getData().get(0).getVersion());
		assertEquals(1, result.getData().get(0).getUpdateMode());
	}

	@Test
	public void testListSoftwareVersionByPaginationWithSortByVersionDES() {
		Pagination<SoftwareVersionResponse> result = softwareVersionService.findSoftwareVersionByPagination(null,
				TestContants.SOFTWARE_PACKAGE_NAME, null, null, null, null, null, "version", "DESC", 0, 3);
		Long countByPackageId = softwareVersionRepository.countByPackageName(TestContants.SOFTWARE_PACKAGE_NAME);
		assertEquals(countByPackageId, result.getTotal());
		assertEquals("Caribbean-POS", result.getData().get(0).getName());
		assertEquals("com.styl.caribbean.pos.amex", result.getData().get(0).getPackageName());
		assertEquals("0.2.0", result.getData().get(0).getVersion());
		assertEquals(3, result.getData().get(0).getUpdateMode());
	}

	@Test
	public void testListDeviceAssignBySoftware() {
		Optional<SoftwareVersion> sw = softwareVersionRepository
				.findByVerionAndPackageName(TestContants.SOFTWARE_VERSION_0_1_7, TestContants.SOFTWARE_PACKAGE_NAME);
		assertEquals(true, sw.isPresent());
		Device device = addDevice(1);
		addAssignStateInstalled(device, TestContants.SOFTWARE_VERSION_0_1_7);
		addAssignStateInstalled(device, TestContants.SOFTWARE_VERSION_0_1_8);
		Set<DevicePortalResponse> listDeviceBySoftwareVersion = softwareVersionService
				.listDeviceBySoftwareVersion(sw.get().getId());
		listDeviceBySoftwareVersion.forEach(i -> {
			System.out.println(i.getDeviceUid());
		});
	}

	@Test
	public void testOTAThrowDeviceNotFound() {
		UpgradeRequest upgradeRequest = new UpgradeRequest();
		upgradeRequest.setDeviceUid("DEVICE_NOT_FOUND");

		ServiceException exception = assertThrows(ServiceException.class, () -> {
			softwareVersionService.ota(upgradeRequest);
		});
		assertEquals(ErrorCode.DEVICE_NOT_FOUND.getErrorCode(), exception.getErrorCode());
	}

	@Test
	public void testOTA() {
		// Add data to test
		Device device = addDevice(1);
		ServicePlatform sp = addServicePlatform();

		Software sw1 = addSoftware(sp, 1);
		Software sw2 = addSoftware(sp, 2);
		addSoftwareVersion(sw1, 10);
		addSoftwareVersion(sw2, 8);

		// generate request
		UpgradeRequest upgradeRequest = new UpgradeRequest();
		upgradeRequest.setDeviceUid(device.getId());

		// Assign software version 4 (of sw1) to device
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			assignSoftwareVersion(device, sw1.getPackageName(), "Version " + 4, SoftwarePackages.STATE_1_ASSIGNED);
		});
		assertEquals(ErrorCode.DEVICE_NEED_ASSIGN_SP.getErrorCode(), exception.getErrorCode());

		device.setServicePlatform(sp);
		entityManager.persist(device);

		assignSoftwareVersion(device, sw1.getPackageName(), "Version " + 4, SoftwarePackages.STATE_1_ASSIGNED);
		List<SoftwareResponse> result_1 = softwareVersionService.ota(upgradeRequest);
		assertEquals(1, result_1.size());
		assertEquals(sw1.getPackageName(), result_1.get(0).getPackageName());
		assertEquals("Version " + 4, result_1.get(0).getVersion());
		assertEquals(UpdateMode.MANUAL_MODE, result_1.get(0).getUpdateMode());

		// Assign software version 7 (of sw2) to device
		assignSoftwareVersion(device, sw2.getPackageName(), "Version " + 7, SoftwarePackages.STATE_1_ASSIGNED);
		List<SoftwareResponse> result_2 = softwareVersionService.ota(upgradeRequest).stream()
				.sorted(Comparator.comparingLong(SoftwareResponse::getSoftwarePackageId).reversed())
				.collect(Collectors.toList());
		assertEquals(2, result_2.size());

		// Result software version 7 assign to sw2
		SoftwareResponse sv7 = result_2.get(0);
		// Result software version 4 assign to sw1
		SoftwareResponse sv4 = result_2.get(1);

		assertEquals(sw1.getPackageName(), sv4.getPackageName());
		assertEquals("Version " + 4, sv4.getVersion());
		assertEquals(UpdateMode.MANUAL_MODE, sv4.getUpdateMode());

		assertEquals(sw2.getPackageName(), sv7.getPackageName());
		assertEquals("Version " + 7, sv7.getVersion());
		assertEquals(UpdateMode.MANUAL_MODE, sv7.getUpdateMode());

		// Assign software version 9 (of sw1) to device
		assignSoftwareVersion(device, sw1.getPackageName(), "Version " + 9, SoftwarePackages.STATE_1_ASSIGNED);
		List<SoftwareResponse> result_3 = softwareVersionService.ota(upgradeRequest).stream()
				.sorted(Comparator.comparingLong(SoftwareResponse::getSoftwarePackageId).reversed())
				.collect(Collectors.toList());
		assertEquals(2, result_3.size());

		// Result software version 9 assign to sw1
		SoftwareResponse result_3_sv9 = result_3.get(0);
		// Result software version 7 assign to sw2
		SoftwareResponse result_3_sv7 = result_3.get(1);

		assertEquals(sw1.getPackageName(), result_3_sv9.getPackageName());
		assertEquals("Version " + 9, result_3_sv9.getVersion());
		assertEquals(UpdateMode.MANUAL_MODE, result_3_sv9.getUpdateMode());

		assertEquals(sw2.getPackageName(), result_3_sv7.getPackageName());
		assertEquals("Version " + 7, result_3_sv7.getVersion());
		assertEquals(UpdateMode.MANUAL_MODE, result_3_sv7.getUpdateMode());

		// Reassign software 4 (of sw1) to device
		assignSoftwareVersion(device, sw1.getPackageName(), "Version " + 4, SoftwarePackages.STATE_1_ASSIGNED);
		List<SoftwareResponse> result_4 = softwareVersionService.ota(upgradeRequest).stream()
				.sorted(Comparator.comparingLong(SoftwareResponse::getSoftwarePackageId).reversed())
				.collect(Collectors.toList());
		assertEquals(2, result_4.size());
		// Result software version 4 assign to sw1
		SoftwareResponse result_4_sv4 = result_4.get(0);
		// Result software version 7 assign to sw2
		SoftwareResponse result_4_sv7 = result_4.get(1);

		assertEquals(sw1.getPackageName(), result_4_sv4.getPackageName());
		assertEquals("Version " + 4, result_4_sv4.getVersion());

		assertEquals(sw2.getPackageName(), result_4_sv7.getPackageName());
		assertEquals("Version " + 7, result_4_sv7.getVersion());

	}

	@Test
	public void testSubmitStateOTA() {
		// Add data to test
		Device device = addDevice(1);
		ServicePlatform sp = addServicePlatform();
		Software sw1 = addSoftware(sp, 1);
		addSoftwareVersion(sw1, 5);

		device.setServicePlatform(sp);
		entityManager.persist(device);

		// Assign software version 4 (of sw1) to device
		assignSoftwareVersion(device, sw1.getPackageName(), "Version " + 4, SoftwarePackages.STATE_1_ASSIGNED);

		List<SoftwarePackages> assignedPackages = softwarePackagesRepository.findSoftwarePackage(device.getId(),
				sw1.getId(), "Version " + 4, Arrays.asList(SoftwarePackages.STATE_1_ASSIGNED));

		assertFalse(assignedPackages.isEmpty());
		Long vscId = assignedPackages.get(0).getId();

		Optional<SoftwarePackages> findById = softwarePackagesRepository.findById(vscId);
		assertEquals(true, findById.isPresent());
		assertEquals(SoftwarePackages.STATE_1_ASSIGNED, findById.get().getState());

		StateOTARequest request_1 = new StateOTARequest();
		request_1.setDeviceUid(device.getId());
		request_1.setState(SoftwarePackages.STATE_2_DOWNLOADING);
		request_1.setSoftwarePackageId(vscId);
		SoftwarePackages result_1 = softwareVersionService.submitStateOTA(request_1);
		assertEquals(SoftwarePackages.STATE_2_DOWNLOADING, result_1.getState());

		StateOTARequest request_2 = new StateOTARequest();
		request_2.setDeviceUid(device.getId());
		request_2.setState(SoftwarePackages.STATE_6_UPDATED);
		request_2.setSoftwarePackageId(vscId);
		SoftwarePackages result_2 = softwareVersionService.submitStateOTA(request_2);
		assertEquals(SoftwarePackages.STATE_6_UPDATED, result_2.getState());

	}

	@Test
	public void testRemoveSoftwareVersion_Success() throws IOException {
		Optional<SoftwareVersion> sofV = softwareVersionRepository
				.findByVerionAndPackageName(TestContants.SOFTWARE_VERSION_0_1_8, TestContants.SOFTWARE_PACKAGE_NAME);

		assertEquals(true, sofV.isPresent());
		ReflectionTestUtils.setField(softwareVersionService, "awsS3StorageService", awsS3StorageService);
		doNothing().when(awsS3StorageService).deleteFile(ArgumentMatchers.<String>any());
		boolean removeSoftwareVersion = softwareVersionService.removeSoftwareVersion(sofV.get().getId());
		assertEquals(true, removeSoftwareVersion);

	}

	@Disabled
	@Test
	public void testListDeviceCanAssignSoftwareVersion_success() {
		DeviceModel model = deviceModelService.findByModel(TestContants.MODEL);
		ServicePlatform sp = addServicePlatform();
		Software sw1 = addSoftware(sp, 1);
		sw1.setDeviceModels(Set.of(model));
		SoftwareVersion sv = addSWV(sw1);

		Set<DevicePortalResponse> listDeviceCanAssignBySV = softwareVersionService
				.listDeviceCanBeAssignedBySoftwareVersion(sv.getId());
		assertEquals(0, listDeviceCanAssignBySV.size());

		addDeviceWithSP(1, sp);
		Set<DevicePortalResponse> result_2 = softwareVersionService
				.listDeviceCanBeAssignedBySoftwareVersion(sv.getId());
		assertEquals(1, result_2.size());
	}

	private void assignSoftwareVersion(Device device, String packageId, String version, Integer stateAssign) {
		SoftwareVersion softwareVersion = softwareVersionService.findByVersionAndPackageId(version, packageId);
		softwarePackagesService.assignBySoftwareVersion(softwareVersion, Arrays.asList(device.getId()), "test");
	}

	private Software addSoftware(ServicePlatform spId1, int softwareId) {
		Software newSoftware = new Software();
		newSoftware.setName("Name " + softwareId);
		newSoftware.setDescription("Description " + softwareId);
		newSoftware.setPackageName("com.package." + softwareId);
		newSoftware.setServicePlatform(spId1);
		entityManager.persist(newSoftware);
		entityManager.flush();
		return newSoftware;
	}

	private void addSoftwareVersion(Software software, int numberSVAdd) {
		UpdateMode updateMode = updateModeRepository.findByMode(UpdateMode.MANUAL_MODE).orElse(null);
		for (int i = 1; i <= numberSVAdd; i++) {
			SoftwareVersion newSV = new SoftwareVersion();
			newSV.setVersion("Version " + i);
			newSV.setSoftware(software);
			newSV.setUpdateMode(updateMode);
			newSV.setFilePath("OTA/software/token/" + i);
			newSV.setSoftwareSize(12121212L);
			newSV.setChecksum("abcdssad");
			newSV.setCreatedTime(1667788630977L);
			newSV.setCreatedBy("Dino");
			newSV.setReleaseNote("Test");
			entityManager.persist(newSV);
			entityManager.flush();
		}
	}

	private SoftwareVersion addSWV(Software software) {
		UpdateMode updateMode = updateModeRepository.findByMode(UpdateMode.MANUAL_MODE).orElse(null);
		SoftwareVersion newSV = new SoftwareVersion();
		newSV.setVersion("Version ");
		newSV.setSoftware(software);
		newSV.setUpdateMode(updateMode);
		newSV.setFilePath("OTA/software/token/");
		newSV.setSoftwareSize(12121212L);
		newSV.setChecksum("abcdssad");
		newSV.setCreatedTime(1667788630977L);
		newSV.setCreatedBy("Dino");
		newSV.setReleaseNote("Test");
		entityManager.persist(newSV);
		entityManager.flush();
		return newSV;
	}

	private ServicePlatform addServicePlatform() {
		ServicePlatform newSp = new ServicePlatform();
		newSp.setName("STYL SOLUTIONS TEST");
		newSp.setShortName("STYL_TEST");
		newSp.setCreatedTime(123456789L);
		newSp.setCreatedBy("Test");
		newSp.setUrl("https://styl.solutions");
		entityManager.persist(newSp);
		entityManager.flush();
		return newSp;
	}

	private Device addDevice(int numberDevice) {
		DeviceModel model = deviceModelService.findByModel(TestContants.MODEL);
		Device device = new Device();
		device.setHardwareId("hardwareId-" + numberDevice);
		device.setCreatedBy("Dino");
		device.setCreatedTime(121131313L);
		device.setFirstRegistrationTime(121131313L);
		device.setImei("Imeiiiiiiiii");
		device.setSimId("12222222");
		device.setModel(model);
		device.setState(Device.ASSIGNED);
		entityManager.persist(device);
		entityManager.flush();
		return device;
	}

	private Device addDeviceWithSP(int numberDevice, ServicePlatform sp) {
		DeviceModel model = deviceModelService.findByModel(TestContants.MODEL);
		Device device = new Device();
		device.setHardwareId("hardwareId-" + numberDevice);
		device.setCreatedBy("Dino");
		device.setCreatedTime(121131313L);
		device.setFirstRegistrationTime(121131313L);
		device.setImei("Imeiiiiiiiii");
		device.setSimId("12222222");
		device.setModel(model);
		device.setState(Device.ASSIGNED);
		device.setServicePlatform(sp);
		entityManager.persist(device);
		entityManager.flush();
		return device;
	}

	private void addAssignStateInstalled(Device device, String version) {
		SoftwareVersion softwareVersion = softwareVersionService.findByVersionAndPackageId(version,
				TestContants.SOFTWARE_PACKAGE_NAME);

		SoftwarePackages versionControl = new SoftwarePackages();
		versionControl.setDevice(device);
		versionControl.setSoftware(softwareVersion.getSoftware());
		versionControl.setSoftwareVersion(softwareVersion);
		versionControl.setState(SoftwarePackages.STATE_6_UPDATED);
		versionControl.setAssignedTime(System.currentTimeMillis());
		versionControl.setAssignedBy("");
		versionControl.setUpdateMode(softwareVersion.getUpdateMode().getModeId());
		entityManager.persist(versionControl);
		entityManager.flush();
	}

}
