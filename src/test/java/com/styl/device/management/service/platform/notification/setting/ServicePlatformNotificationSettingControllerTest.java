/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.service.platform.notification.setting;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Collection;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.device.management.auth.service.platform.PreConditionUtil;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.kafka.KafkaTopicService;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.service.platform.ServicePlatformRepository;
import com.styl.device.management.persistence.service.platform.notification.setting.ServicePlatformNotificationSetting;
import com.styl.device.management.persistence.service.platform.notification.setting.ServicePlatformNotificationSettingService;
import com.styl.device.management.rest.portal.admin.service.platform.notification.setting.ServicePlatformNotificationSettingResponse;
import com.styl.device.management.rest.portal.admin.service.platform.notification.setting.ServicePlatformNotificationSettingUpdateRequest;
import com.styl.device.management.service.platform.event.publisher.event.common.EventVersion;

/**
 * <AUTHOR> Yee
 *
 */
@SpringBootTest(properties = { "spring.sql.init.mode=never" })
@ActiveProfiles("test")
@AutoConfigureMockMvc(addFilters = false)
@Transactional
@DirtiesContext
@EmbeddedKafka(partitions = 1, brokerProperties = { "listeners=PLAINTEXT://localhost:9093",
		"port=9093" }, kraft = false)
public class ServicePlatformNotificationSettingControllerTest {

	@Autowired
	protected MockMvc mockMvc;

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	ServicePlatformNotificationSettingService spNotificationSettingService;

	@Autowired
	ServicePlatformRepository spRepository;

	@Autowired
	KafkaTopicService topicService;

	private static final String EVENT_TOPIC_PREFIX = "DMS";

	@Test
	public void whenGetNonExistNotificationSetting_ThenReturnDummyNS() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		MockHttpServletResponse response = mockMvc
				.perform(get("/api/management/service-platform/notification-setting/" + sp.getId())
						.accept(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk()).andReturn().getResponse();
		ServicePlatformNotificationSettingResponse result = getObject(response.getContentAsString(),
				ServicePlatformNotificationSettingResponse.class);
		assertNotNull(result);
		assertNull(result.getServicePlatformId());

	}

	@Test
	public void whenGetExistNotificationSetting_ThenReturnNS() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		PreConditionUtil.addSpNotificationSetting(entityManager, "test", sp);
		MockHttpServletResponse response = mockMvc
				.perform(get("/api/management/service-platform/notification-setting/" + sp.getId())
						.accept(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk()).andReturn().getResponse();
		ServicePlatformNotificationSettingResponse result = getObject(response.getContentAsString(),
				ServicePlatformNotificationSettingResponse.class);
		assertNotNull(result);
		assertEquals(sp.getId(), result.getServicePlatformId());
	}

	@Test
	public void whenManipulateNonExistNotificationSetting_ThenAddSuccessfully() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		ServicePlatformNotificationSettingUpdateRequest request = new ServicePlatformNotificationSettingUpdateRequest();
		request.setServicePlatformId(sp.getId());
		request.setEventVersion(EventVersion.V1.toString());
		request.setEnabledWebhook(true);
		request.setWebhookUrl("testurl");
		MockHttpServletResponse response = mockMvc
				.perform(post("/api/management/service-platform/notification-setting/" + sp.getId())
						.content(convertObjectToJson(request)).contentType(MediaType.APPLICATION_JSON)
						.accept(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk()).andReturn().getResponse();
		ServicePlatformNotificationSettingResponse result = getObject(response.getContentAsString(),
				ServicePlatformNotificationSettingResponse.class);
		assertNotNull(result);
		assertEquals(EventVersion.V1.toString(), result.getEventVersion());
		assertEquals(true, result.getEnabledWebhook());
		assertEquals("testurl", result.getWebhookUrl());

		ServicePlatformNotificationSetting ns = entityManager.find(ServicePlatformNotificationSetting.class,
				sp.getId());
		assertNotNull(ns);
		assertEquals(EventVersion.V1.toString(), ns.getEventVersion());
		assertEquals(true, ns.isEnabledWebhook());
		assertEquals("testurl", ns.getWebhookUrl());

	}

	@Test
	public void whenUpdateNullEnabledWebhook_ThenAddSuccessfully() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		ServicePlatformNotificationSettingUpdateRequest request = new ServicePlatformNotificationSettingUpdateRequest();
		request.setServicePlatformId(sp.getId());
		request.setEventVersion(EventVersion.V1.toString());
		request.setEnabledWebhook(null);
		request.setWebhookUrl("testurl");
		MockHttpServletResponse response = mockMvc
				.perform(post("/api/management/service-platform/notification-setting/" + sp.getId())
						.content(convertObjectToJson(request)).contentType(MediaType.APPLICATION_JSON)
						.accept(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk()).andReturn().getResponse();
		ServicePlatformNotificationSettingResponse result = getObject(response.getContentAsString(),
				ServicePlatformNotificationSettingResponse.class);
		assertNotNull(result);
		assertEquals(EventVersion.V1.toString(), result.getEventVersion());
		assertEquals(false, result.getEnabledWebhook());
		assertEquals("testurl", result.getWebhookUrl());

		ServicePlatformNotificationSetting ns = entityManager.find(ServicePlatformNotificationSetting.class,
				sp.getId());
		assertNotNull(ns);
		assertEquals(EventVersion.V1.toString(), ns.getEventVersion());
		assertEquals(false, ns.isEnabledWebhook());
		assertEquals("testurl", ns.getWebhookUrl());

	}

	@Test
	public void whenManipulateExistNotificationSetting_ThenUpdateSuccessfully() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		PreConditionUtil.addSpNotificationSetting(entityManager, "test", sp);
		ServicePlatformNotificationSettingUpdateRequest request = new ServicePlatformNotificationSettingUpdateRequest();
		request.setServicePlatformId(sp.getId());
		request.setEventVersion(EventVersion.V1.toString());
		request.setEnabledWebhook(false);
		request.setWebhookUrl("testurl2");
		MockHttpServletResponse response = mockMvc
				.perform(post("/api/management/service-platform/notification-setting/" + sp.getId())
						.content(convertObjectToJson(request)).contentType(MediaType.APPLICATION_JSON)
						.accept(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk()).andReturn().getResponse();
		ServicePlatformNotificationSettingResponse result = getObject(response.getContentAsString(),
				ServicePlatformNotificationSettingResponse.class);
		assertNotNull(result);
		assertEquals(EventVersion.V1.toString(), result.getEventVersion());
		assertEquals(false, result.getEnabledWebhook());
		assertEquals("testurl2", result.getWebhookUrl());

		ServicePlatformNotificationSetting ns = entityManager.find(ServicePlatformNotificationSetting.class,
				sp.getId());
		assertNotNull(ns);
		assertEquals(EventVersion.V1.toString(), ns.getEventVersion());
		assertEquals(false, ns.isEnabledWebhook());
		assertEquals("testurl2", ns.getWebhookUrl());
	}

	@Test
	public void whenManipulateNSWithNonExistEventVersion_ThenThrowError() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		PreConditionUtil.addSpNotificationSetting(entityManager, "test", sp);
		ServicePlatformNotificationSettingUpdateRequest request = new ServicePlatformNotificationSettingUpdateRequest();
		request.setServicePlatformId(sp.getId());
		request.setEventVersion("2021-01-01");
		request.setEnabledWebhook(true);
		request.setWebhookUrl("testurl");
		mockMvc.perform(post("/api/management/service-platform/notification-setting/" + sp.getId())
				.content(convertObjectToJson(request)).contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.jsonPath("errors").exists())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isNotEmpty())
				.andExpect(MockMvcResultMatchers.jsonPath("errors[0].errorCode")
						.value(ErrorCode.SERVICE_PLATFORM_NOTIFICATION_SETTING_EVENT_VERSION_NOT_FOUND.getErrorCode()));
	}

	@Test
	public void whenCreateNewTopic_ThenCreateSuccessfully() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		PreConditionUtil.addSpNotificationSetting(entityManager, "test", sp);
		MockHttpServletResponse response = mockMvc
				.perform(post("/api/management/service-platform/notification-setting/" + sp.getId() + "/createtopic")
						.accept(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk()).andReturn().getResponse();
		ServicePlatformNotificationSettingResponse result = getObject(response.getContentAsString(),
				ServicePlatformNotificationSettingResponse.class);
		assertNotNull(result);
		assertEquals(EVENT_TOPIC_PREFIX + ".testsp", result.getEventTopic());

		ServicePlatformNotificationSetting ns = entityManager.find(ServicePlatformNotificationSetting.class,
				sp.getId());
		assertNotNull(ns);
		assertEquals(EVENT_TOPIC_PREFIX + ".testsp", ns.getEventTopic());

	}

	@Test
	public void whenCreateNewTopicForNonExistNS_ThenCreateSuccessfully() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp2");
		MockHttpServletResponse response = mockMvc
				.perform(post("/api/management/service-platform/notification-setting/" + sp.getId() + "/createtopic")
						.accept(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk()).andReturn().getResponse();
		ServicePlatformNotificationSettingResponse result = getObject(response.getContentAsString(),
				ServicePlatformNotificationSettingResponse.class);
		assertNotNull(result);
		assertEquals(EVENT_TOPIC_PREFIX + ".testsp2", result.getEventTopic());

		ServicePlatformNotificationSetting ns = entityManager.find(ServicePlatformNotificationSetting.class,
				sp.getId());
		assertNotNull(ns);
		assertEquals(EVENT_TOPIC_PREFIX + ".testsp2", ns.getEventTopic());
	}

	@Test
	public void whenCreateNewTopicWithTopicNameExisted_ThenCreateSuccessfullyWithSuffix() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		PreConditionUtil.addSpNotificationSetting(entityManager, "test2", sp);
		topicService.createTopic(EVENT_TOPIC_PREFIX + ".testsp");
		topicService.createTopic(EVENT_TOPIC_PREFIX + ".testsp.1");
		MockHttpServletResponse response = mockMvc
				.perform(post("/api/management/service-platform/notification-setting/" + sp.getId() + "/createtopic")
						.accept(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk()).andReturn().getResponse();
		ServicePlatformNotificationSettingResponse result = getObject(response.getContentAsString(),
				ServicePlatformNotificationSettingResponse.class);
		assertNotNull(result);
		assertEquals(EVENT_TOPIC_PREFIX + ".testsp.2", result.getEventTopic());

		ServicePlatformNotificationSetting ns = entityManager.find(ServicePlatformNotificationSetting.class,
				sp.getId());
		assertNotNull(ns);
		assertEquals(EVENT_TOPIC_PREFIX + ".testsp.2", ns.getEventTopic());
	}

	@Test
	public void whenCreateNewTopicWithTopicNameExistedMoreThanMaxTry_ThenFailedToCreate() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		PreConditionUtil.addSpNotificationSetting(entityManager, "test2", sp);
		topicService.createTopic(EVENT_TOPIC_PREFIX + ".testsp");
		topicService.createTopic(EVENT_TOPIC_PREFIX + ".testsp.1");
		topicService.createTopic(EVENT_TOPIC_PREFIX + ".testsp.2");
		topicService.createTopic(EVENT_TOPIC_PREFIX + ".testsp.3");
		topicService.createTopic(EVENT_TOPIC_PREFIX + ".testsp.4");
		topicService.createTopic(EVENT_TOPIC_PREFIX + ".testsp.5");
		mockMvc.perform(post("/api/management/service-platform/notification-setting/" + sp.getId() + "/createtopic")
				.accept(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.jsonPath("errors").exists())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isNotEmpty())
				.andExpect(MockMvcResultMatchers.jsonPath("errors[0].errorCode")
						.value(ErrorCode.SERVICE_PLATFORM_TOPIC_CREATE_FAILED.getErrorCode()));
	}

	@Test
	public void whenListEventVersions_ThenListSuccessfully() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		PreConditionUtil.addSpNotificationSetting(entityManager, "test", sp);
		MockHttpServletResponse response = mockMvc
				.perform(get("/api/management/service-platform/notification-setting/eventversions")
						.accept(MediaType.APPLICATION_JSON))
				.andReturn().getResponse();
		@SuppressWarnings("unchecked")
		Collection<String> result = getObject(response.getContentAsString(), Collection.class);
		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals(EventVersion.V1.toString(), result.iterator().next());
	}

	@Test
	public void testGetNSByServicePlatformShortName() throws Exception {
		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		PreConditionUtil.addSpNotificationSetting(entityManager, "test", sp);
		ServicePlatformNotificationSetting ns = spNotificationSettingService.findByServicePlatformShortName("testsp");
		assertNotNull(ns);
		assertEquals(sp.getId(), ns.getId());
	}

	public String convertObjectToJson(Object object) throws JsonProcessingException {
		if (object == null) {
			return null;
		}
		ObjectMapper mapper = new ObjectMapper();
		return mapper.writeValueAsString(object);
	}

	public <T> T getObject(String data, Class<T> type) {
		T target = null;
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			target = objectMapper.readValue(data, type);
			// JAXBUtils.marshal(target);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return target;
	}

}
