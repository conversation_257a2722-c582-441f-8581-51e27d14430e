/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.platform;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.TestContants;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.service.platform.ServicePlatformRepository;
import com.styl.device.management.persistence.service.platform.ServicePlatformService;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.SoftwareService;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformAddRequest;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformPortalResponse;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformUpdateRequest;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ServicePlatformServiceTest {

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	ServicePlatformService spService;

	@Autowired
	ServicePlatformRepository spRepository;

	@Autowired
	SoftwareService softwareService;

	@Test
	public void testAddServicePlatform() {
		ServicePlatformAddRequest addRequest = new ServicePlatformAddRequest();
		addRequest.setName("STYL SOLUTIONS ADD");
		addRequest.setShortName("STYL ADD");
		addRequest.setUrl("https://example.com");
		addRequest.setEmailContact("<EMAIL>");
		addRequest.setNameContact("Dino Lam");
		addRequest.setPhoneContact("121313131");

		ServicePlatform result = spService.addServicePlatform(addRequest);
		assertEquals("STYL SOLUTIONS ADD", result.getName());
		assertEquals("STYL ADD", result.getShortName());
		assertEquals("https://example.com", result.getUrl());
		assertEquals("<EMAIL>", result.getContactEmail());
		assertEquals("Dino Lam", result.getContactName());
		assertEquals("121313131", result.getContactPhone());

		Optional<ServicePlatform> findByName = spRepository.findByName("STYL SOLUTIONS ADD");
		assertEquals(true, findByName.isPresent());
	}

	@Test
	public void testAddServicePlatformThrowErrorServicePlatformExisted() {
		ServicePlatformAddRequest addRequest = new ServicePlatformAddRequest();
		addRequest.setName(TestContants.SERVICE_PLATFORM_NAME);
		addRequest.setShortName(TestContants.SERVICE_PLATFORM_SHORTNAME);
		addRequest.setUrl("https://example.com");
		addRequest.setEmailContact("<EMAIL>");
		addRequest.setNameContact("Dino Lam");
		addRequest.setPhoneContact("121313131");
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			spService.addServicePlatform(addRequest);
		});

		assertEquals(ErrorCode.SERVICE_PLATFORM_EXISTED.getErrorCode(), exception.getErrorCode());

	}

	@Test
	public void testUpdateServicePlatform() {
		ServicePlatformUpdateRequest updateRequest = new ServicePlatformUpdateRequest();
		updateRequest.setId(TestContants.SERVICE_PLATFORM_ID);
		updateRequest.setEmailContact("test contact email");
		updateRequest.setNameContact("test contact name");
		updateRequest.setPhoneContact("1213131");
		updateRequest.setName("test name");
		updateRequest.setShortName("TEST");
		updateRequest.setUrl("update url");

		ServicePlatform updateServicePlatform = spService.updateServicePlatform(updateRequest);
		assertEquals("test contact email", updateServicePlatform.getContactEmail());
		assertEquals("test contact name", updateServicePlatform.getContactName());
		assertEquals("1213131", updateServicePlatform.getContactPhone());
		assertEquals("test name", updateServicePlatform.getName());
		assertEquals("TEST", updateServicePlatform.getShortName());
		assertEquals("update url", updateServicePlatform.getUrl());

		Optional<ServicePlatform> findById = spRepository.findById(TestContants.SERVICE_PLATFORM_ID);
		assertEquals(true, findById.isPresent());
		ServicePlatform result = findById.get();
		assertEquals("test contact email", result.getContactEmail());
		assertEquals("test contact name", result.getContactName());
		assertEquals("1213131", result.getContactPhone());
		assertEquals("test name", result.getName());
		assertEquals("TEST", result.getShortName());
		assertEquals("update url", result.getUrl());

	}

	@Test
	public void testUpdateServicePlatformThrowErrorNameExist() {
		addServicePlatform();
		ServicePlatformUpdateRequest updateRequest = new ServicePlatformUpdateRequest();
		updateRequest.setId(TestContants.SERVICE_PLATFORM_ID);
		updateRequest.setEmailContact("test contact email");
		updateRequest.setNameContact("test contact name");
		updateRequest.setPhoneContact("1213131");
		updateRequest.setName("STYL SOLUTIONS TEST");
		updateRequest.setShortName("TEST");
		updateRequest.setUrl("update url");
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			spService.updateServicePlatform(updateRequest);
		});

		assertEquals(ErrorCode.SERVICE_PLATFORM_NAME_EXISTED.getErrorCode(), exception.getErrorCode());

	}

	@Test
	public void testUpdateServicePlatformThrowErrorShortNameExist() {
		addServicePlatform();
		ServicePlatformUpdateRequest updateRequest = new ServicePlatformUpdateRequest();
		updateRequest.setId(TestContants.SERVICE_PLATFORM_ID);
		updateRequest.setEmailContact("test contact email");
		updateRequest.setNameContact("test contact name");
		updateRequest.setPhoneContact("1213131");
		updateRequest.setName("test name");
		updateRequest.setShortName("STYL_TEST");
		updateRequest.setUrl("update url");
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			spService.updateServicePlatform(updateRequest);
		});

		assertEquals(ErrorCode.SERVICE_PLATFORM_SHORTNAME_EXISTED.getErrorCode(), exception.getErrorCode());

	}

	@Test
	public void testUpdateServicePlatformThrowErrorIdNotFound() {
		ServicePlatformUpdateRequest updateRequest = new ServicePlatformUpdateRequest();
		updateRequest.setId(1232131314);
		updateRequest.setEmailContact("test contact email");
		updateRequest.setNameContact("test contact name");
		updateRequest.setPhoneContact("1213131");
		updateRequest.setName("test name");
		updateRequest.setShortName("STYL_TEST");
		updateRequest.setUrl("update url");
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			spService.updateServicePlatform(updateRequest);
		});

		assertEquals(ErrorCode.SERVICE_PLATFORM_NOT_FOUND.getErrorCode(), exception.getErrorCode());

	}

	@Test
	public void testRemoveServicePlatform() {
		ServicePlatform sp = addServicePlatform();
		Optional<ServicePlatform> findByIdBeforeRm = spRepository.findById(sp.getId());
		assertEquals(true, findByIdBeforeRm.isPresent());

		// remove sp
		spService.removeServicePlatform(sp.getId());
		Optional<ServicePlatform> findByIdAfterRm = spRepository.findById(sp.getId());
		assertEquals(false, findByIdAfterRm.isPresent());

	}

	@Test
	public void testPagination() {
		addServicePlatform(1, 10);

		// Filter all
		Pagination<ServicePlatformPortalResponse> result_1 = spService.listServicePlatform(null, null, null,null, null, null, null,
				null, null, "id", "ASC", 0, 2);
		assertEquals(1, result_1.getPage());
		assertEquals(2, result_1.getPageSize());
		assertEquals(11, result_1.getTotal());

		// Filter by name STYL SOLUTIONS TEST 10
		Pagination<ServicePlatformPortalResponse> result_2 = spService.listServicePlatform(null, "STYL SOLUTIONS TEST 10",
				null, null, null, null, null, null, null, "id", "ASC", 0, 2);
		assertEquals(1, result_2.getTotal());

		// Filter by name STYL SOLUTIONS TEST
		Pagination<ServicePlatformPortalResponse> result_3 = spService.listServicePlatform(null, "STYL SOLUTIONS TEST", null,
				null, null, null, null, null, null, "id", "ASC", 0, 2);
		assertEquals(10, result_3.getTotal());

		// Filter by short name STYL_TEST
		Pagination<ServicePlatformPortalResponse> result_4 = spService.listServicePlatform("STYL_TEST", null, null, null, null,
				null, null, null, null, "id", "ASC", 0, 2);
		assertEquals(10, result_4.getTotal());

		// Filter by short name STYL_TEST 2
		Pagination<ServicePlatformPortalResponse> result_5 = spService.listServicePlatform("STYL_TEST 2", null, null, null, null,
				null, null, null, null, "id", "ASC", 0, 2);
		assertEquals(1, result_5.getTotal());

		// Filter by contact phone
		Pagination<ServicePlatformPortalResponse> result_6 = spService.listServicePlatform(null, null, null, null, null,
				"phone", null, null, null, "id", "ASC", 0, 2);
		assertEquals(10, result_6.getTotal());

		// Filter by contact name
		Pagination<ServicePlatformPortalResponse> result_7 = spService.listServicePlatform(null, null, null, "name", null,
				null, null, null, null, "id", "ASC", 0, 2);
		assertEquals(10, result_7.getTotal());

		// Filter by contact email
		Pagination<ServicePlatformPortalResponse> result_8 = spService.listServicePlatform(null, null, null, null, "email 5",
				null, null, null, null, "id", "ASC", 0, 2);
		assertEquals(1, result_8.getTotal());

		// Filter by url
		Pagination<ServicePlatformPortalResponse> result_9 = spService.listServicePlatform(null, null,
				"https://styl.solutions/", null, null, null, null, null, null, "id", "ASC", 0, 2);
		assertEquals(10, result_9.getTotal());

	}

	private void addServicePlatform(int startSp, int numberSP) {
		for (int i = startSp; i <= numberSP; i++) {
			ServicePlatform newSp = new ServicePlatform();
			newSp.setName("STYL SOLUTIONS TEST " + i);
			newSp.setShortName("STYL_TEST " + i);
			newSp.setContactEmail("email " + i);
			newSp.setContactName("name " + i);
			newSp.setContactPhone("phone " + i);
			newSp.setCreatedTime(123456789L);
			newSp.setCreatedBy("Test");
			newSp.setUrl("https://styl.solutions/" + i);
			entityManager.persist(newSp);
			entityManager.flush();
		}
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	private Software addSoftware(ServicePlatform spId1, int softwareId) {
		Software newSoftware = new Software();
		newSoftware.setName("Name " + softwareId);
		newSoftware.setDescription("Description " + softwareId);
		newSoftware.setPackageName("com.package." + softwareId);
		newSoftware.setServicePlatform(spId1);
		entityManager.persist(newSoftware);
		entityManager.flush();
		return newSoftware;
	}

	private ServicePlatform addServicePlatform() {
		ServicePlatform newSp = new ServicePlatform();
		newSp.setName("STYL SOLUTIONS TEST");
		newSp.setShortName("STYL_TEST");
		newSp.setCreatedTime(123456789L);
		newSp.setCreatedBy("Test");
		newSp.setUrl("https://styl.solutions");
		entityManager.persist(newSp);
		entityManager.flush();
		return newSp;
	}
}
