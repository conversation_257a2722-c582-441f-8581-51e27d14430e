/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.service.platform;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Calendar;
import java.util.LinkedHashMap;
import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.device.management.auth.service.platform.AbstractControllerTest;
import com.styl.device.management.auth.service.platform.PreConditionUtil;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.persistence.api.key.ApiKey;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.rest.portal.admin.service.platform.AddApiKeyRequest;

/**
 * <AUTHOR> Yee
 *
 */
@Transactional
public class ApiKeyControllerTest extends AbstractControllerTest {

	@PersistenceContext
	private EntityManager entityManager;

	@Test
	public void testGetListApiKey() throws Exception {

		String userId = "user";

		Calendar calTwoDayBef = Calendar.getInstance();
		calTwoDayBef.add(Calendar.DAY_OF_YEAR, -2);
		Calendar calYesterday = Calendar.getInstance();
		calYesterday.add(Calendar.DAY_OF_YEAR, -1);

		Calendar calTwoMonthAft = Calendar.getInstance();
		calTwoMonthAft.add(Calendar.MONTH, 2);
		Calendar calThreeMonthAft = Calendar.getInstance();
		calThreeMonthAft.add(Calendar.MONTH, 3);

		ServicePlatform sp1 = PreConditionUtil.addServicePlatform(entityManager, "test");

		ApiKey apiKey1 = PreConditionUtil.addApiKey(entityManager, sp1.getId(), userId);
		ApiKey apiKey2 = PreConditionUtil.addApiKey(entityManager, sp1.getId(), userId);
		apiKey2.setEnabled(false);
		ApiKey apiKey3 = PreConditionUtil.addApiKey(entityManager, sp1.getId(), userId);
		apiKey3.setCreatedTime(calYesterday.getTimeInMillis());
		ApiKey apiKey4 = PreConditionUtil.addApiKey(entityManager, sp1.getId(), userId);
		ApiKey apiKey5 = PreConditionUtil.addApiKey(entityManager, sp1.getId(), userId);
		apiKey5.setExpiredTime(calThreeMonthAft.getTimeInMillis());
		ApiKey apiKey6 = PreConditionUtil.addApiKey(entityManager, sp1.getId(), userId);
		apiKey6.setExpiredTime(calTwoMonthAft.getTimeInMillis());

		MockHttpServletResponse resultSort = this.mvc
				.perform(get("/api/management/service-platform/" + sp1.getId()
						+ "/apikey/list?sort=expiredTime&ordering=desc"))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		List<LinkedHashMap<String, Object>> dataSort = getObject(resultSort.getContentAsString(), List.class);
		assertEquals(6, dataSort.size());
		assertEquals(apiKey5.getId(), Long.valueOf((Integer) dataSort.get(0).get("id")));
		assertEquals(apiKey6.getId(), Long.valueOf((Integer) dataSort.get(1).get("id")));
		assertEquals(apiKey4.getId(), Long.valueOf((Integer) dataSort.get(2).get("id")));
		assertEquals(apiKey3.getId(), Long.valueOf((Integer) dataSort.get(3).get("id")));
		assertEquals(apiKey2.getId(), Long.valueOf((Integer) dataSort.get(4).get("id")));
		assertEquals(apiKey1.getId(), Long.valueOf((Integer) dataSort.get(5).get("id")));
		
		MockHttpServletResponse resultSortDefault = this.mvc
				.perform(get("/api/management/service-platform/" + sp1.getId()
						+ "/apikey/list"))
				.andExpect(status().isOk()).andReturn().getResponse();
		@SuppressWarnings("unchecked")
		List<LinkedHashMap<String, Object>> dataSortDefault = getObject(resultSortDefault.getContentAsString(), List.class);
		assertEquals(6, dataSortDefault.size());
		assertEquals(apiKey5.getId(), Long.valueOf((Integer) dataSortDefault.get(0).get("id")));
		assertEquals(apiKey6.getId(), Long.valueOf((Integer) dataSortDefault.get(1).get("id")));
		assertEquals(apiKey4.getId(), Long.valueOf((Integer) dataSortDefault.get(2).get("id")));
		assertEquals(apiKey3.getId(), Long.valueOf((Integer) dataSortDefault.get(3).get("id")));
		assertEquals(apiKey2.getId(), Long.valueOf((Integer) dataSortDefault.get(4).get("id")));
		assertEquals(apiKey1.getId(), Long.valueOf((Integer) dataSortDefault.get(5).get("id")));

	}

	public <T> T getObject(String data, Class<T> type) {
		T target = null;
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			target = objectMapper.readValue(data, type);
			// JAXBUtils.marshal(target);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return target;
	}

	@Test
	public void testAddApiKey() throws Exception {
		Calendar calTwoMonthAft = Calendar.getInstance();
		calTwoMonthAft.add(Calendar.MONTH, 2);

		ServicePlatform sp1 = PreConditionUtil.addServicePlatform(entityManager, "test");

		AddApiKeyRequest request = new AddApiKeyRequest();
		request.setExpiredTime(calTwoMonthAft.getTimeInMillis());

		MockHttpServletResponse resultAdd = this.mvc
				.perform(post("/api/management/service-platform/" + sp1.getId() + "/apikey/add")
						.contentType(MediaType.APPLICATION_JSON).content(convertObjectToJson(request)))
				.andExpect(status().isOk()).andReturn().getResponse();

		ApiKey dataAdd = getObject(resultAdd.getContentAsString(), ApiKey.class);
		assertNotNull(dataAdd);
		assertNotNull(dataAdd.getId());
		assertNotNull(dataAdd.getSecretKey());
		assertEquals(calTwoMonthAft.getTimeInMillis(), dataAdd.getExpiredTime());

		List<ApiKey> keys = getByServicePlatformId(sp1.getId());
		assertEquals(1, keys.size());

	}

	private List<ApiKey> getByServicePlatformId(Integer servicePlatformId) {
		TypedQuery<ApiKey> query = entityManager.createQuery(
				"Select k FROM ApiKey k WHERE " + "k.servicePlatformId=:servicePlatformId order by k.id ASC",
				ApiKey.class);
		query.setParameter("servicePlatformId", servicePlatformId);
		return query.getResultList();
	}

	public String convertObjectToJson(Object object) throws JsonProcessingException {
		if (object == null) {
			return null;
		}
		ObjectMapper mapper = new ObjectMapper();
		return mapper.writeValueAsString(object);
	}

	@Test
	public void testAddApiKey_expired() throws Exception {
		Calendar calOneMonthBef = Calendar.getInstance();
		calOneMonthBef.add(Calendar.MONTH, -1);

		ServicePlatform sp1 = PreConditionUtil.addServicePlatform(entityManager, "test");

		AddApiKeyRequest request = new AddApiKeyRequest();
		request.setExpiredTime(calOneMonthBef.getTimeInMillis());

		this.mvc.perform(post("/api/management/service-platform/" + sp1.getId() + "/apikey/add")
				.contentType(MediaType.APPLICATION_JSON).content(convertObjectToJson(request)))
				.andExpect(MockMvcResultMatchers.jsonPath("errors").exists())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isNotEmpty())
				.andExpect(MockMvcResultMatchers.jsonPath("errors[0].errorCode").value(ErrorCode.API_KEY_EXPIRED.getErrorCode()));

	}

	@Test
	public void testDeleteApiKey() throws Exception {

		ServicePlatform sp1 = PreConditionUtil.addServicePlatform(entityManager, "test");

		ApiKey apiKey = PreConditionUtil.addApiKey(entityManager, sp1.getId(), "user");

		this.mvc.perform(delete("/api/management/service-platform/" + sp1.getId() + "/apikey/delete/" + apiKey.getId()))
				.andExpect(status().isOk()).andReturn().getResponse();

	}

	@Test
	public void testDeleteApiKey_notfound() throws Exception {
		ServicePlatform sp1 = PreConditionUtil.addServicePlatform(entityManager, "test");

		this.mvc.perform(delete("/api/management/service-platform/" + sp1.getId() + "/apikey/delete/1"))
				.andExpect(MockMvcResultMatchers.jsonPath("errors").exists())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isNotEmpty())
				.andExpect(MockMvcResultMatchers.jsonPath("errors[0].errorCode").value(ErrorCode.API_KEY_NOT_FOUND.getErrorCode()));

	}

}
