/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.platform;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.auth.service.platform.PreConditionUtil;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.service.platform.ServicePlatformRepository;
import com.styl.device.management.persistence.service.platform.ServicePlatformService;
import com.styl.device.management.persistence.software.SoftwareRepository;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformAddRequest;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformPortalResponse;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformUpdateRequest;
import com.styl.device.management.utils.Pagination;
import com.styl.device.management.utils.Utils;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest(properties = { "spring.sql.init.mode=never", "security.api.enabled=false" })
@ActiveProfiles("test")
@AutoConfigureMockMvc(addFilters = false)
public class ServicePlatformControllerTest {

	@Autowired
	protected MockMvc mockMvc;

	@Autowired
	private ServicePlatformRepository spRepository;

	@MockBean
	ServicePlatformService spService;

	@Autowired
	SoftwareRepository svRepository;

	@Test
	public void testListServicePlatform_Success() throws Exception {
		Pageable paging = PageRequest.of(0, 10);
		Page<ServicePlatform> findAll = spRepository.findAll(paging);
		List<ServicePlatformPortalResponse> sp = findAll.stream().map(s -> {
			return new ServicePlatformPortalResponse(s);
		}).collect(Collectors.toList());

		Pagination<ServicePlatformPortalResponse> expectResult = new Pagination<ServicePlatformPortalResponse>(
				findAll.getTotalElements(), 1, 10, sp);

		when(spService.listServicePlatform(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(),
				anyInt(), anyInt())).thenReturn(expectResult);
		MockHttpServletResponse response = mockMvc
				.perform(get("/api/management/service-platform").accept(MediaType.APPLICATION_JSON)).andReturn()
				.getResponse();

		assertEquals(200, response.getStatus());
		assertEquals(Utils.mapToJson(expectResult), response.getContentAsString());
	}

	@Test
	public void testAddServicePlatform_Success() throws Exception {
		ServicePlatformAddRequest addRequest = new ServicePlatformAddRequest();
		addRequest.setName("STYL SOLUTIONS CONTROLLER ADD");
		addRequest.setShortName("STYL");
		addRequest.setUrl("https://example.com");
		addRequest.setEmailContact("<EMAIL>");
		addRequest.setNameContact("Dino Lam");
		addRequest.setPhoneContact("121313131");

		ServicePlatform mockExpect = new ServicePlatform(addRequest);
		mockExpect.setId(789);
		when(spService.addServicePlatform(any())).thenReturn(mockExpect);

		MockHttpServletResponse response = mockMvc.perform(post("/api/management/service-platform")
				.contentType(MediaType.APPLICATION_JSON).content(Utils.mapToJson(addRequest))).andReturn()
				.getResponse();

		assertEquals(200, response.getStatus());
	}

	@Test
	public void testAddServicePlatform_Name_Invalid() throws Exception {
		ServicePlatformAddRequest addRequest = new ServicePlatformAddRequest();
		addRequest.setName(" ");
		addRequest.setShortName("STYL ADD");
		addRequest.setUrl("https://example.com");
		addRequest.setEmailContact("<EMAIL>");
		addRequest.setPhoneContact("Dino Lam");
		addRequest.setPhoneContact("121313131");

		mockMvc.perform(post("/api/management/service-platform").contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(addRequest))).andExpect(MockMvcResultMatchers.jsonPath("errors").exists())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isNotEmpty()).andExpect(MockMvcResultMatchers
						.jsonPath("errors[0].errorCode").value(ErrorCode.INVALID_FIELD.getErrorCode()));
	}

	@Test
	public void testAddServicePlatform_ShortName_Invalid() throws Exception {
		ServicePlatformAddRequest addRequest = new ServicePlatformAddRequest();
		addRequest.setName("STYL SOLUTIONS ADD");
		addRequest.setShortName(" ");
		addRequest.setUrl("https://example.com");
		addRequest.setEmailContact("<EMAIL>");
		addRequest.setNameContact("Dino Lam");
		addRequest.setPhoneContact("121313131");

		mockMvc.perform(post("/api/management/service-platform").contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(addRequest))).andExpect(MockMvcResultMatchers.jsonPath("errors").exists())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isNotEmpty()).andExpect(MockMvcResultMatchers
						.jsonPath("errors[0].errorCode").value(ErrorCode.INVALID_FIELD.getErrorCode()));
	}

	@Test
	public void testAddServicePlatform_URL_Invalid() throws Exception {
		ServicePlatformAddRequest addRequest = new ServicePlatformAddRequest();
		addRequest.setName("STYL SOLUTIONS ADD");
		addRequest.setShortName("STYL ADD");
		addRequest.setUrl(" ");
		addRequest.setEmailContact("<EMAIL>");
		addRequest.setPhoneContact("Dino Lam");
		addRequest.setPhoneContact("121313131");

		mockMvc.perform(post("/api/management/service-platform").contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(addRequest))).andExpect(MockMvcResultMatchers.jsonPath("errors").exists())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isNotEmpty()).andExpect(MockMvcResultMatchers
						.jsonPath("errors[0].errorCode").value(ErrorCode.INVALID_FIELD.getErrorCode()));
	}

	@Test
	public void testUpdateServicePlatform_Success() throws Exception {
		ServicePlatformUpdateRequest updateRequest = new ServicePlatformUpdateRequest();
		updateRequest.setId(8910);
		updateRequest.setEmailContact("<EMAIL>");
		updateRequest.setNameContact("Mr.test");
		updateRequest.setPhoneContact("2121313131");
		updateRequest.setName("Test");
		updateRequest.setShortName("TST");
		updateRequest.setUrl("Url");

		ServicePlatform mockExpect = new ServicePlatform(updateRequest);
		when(spService.updateServicePlatform(any())).thenReturn(mockExpect);

		MockHttpServletResponse response = mockMvc.perform(put("/api/management/service-platform")
				.contentType(MediaType.APPLICATION_JSON).content(Utils.mapToJson(updateRequest))).andReturn()
				.getResponse();

		assertEquals(200, response.getStatus());
	}

	@Test
	public void testUpdateServicePlatform_InvalidEmail() throws Exception {
		ServicePlatformUpdateRequest updateRequest = new ServicePlatformUpdateRequest();
		updateRequest.setId(8910);
		updateRequest.setEmailContact("dino.lam invalid");
		updateRequest.setNameContact("Mr.test");
		updateRequest.setPhoneContact("2121313131");
		updateRequest.setName("Test");
		updateRequest.setShortName("TST");
		updateRequest.setUrl("Url");

		ServicePlatform mockExpect = new ServicePlatform(updateRequest);
		when(spService.updateServicePlatform(any())).thenReturn(mockExpect);

		mockMvc.perform(put("/api/management/service-platform").contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(updateRequest))).andExpect(MockMvcResultMatchers.jsonPath("errors").exists())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isNotEmpty()).andExpect(MockMvcResultMatchers
						.jsonPath("errors[0].errorCode").value(ErrorCode.INVALID_FIELD.getErrorCode()));
	}

	@Test
	public void testUpdateServicePlatform_InvalidId() throws Exception {
		ServicePlatformUpdateRequest updateRequest = new ServicePlatformUpdateRequest();
		updateRequest.setId(null);
		updateRequest.setEmailContact("<EMAIL>");
		updateRequest.setNameContact("Mr.test");
		updateRequest.setPhoneContact("212131 3131");
		updateRequest.setName("Test");
		updateRequest.setShortName("TST");
		updateRequest.setUrl("Url");

		ServicePlatform mockExpect = new ServicePlatform(updateRequest);
		when(spService.updateServicePlatform(any())).thenReturn(mockExpect);

		mockMvc.perform(put("/api/management/service-platform").contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(updateRequest))).andExpect(MockMvcResultMatchers.jsonPath("errors").exists())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isNotEmpty()).andExpect(MockMvcResultMatchers
						.jsonPath("errors[0].errorCode").value(ErrorCode.INVALID_FIELD.getErrorCode()));
	}

	@Test
	public void testRemoveServicePlatform_Success() throws Exception {

		when(spService.removeServicePlatform(any())).thenReturn(true);

		MockHttpServletResponse response = mockMvc
				.perform(delete("/api/management/service-platform/1").contentType(MediaType.APPLICATION_JSON))
				.andReturn().getResponse();

		assertEquals(200, response.getStatus());
	}
	
	@PersistenceContext
	private EntityManager entityManager;
	
	
	@Test
	@Transactional
	public void testRemoveServicePlatformWithNotificationSetting_Success() throws Exception {

		ServicePlatform sp = PreConditionUtil.addServicePlatform(entityManager, "testsp");
		PreConditionUtil.addSpNotificationSetting(entityManager, "test", sp);

		MockHttpServletResponse response = mockMvc
				.perform(delete("/api/management/service-platform/"+sp.getId()).contentType(MediaType.APPLICATION_JSON))
				.andReturn().getResponse();

		assertEquals(200, response.getStatus());
	}
}
