/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management;

/**
 * <AUTHOR> Lam
 *
 */
public class TestContants {

	public static final String SOFTWARE_NAME = "Caribbean-POS";
	public static final String SOFTWARE_PACKAGE_NAME = "com.styl.caribbean.pos.amex";
	public static final String SOFTWARE_PACKAGE_NAME_2 = "com.styl.dms.test";
	public static final String SOFTWARE_PACKAGE_NAME_TEST_REMOVE = "test.remove";
	public static final String SOFTWARE_VERSION_0_1_7 = "0.1.7";
	public static final String SOFTWARE_VERSION_0_1_8 = "0.1.8";
	
	public static final Integer SERVICE_PLATFORM_ID = 1;
	public static final String SERVICE_PLATFORM_NAME = "STYL SOLUTION";
	public static final String SERVICE_PLATFORM_SHORTNAME = "STYL";
	
	public static final String DEVICE_UID = "T-0001";
	public static final String DEVICE_HARDWARE_ID = "1212131deafa";
	public static final String DEVICE_HARDWARE_ID_REGISTED_ON_PORTAL = "hardware_id_registed_on_portal";
	public static final String DEVICE_HARDWARE_ID_REGISTER = "hardware_id_register";
	public static final String MODEL = "E700";
	
	public static final String TAG = "Tag constant";
	public static final String TEST_TOPIC_NAME = "DMS.STYL";
	
	
}
