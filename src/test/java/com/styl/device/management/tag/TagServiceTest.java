/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.tag;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.stream.Stream;

import org.junit.experimental.theories.Theories;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.TestContants;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.tag.TagRepository.TagWithCountDevice;
import com.styl.device.management.persistence.tag.TagService;
import com.styl.device.management.rest.portal.admin.tag.TagAddRequest;
import com.styl.device.management.rest.portal.admin.tag.TagResponse;
import com.styl.device.management.rest.portal.admin.tag.TagUpdateRequest;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TagServiceTest {

	@Autowired
	TagService tagService;

	@ParameterizedTest
	@MethodSource("provideTagForAdd")
	void addTagTest(TagAddRequest tagAdd, TagResponse expected) {

		TagResponse tagResponse = tagService.addTag(tagAdd);
		assertEquals(expected.getName(), tagResponse.getName());
		assertEquals(expected.getDescription(), tagResponse.getDescription());
	}

	private static Stream<Arguments> provideTagForAdd() {
		TagAddRequest tag_1 = new TagAddRequest("Tag 1", "Decription Tag 1");
		TagAddRequest tag_2 = new TagAddRequest("Tag 2", "Decription Tag 2");
		TagAddRequest tag_3 = new TagAddRequest("Tag 3", "Decription Tag 3");
		TagAddRequest tag_4 = new TagAddRequest("Tag 4", "Decription Tag 4");

		return Stream.of(Arguments.of(tag_1, new TagResponse(tag_1)), Arguments.of(tag_2, new TagResponse(tag_2)),
				Arguments.of(tag_3, new TagResponse(tag_3)), Arguments.of(tag_4, new TagResponse(tag_4)));
	}

	@Test
	void addTagThrowError() {
		TagAddRequest tagAdd = new TagAddRequest(TestContants.TAG, "Decription Tag 1");
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			tagService.addTag(tagAdd);
		});

		assertEquals(ErrorCode.TAG_ALREADY_EXISTED.getCode(), exception.getCode());
		assertEquals(ErrorCode.TAG_ALREADY_EXISTED.getErrorCode(), exception.getErrorCode());
	}

	@Test
	void testUpdateThrowError_WhenTagNotFound() {
		TagUpdateRequest tagUpdate = new TagUpdateRequest(9999, "Tag Update", "Tag Update");
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			tagService.updateTag(tagUpdate);
		});

		assertEquals(ErrorCode.TAG_NOT_FOUND.getCode(), exception.getCode());
		assertEquals(ErrorCode.TAG_NOT_FOUND.getErrorCode(), exception.getErrorCode());
	}

	@Test
	void testUpdateThrowError_WhenNameAlreadyExisted() {
		TagAddRequest tag_1 = new TagAddRequest("Tag 1", "Decription Tag 1");
		TagAddRequest tag_2 = new TagAddRequest("Tag 2", "Decription Tag 2");
		TagResponse tagResponse_1 = tagService.addTag(tag_1);
		tagService.addTag(tag_2);

		TagUpdateRequest tagUpdate = new TagUpdateRequest(tagResponse_1.getId(), "Tag 2", "Tag Update");
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			tagService.updateTag(tagUpdate);
		});

		assertEquals(ErrorCode.TAG_NAME_ALREADY_EXISTED.getCode(), exception.getCode());
		assertEquals(ErrorCode.TAG_NAME_ALREADY_EXISTED.getErrorCode(), exception.getErrorCode());
	}

	@Test
	void testUpdateTag() {
		TagAddRequest tag_1 = new TagAddRequest("Tag 1", "Decription Tag 1");
		TagResponse tagResponse_1 = tagService.addTag(tag_1);
		TagUpdateRequest tagUpdate = new TagUpdateRequest(tagResponse_1.getId(), "Tag Update",
				"Tag Description Update");

		assertEquals("Tag Update", tagUpdate.getName());
		assertEquals("Tag Description Update", tagUpdate.getDescription());
	}

	@Test
	void testGetTagWithPagination() {

		Pagination<TagWithCountDevice> pagination = tagService.getPagination(null, null, "id", "DESC", 0, 10);
		assertEquals(2, pagination.getTotal());

		TagAddRequest tag_1 = new TagAddRequest("Tag 1", "Decription Tag 1");
		TagAddRequest tag_2 = new TagAddRequest("Tag 2", "Decription Tag 2");
		TagAddRequest tag_3 = new TagAddRequest("Tag 3", "Decription Tag 3");
		TagAddRequest tag_4 = new TagAddRequest("Tag 4", "Decription Tag 4");
		tagService.addTag(tag_1);
		tagService.addTag(tag_2);
		tagService.addTag(tag_3);
		tagService.addTag(tag_4);

		Pagination<TagWithCountDevice> pagination_2 = tagService.getPagination(null, null, "id", "DESC", 0, 10);
		assertEquals(6, pagination_2.getTotal());

		// filter by name
		Pagination<TagWithCountDevice> pagination_3 = tagService.getPagination("Tag 1", null, "id", "DESC", 0, 10);
		assertEquals(1, pagination_3.getTotal());

		// filter by description
		Pagination<TagWithCountDevice> pagination_4 = tagService.getPagination(null, "Decription Tag 2", "id", "DESC",
				0, 10);
		assertEquals(1, pagination_4.getTotal());
	}

	@Test
	void testRemoveTag() {
		TagAddRequest tag_1 = new TagAddRequest("Tag 1", "Decription Tag 1");
		TagResponse addTag = tagService.addTag(tag_1);

		Pagination<TagWithCountDevice> pagination_1 = tagService.getPagination(null, null, "id", "DESC", 0, 10);
		assertEquals(3, pagination_1.getTotal());

		tagService.removeTag(addTag.getId());

		Pagination<TagWithCountDevice> pagination_2 = tagService.getPagination(null, null, "id", "DESC", 0, 10);
		assertEquals(2, pagination_2.getTotal());

	}
}
