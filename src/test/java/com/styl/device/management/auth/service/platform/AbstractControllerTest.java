package com.styl.device.management.auth.service.platform;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.log;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.json.JsonParseException;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.device.management.DeviceManageServiceApplication;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.Filter;

@SpringBootTest(classes = DeviceManageServiceApplication.class, properties = { "spring.sql.init.mode=never" })
@ActiveProfiles("test")
@WebAppConfiguration
public abstract class AbstractControllerTest {

	@Autowired
	WebApplicationContext webApplicationContext;

	protected MockMvc mvc;

	@Autowired
	private ServicePlatformAuthenticationFilter filter;

	@PostConstruct
	public void init() {
		mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).addFilters((Filter) filter).alwaysDo(log())
				.build();
	}

	protected String mapToJson(Object obj) throws JsonProcessingException {
		ObjectMapper objectMapper = new ObjectMapper();
		return objectMapper.writeValueAsString(obj);
	}

	protected <T> T mapFromJson(String json, Class<T> clazz)
			throws JsonParseException, JsonMappingException, IOException {

		ObjectMapper objectMapper = new ObjectMapper();
		return objectMapper.readValue(json, clazz);
	}
}