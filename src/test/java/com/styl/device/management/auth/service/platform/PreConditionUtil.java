/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.auth.service.platform;

import java.util.Set;

import jakarta.persistence.EntityManager;

import com.styl.device.management.persistence.api.key.ApiKey;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.service.platform.notification.setting.ServicePlatformNotificationSetting;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.software.update.mode.UpdateMode;
import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformAddRequest;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareVersionAddRequest;
import com.styl.device.management.service.platform.event.publisher.event.common.EventVersion;
import com.styl.device.management.utils.Utils;

/**
 * <AUTHOR> Yee
 *
 */
public class PreConditionUtil {

	public static final String API_KEY = "DMS-ApiKey";
	public static final String NONCE = "DMS-Nonce";
	public static final String SIGNATURE = "DMS-Signature";

	public static ApiKey addApiKey(EntityManager entityManager, Integer servicePlatformId, String userId) {
		return add(entityManager, new ApiKey(servicePlatformId, Utils.generateToken(64), Utils.generateToken(128),
				System.currentTimeMillis() + 120000, userId));
	}

	public static ApiKey add(EntityManager entityManager, ApiKey apiKey) {
		entityManager.persist(apiKey);
		entityManager.flush();
		return apiKey;
	}

	public static ServicePlatform addServicePlatform(EntityManager entityManager, String name) {
		ServicePlatformAddRequest request = new ServicePlatformAddRequest();
		request.setName(name);
		request.setShortName(name);
		request.setUrl(name + "url");
		request.setNameContact("Mr/Mrs. " + name);
		request.setEmailContact(name + "@test.solutions");
		request.setPhoneContact("0123456789");
		return add(entityManager, request);
	}

	public static ServicePlatform add(EntityManager entityManager, ServicePlatformAddRequest request) {
		ServicePlatform sp = new ServicePlatform(request);
		entityManager.persist(sp);
		entityManager.flush();
		return sp;
	}

	public static DeviceModel addDeviceModel(EntityManager entityManager, String name) {
		DeviceModel deviceModel = new DeviceModel(name, name + "desc");
		entityManager.persist(deviceModel);
		entityManager.flush();
		return deviceModel;
	}

	public static Device addDevice(EntityManager entityManager, DeviceModel model, ServicePlatform sp,
			String deviceId) {
		return add(entityManager, new Device(model, sp, deviceId + "-hardwareId", 1, deviceId + "-sim",
				deviceId + "-imei", System.currentTimeMillis() - 120000, System.currentTimeMillis() - 60000));
	}

	public static Device add(EntityManager entityManager, Device device) {
		entityManager.persist(device);
		entityManager.flush();
		return device;
	}

	public static Software addSoftware(EntityManager entityManager, ServicePlatform sp, Set<DeviceModel> dm, String name) {
		return add(entityManager, new Software(dm, sp, name + "-packageId", name, name + "-desc"));
	}

	public static Software add(EntityManager entityManager, Software software) {
		entityManager.persist(software);
		entityManager.flush();
		return software;
	}

	public static UpdateMode addUpdateMode(EntityManager entityManager, Integer id, String mode) {
		UpdateMode um = new UpdateMode();
		um.setModeId(id);
		um.setMode(mode);
		um.setDescription(mode + "-desc");
		entityManager.persist(um);
		entityManager.flush();
		return um;
	}

	public static SoftwareVersion addSoftwareVersion(EntityManager entityManager, Software software, String name,
			String version, UpdateMode updateMode) {
		SoftwareVersionAddRequest request = new SoftwareVersionAddRequest();
		request.setVersion(version);
		request.setPackageName(name + "-packageId");
		request.setFilePath("/" + name + "/" + version);
		request.setUpdateModeId(updateMode.getModeId());
		request.setReleaseNote(name + "-releaseNote");
		return add(entityManager, new SoftwareVersion(request, software, updateMode));
	}

	public static SoftwareVersion add(EntityManager entityManager, SoftwareVersion softwareVersion) {
		entityManager.persist(softwareVersion);
		entityManager.flush();
		return softwareVersion;
	}

	public static SoftwarePackages addSoftwarePackages(EntityManager entityManager, Device device, Software software,
			SoftwareVersion sv, Integer state, String user, Integer updateMode) {
		SoftwarePackages sp = new SoftwarePackages();
		sp.setSoftware(software);
		sp.setDevice(device);
		sp.setSoftwareVersion(sv);
		sp.setState(state);
		sp.setAssignedBy(user);
		sp.setAssignedTime(System.currentTimeMillis());
		sp.setUpdateMode(updateMode);
		entityManager.persist(sp);
		entityManager.flush();
		return sp;
	}

	public static ServicePlatformNotificationSetting addSpNotificationSetting(EntityManager entityManager, String topic,
			ServicePlatform sp) {
		ServicePlatformNotificationSetting ns = new ServicePlatformNotificationSetting();
		ns.setEventTopic(topic);
		ns.setId(sp.getId());
		ns.setEnabledWebhook(true);
		ns.setWebhookUrl("http://testurl");
		ns.setEventVersion(EventVersion.V1.name());
		ns.setCreatedTime(System.currentTimeMillis());
		ns.setCreatedBy("");
		entityManager.persist(ns);
		entityManager.flush();
		return ns;
	}

}
