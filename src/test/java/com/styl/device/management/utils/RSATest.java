/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
public class RSATest {

	private static final Logger logger = LoggerFactory.getLogger(RSATest.class);

	@Test
	void generateAESKey() {
		byte[] aesKey = AESUtils.generateSymmetricKey();
		assertEquals(32, aesKey.length);
	}

	@Test
	void testEncryptAndDecryptFlow() {
		String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz2MdqtTa63UfVJTWwRrAWn4HxrEYzp375qn6av+793lwDJRbGiTVdNjiAg682ecd3amwOqIciSAGFU8x91RmN46RIBEUohupbgh/uARI99Ak3QyE+TQBMXvDKF3hkG/26qJikFelC1+CSlDKIGpUHtIQw6/nAIVIBUJjZWorpfixyUpotjK+/Z7gXKp7dXX+EzRLmVFTSwPez4gszntSvy7MFONinsXKnHu8QD7rSdrS9UrNu8GxNllfwsJvGtojVJLJzpA5NVvIxvrt2s3B8hnLFVj6VW7ka8JCw2Y7JYI1iiow4sqcVIw1sM5CXe9TovnojnUu8qvlNZ/sn0VvowIDAQAB";
		String privateKey = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDPYx2q1NrrdR9UlNbBGsBafgfGsRjOnfvmqfpq/7v3eXAMlFsaJNV02OICDrzZ5x3dqbA6ohyJIAYVTzH3VGY3jpEgERSiG6luCH+4BEj30CTdDIT5NAExe8MoXeGQb/bqomKQV6ULX4JKUMogalQe0hDDr+cAhUgFQmNlaiul+LHJSmi2Mr79nuBcqnt1df4TNEuZUVNLA97PiCzOe1K/LswU42Kexcqce7xAPutJ2tL1Ss27wbE2WV/Cwm8a2iNUksnOkDk1W8jG+u3azcHyGcsVWPpVbuRrwkLDZjslgjWKKjDiypxUjDWwzkJd71Oi+eiOdS7yq+U1n+yfRW+jAgMBAAECggEAFfU9n+fXohSNk6wKrPmDIlGzyxLXOJE7qamXrRqUsVg3R+2xU6xQuYV5MJSU5FF3NRARa7PcZ5xvaHQxKynRhNJghU+pqpxZLSBTPY2emDZ+oLUBo00hTzb2a+C80Ek6kI9O9k+BVxnutqWlMRdw/q5pPf7y63gxLvVfhs5d/r2Ma5l/KJ6Rn+qq0MU5cmxX+LkejLR9dE1vqD2+OnsSRDrkoeHZuBea3QmXhoWp2E/WqA48lixkmVCb5oE2HNle1Zp5fI/ftDyn7Qd8u3tbukSrZgV8vpE1WFl2Raw/ERwcShOQtpfZK9x/D4j5D78jAzV1neWaqJm11VVfTIDt6QKBgQDuJx2IMRIVwS2o4xKX+op2KqbkX/dUt4c6FCaZBNGZa50JCBk5RNNA9tW97oXtz7SvnSdmN6W740o6PxX+6WD/uGSgbZDzJlrTQlTVrO21ADkmuEroprUCJR4oocwCF1M2ycLhzo0TuIvk3JylatVG8HdIzr4k7g2KCSWpcjR4lwKBgQDe7cWfZlUAzU9b30TMNrZ23IWb1v5YtpN7UCfSkj0e2Dr+rV3lP8dDkE64jO3FN2JsGd3yvB0JXjrkw1i+CPvcxtEwKnTEj3OdwvIaSIIv7t/hLKfY8G8kXhM8zj4ACu4ySLpvuTRuBTH5+1YBH88ErcVCUqa/tUkBvmGrG6T21QKBgQDnOvNhmMOGe9ppcJBNH+Xwddk1RDhQ+SFDMu2HBTa9T89ZRZO4FytGmGNP1pgTHujA2kW26bDxjO41P6uLpe2YXODXI4rpiwQEuxLDpPPoGh9jiOyyl6qIOqHdsbrzDNKvD5c4x+QSkczCmbE+q0wJZNFsz8+u67QZomf+7fQkYQKBgQCMyoIbD5fzr7bFARcEGjHWk6NWLsvn3zb5lD/yPyWWy11G5tr8I/GG/Caba3XnLflc6GFCIgf8mKR9QdpT+nyGjJIk50tjsG2GX05jxxNhjm2aWoeO/RpUT426w05o3H5fB4unb8JrEaDkjhVdFGe8PrrsDiL9xFJdZY1tcUIYGQKBgQDLoXJf7pHXQE5WtxedDaSM1Iv7RPW3BH+dDTVj9GiqLLiPP7jnrVnzxhk2elPAcYdfsTgg/2ycox6hI9EibY0+oKiwhMLzeUjCgulnaVSXhQeR6F0kX/gGOzYxrSWvCY+k6einx8AzoOI8PqEZ8ZRC6X2R96ijw5vupQfDroTJcA==";

		// 1. Generate Iv Parameter and AES key
		String ivPara = Utils.generateToken(16);
		byte[] aesKey = AESUtils.generateSymmetricKey();
		logger.debug("Iv Para: {}", ivPara);
		logger.debug("aesKey: {}", Base64.getEncoder().encodeToString(aesKey));
		logger.debug("Length AES: " + aesKey.length);
		logger.debug("-----------------------------------");

		// 2. add AES and IV
		byte[] ivParaBytes = ivPara.getBytes();
		byte[] exchangeKey = new byte[aesKey.length + ivParaBytes.length];
		System.arraycopy(aesKey, 0, exchangeKey, 0, aesKey.length);
		System.arraycopy(ivParaBytes, 0, exchangeKey, aesKey.length, ivParaBytes.length);

		logger.debug("Length of exchangeKey: " + exchangeKey.length);
		// 32 byte for key - 16 byte for iv parameter
		byte aes[] = Arrays.copyOfRange(exchangeKey, 0, aesKey.length);
		byte ivbeforeEcrypt[] = Arrays.copyOfRange(exchangeKey, aesKey.length, exchangeKey.length);
		logger.debug("Compare AES: {}", Arrays.equals(aesKey, aes));
		logger.debug("aesKey: {}", Base64.getEncoder().encodeToString(aes));
		logger.debug("Iv: {}", new String(ivbeforeEcrypt, StandardCharsets.UTF_8));
		logger.debug("exchangeKey: {}", exchangeKey);
		byte[] exKeyBytes = Base64.getEncoder().encode(exchangeKey);
		String exKey = new String(exKeyBytes);
		logger.debug("exchangeKey to string: {}", exKey);
		logger.debug("-----------------------------------");

		// 3. Encrypt by RSA
		String exchangedKey = RSAUtils.encrypt(publicKey, exKey, RSAUtils.RSA_OAEPWITHSHA256);

		logger.debug("--------------ENCRYPT---------------");

		// 4. Decrypt by RSA base64
		String decryptData = RSAUtils.decrypt(privateKey, exchangedKey, RSAUtils.RSA_OAEPWITHSHA256);

		logger.debug("--------------DECRYPT---------------");

		// decodeBase64
		String decodeBase64DataDecrypt = new String(Base64.getDecoder().decode(decryptData), StandardCharsets.UTF_8);
		logger.debug("Decrypt data: {}", decodeBase64DataDecrypt);
		logger.debug("-----------------------------------------");
		
		byte[] decode = decodeBase64DataDecrypt.getBytes(StandardCharsets.UTF_8);
		byte[] decodeBase64 = Base64.getDecoder().decode(decode);
		byte[] aesKeyAfterDecrypt = Arrays.copyOfRange(decodeBase64, 0, aesKey.length);
		byte[] iv = Arrays.copyOfRange(decodeBase64, aesKey.length, decodeBase64.length);
		String ivString = new String(iv, StandardCharsets.UTF_8);
		
		
		logger.debug("AES key: " + Base64.getEncoder().encodeToString(aesKeyAfterDecrypt));
		logger.debug("IV: {}", ivString);
		
		assertEquals(Base64.getEncoder().encodeToString(aesKey), Base64.getEncoder().encodeToString(aesKeyAfterDecrypt));
		assertEquals(ivPara, ivString);
	}

}
