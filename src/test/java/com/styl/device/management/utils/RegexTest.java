/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.regex.Pattern;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;
import org.junit.experimental.theories.Theories;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
public class RegexTest {

	private static Stream<Arguments> provideStringsForIsBlank() {
		return Stream.of(Arguments.of(null, true), Arguments.of("", true), Arguments.of("  ", true),
				Arguments.of("not blank", false));
	}

	@ParameterizedTest
	@MethodSource("provideStringsForIsBlank")
	void isBlank_ShouldReturnTrueForNullOrBlankStrings(String input, boolean expected) {
		assertEquals(expected, StringUtils.isBlank(input));
	}

	@ParameterizedTest
	@MethodSource("provideStringForTestRegex")
	void testRegex(String input, boolean expected) {
		// Java: \w is limited to [A-Za-z0-9_]
		Pattern pattern = Pattern.compile("^[\\w.]+");
		assertEquals(expected, pattern.matcher(input).matches());
	}

	@ParameterizedTest
	@MethodSource("provideStringForTestRegexAllowLetterAndDigitAndDot")
	void testRegexForCert(String input, boolean expected) {
		// Note that ^ and $ match the beginning and the end of a line.
		// When multiline is enabled, this can mean that one line matches,
		// but not the complete string.
		Pattern pattern = Pattern.compile("^[a-zA-Z0-9.]+$");
		assertEquals(expected, pattern.matcher(input).matches());
	}

	@ParameterizedTest
	@MethodSource("provideStringForTestRegexSoftwarePackageName")
	void testRegexForSoftwarePackageName(String input, boolean expected) {
		Pattern pattern = Pattern.compile("^[a-zA-Z0-9._-]+$");
		assertEquals(expected, pattern.matcher(input).matches());
	}

	@ParameterizedTest
	@MethodSource("provideStringForTestRegexShornameServicePlatform")
	void testRegexForShortNameServicePlatform(String input, boolean expected) {
		Pattern pattern = Pattern.compile("^[A-Z]+$");
		assertEquals(expected, pattern.matcher(input).matches());
	}

	private static Stream<Arguments> provideStringForTestRegex() {
		return Stream.of(Arguments.of("test", true), Arguments.of("test1", true), Arguments.of("styl.solutions", true),
				Arguments.of("test_1", true), Arguments.of("test_1_2", true), Arguments.of("test_1%", false),
				Arguments.of("test^", false), Arguments.of("test&", false), Arguments.of("test*", false),
				Arguments.of("test#", false), Arguments.of("test@", false), Arguments.of("test!", false),
				Arguments.of("a b c", false), Arguments.of("testfail$", false));
	}

	private static Stream<Arguments> provideStringForTestRegexAllowLetterAndDigitAndDot() {
		return Stream.of(Arguments.of("test", true), Arguments.of("test12", true), Arguments.of("test_1", false),
				Arguments.of("test_1_2", false), Arguments.of("test_1%", false), Arguments.of("test^", false),
				Arguments.of("test&", false), Arguments.of("test*", false), Arguments.of("test#", false),
				Arguments.of("test@", false), Arguments.of("test!", false), Arguments.of("a b c", false),
				Arguments.of("testfail$", false));
	}

	private static Stream<Arguments> provideStringForTestRegexSoftwarePackageName() {
		return Stream.of(Arguments.of("Software Package Name", false), Arguments.of("SoftwarePackage", true),
				Arguments.of("Software_package", true), Arguments.of("software-package", true),
				Arguments.of("com.styl.software.core", true), Arguments.of("test^", false),
				Arguments.of("test&", false), Arguments.of("test*", false), Arguments.of("test#", false),
				Arguments.of("test@", false), Arguments.of("test!", false), Arguments.of("testfail$", false));
	}

	private static Stream<Arguments> provideStringForTestRegexShornameServicePlatform() {
		return Stream.of(
				Arguments.of("STYL", true),
				Arguments.of("Styl", false),
				Arguments.of("S T Y L", false),
				Arguments.of("STY-L", false),
				Arguments.of("STYL1", false),
				Arguments.of("test^", false),
				Arguments.of("test&", false), 
				Arguments.of("test*", false), 
				Arguments.of("test#", false),
				Arguments.of("test@", false), 
				Arguments.of("test!", false), 
				Arguments.of("testfail$", false));
	}

}
