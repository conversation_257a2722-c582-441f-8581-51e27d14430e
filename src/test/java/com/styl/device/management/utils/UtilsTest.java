/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.nio.charset.StandardCharsets;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
public class UtilsTest {

	@Test
	public void testOverFlowNumberFormat() {
		String valuePrefix = "T";
		String numberFormat = "%09d";
		Long number = 109999999999L;

		String result = valuePrefix + String.format(numberFormat, number);
		assertEquals("T" + number, result);
	}

	@Test
	public void testFormatGenerateId() {
		String valuePrefix = "T";
		String numberFormat = "%09d";
		Long number = 1L;

		String result = valuePrefix + String.format(numberFormat, number);
		assertEquals("T000000001", result);
	}

	@Test
	public void testGenerateChallenge() {
		String generateToken = Utils.generateChallenge(6);
		assertNotNull(generateToken);
		assertEquals(6, generateToken.length());
	}
	
	
	@Test
	void testByte() {
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
		System.out.println("length: " + test.length());
		
		final byte[] utf8Bytes = test.getBytes(StandardCharsets.UTF_8);	
		System.out.println("length utf8: " + utf8Bytes.length);
	}
}
