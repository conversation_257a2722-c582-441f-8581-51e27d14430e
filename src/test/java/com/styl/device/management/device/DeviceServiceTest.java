/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.device;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.TestContants;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.DeviceRepository;
import com.styl.device.management.persistence.device.DeviceService;
import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.device.model.DeviceModelService;
import com.styl.device.management.persistence.device.registraion.DeviceRegistration;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.rest.device.DeviceActivationStateResponse;
import com.styl.device.management.rest.device.DeviceRegister;
import com.styl.device.management.rest.device.DeviceRegisterResponse;
import com.styl.device.management.rest.device.DeviceResponse;
import com.styl.device.management.rest.device.DeviceStartupRequest;
import com.styl.device.management.rest.device.software.DeviceSoftware;
import com.styl.device.management.rest.portal.admin.device.AllowTransportRequest;
import com.styl.device.management.rest.portal.admin.device.DeviceAssignRequest;
import com.styl.device.management.rest.portal.admin.device.DevicePendingRegisterResponse;
import com.styl.device.management.rest.portal.admin.device.DevicePortalResponse;
import com.styl.device.management.rest.portal.admin.device.DeviceUnassignRequest;
import com.styl.device.management.rest.portal.admin.device.ForceRenewRequest;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class DeviceServiceTest {

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private DeviceService deviceService;

	@Autowired
	private DeviceModelService deviceModelService;

	@Autowired
	private DeviceRepository deviceRepository;

	@Value("${com.styl.device.management.persistence.device.portal.url:}")
	private String portalUrl;

	@Value("${com.styl.device.management.persistence.device.activation.timeout:86400000}")
	private Long timeExpired;

	@Test
	@Transactional
	public void testListDevice() {
		// 1 device was added previously
		Pagination<DevicePortalResponse> result_1 = deviceService.listDevice(null, null, null, null, null, null, null,
				null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(2, result_1.getTotal());

		// Add device to test
		addDevice(1, 10);

		// find all device and pagination
		Pagination<DevicePortalResponse> result_2 = deviceService.listDevice(null, null, null, null, null, null, null,
				null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(12, result_2.getTotal());

		// filter by id
		Pagination<DevicePortalResponse> result_3 = deviceService.listDevice(TestContants.DEVICE_UID, null, null, null,
				null, null, null, null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(1, result_3.getTotal());

		// filter like by id
		Pagination<DevicePortalResponse> result_4 = deviceService.listDevice("ABCID", null, null, null, null, null,
				null, null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(0, result_4.getTotal());

		// filter like by id
		Pagination<DevicePortalResponse> result_4_2 = deviceService.listDevice("T00000", null, null, null, null, null,
				null, null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(10, result_4_2.getTotal());

		// filter like by id
		Pagination<DevicePortalResponse> result_4_3 = deviceService.listDevice("009", null, null, null, null, null,
				null, null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(1, result_4_3.getTotal());

		// filter by hardwareId
		Pagination<DevicePortalResponse> result_5 = deviceService.listDevice(null, "hardwareId-2", null, null, null,
				null, null, null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(1, result_5.getTotal());

		// filter by hardwareId and device uid
		Pagination<DevicePortalResponse> result_6 = deviceService.listDevice("device not found", "hardwareId-2", null,
				null, null, null, null, null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(0, result_6.getTotal());

		// filter by model
		Pagination<DevicePortalResponse> result_7 = deviceService.listDevice(null, null, "E700", null, null, null, null,
				null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(12, result_7.getTotal());

		// filter by model
		Pagination<DevicePortalResponse> result_8 = deviceService.listDevice(null, null, "E800", null, null, null, null,
				null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(0, result_8.getTotal());

		// filter by service-platform
		Pagination<DevicePortalResponse> result_9 = deviceService.listDevice(null, null, null, 1, null, null, null,
				null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(0, result_9.getTotal());

		// add device with service-platform
		ServicePlatform sp = addServicePlatform();
		addDeviceWithServicePlatform(sp);
		// filter by service-platform
		Pagination<DevicePortalResponse> result_10 = deviceService.listDevice(null, null, null, sp.getId(), null, null,
				null, null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(1, result_10.getTotal());

		// filter by simId
		Pagination<DevicePortalResponse> result_11 = deviceService.listDevice(null, null, null, null, "12222222", null,
				null, null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(10, result_11.getTotal());

		// filter by imei
		Pagination<DevicePortalResponse> result_12 = deviceService.listDevice(null, null, null, null, null,
				"Imeiiiiiiiii", null, null, null, null, null, "id", "ASC", 0, 10);
		assertEquals(10, result_12.getTotal());

		// filter by imei
		Pagination<DevicePortalResponse> result_13 = deviceService.listDevice(null, null, null, null, null, null, null,
				141131315L, null, null, null, "id", "ASC", 0, 10);
		assertEquals(13, result_13.getTotal());

	}

//	@Test
//	public void testSoftwareDetailOfDevice() {
//		List<SoftwareVersionResponse> result_1 = deviceService.getSoftwareDetailOfDevice(TestContants.DEVICE_UID);
//		assertEquals(0, result_1.size());
//
//		// insert current software assign
//		Optional<Device> deviceContant = deviceRepository.findById(TestContants.DEVICE_UID);
//		assertEquals(true, deviceContant.isPresent());
//		addAssignStateInstalled(deviceContant.get(), TestContants.SOFTWARE_VERSION_0_1_7);
//		List<SoftwareVersionResponse> result_2 = deviceService.getSoftwareDetailOfDevice(TestContants.DEVICE_UID);
//		assertEquals(1, result_2.size());
//		assertEquals(TestContants.SOFTWARE_VERSION_0_1_7, result_2.get(0).getVersion());
//
//		// insert 0.1.8 to device
//		addAssignStateInstalled(deviceContant.get(), TestContants.SOFTWARE_VERSION_0_1_8);
//		List<SoftwareVersionResponse> result_3 = deviceService.getSoftwareDetailOfDevice(TestContants.DEVICE_UID);
//		assertEquals(1, result_3.size());
//		assertEquals(TestContants.SOFTWARE_VERSION_0_1_8, result_3.get(0).getVersion());
//	}

	@Test
	public void testRegisterDevice_Success() {
		DeviceRegister deviceRegis = new DeviceRegister();
		deviceRegis.setHardwareId("New HardwareId Regis");
		deviceRegis.setImei("imei12sdwqefasd");
		deviceRegis.setModel("E700");
		deviceRegis.setSimId("121313das");

		DeviceRegisterResponse registerDevice = deviceService.selfRegisterDevice(deviceRegis);
		String activationCode = registerDevice.getChallenge();
		assertEquals(6, activationCode.length());
		assertEquals(portalUrl + activationCode, registerDevice.getQrString());
		assertEquals(portalUrl, registerDevice.getUrl());
		assertEquals(1, registerDevice.getChallengeCode());
	}

	@Test
	public void testFlowActiveDevice_Success() {
		DeviceRegister deviceRegis = new DeviceRegister();
		deviceRegis.setHardwareId("New HardwareId Regis");
		deviceRegis.setImei("imei12sdwqefasd");
		deviceRegis.setModel("E700");
		deviceRegis.setSimId("121313das");
		DeviceRegisterResponse registerDevice = deviceService.selfRegisterDevice(deviceRegis);
		String activationCode = registerDevice.getChallenge();

		Device result = deviceService.activateDeviceWithChallenge(activationCode);
		assertEquals("New HardwareId Regis", result.getHardwareId());
		assertEquals("imei12sdwqefasd", result.getImei());
		assertEquals("121313das", result.getSimId());
		assertEquals("E700", result.getModel().getModel());
	}

	@Test
	public void testFlowActiveDeviceWithNewModel_Success() {
		DeviceRegister deviceRegis = new DeviceRegister();
		deviceRegis.setHardwareId("New HardwareId Regis");
		deviceRegis.setImei("imei12sdwqefasd");
		deviceRegis.setModel("newModel");
		deviceRegis.setSimId("121313das");
		DeviceRegisterResponse registerDevice = deviceService.selfRegisterDevice(deviceRegis);
		String activationCode = registerDevice.getChallenge();

		Device result = deviceService.activateDeviceWithChallenge(activationCode);
		assertEquals("New HardwareId Regis", result.getHardwareId());
		assertEquals("imei12sdwqefasd", result.getImei());
		assertEquals("121313das", result.getSimId());
		assertEquals("newModel", result.getModel().getModel());

		DeviceModel model = deviceModelService.findByModel("newModel");
		assertEquals("newModel", model.getModel());
	}

	@Test
	public void testFlowRegisterWithPassChallengeAndNewModel_Success() {
		String hardwareId = "RegisterWithNewModel";
		DeviceRegistration deviceRegis = new DeviceRegistration();
		deviceRegis.setExpiryTime(1L);
		deviceRegis.setHardwareId(hardwareId);
		deviceRegis.setModel("newModel");
		deviceRegis.setChallengeCode(DeviceRegistration.NO_CHALLENGE_REQUIRED);
		entityManager.persist(deviceRegis);
		entityManager.flush();

		Device result = deviceService.activateDevice(deviceRegis);
		assertEquals(hardwareId, result.getHardwareId());
		assertEquals("newModel", result.getModel().getModel());

		DeviceModel model = deviceModelService.findByModel("newModel");
		assertEquals("newModel", model.getModel());
	}

	@Test
	public void testRegisterDeviceSeccond() {
		DeviceRegister deviceRegis = new DeviceRegister();
		deviceRegis.setHardwareId(TestContants.DEVICE_HARDWARE_ID_REGISTER);
		deviceRegis.setImei("imei12sdwqefasd");
		deviceRegis.setModel("E700");
		deviceRegis.setSimId("121313das");

		DeviceRegisterResponse registerDevice = deviceService.selfRegisterDevice(deviceRegis);
		String activationCode = registerDevice.getChallenge();
		assertEquals(6, activationCode.length());
		assertEquals(portalUrl + activationCode, registerDevice.getQrString());
		assertEquals(portalUrl, registerDevice.getUrl());
		assertEquals(1, registerDevice.getChallengeCode());

	}

	/**
	 * If device is already registered previously, it will not challenge
	 */
	@Test
	public void testSelfRegisterPassChallenge_Success() {
		DeviceRegister deviceRegis = new DeviceRegister();
		deviceRegis.setHardwareId(TestContants.DEVICE_HARDWARE_ID);
		deviceRegis.setImei("imei12sdwqefasd");
		deviceRegis.setModel("E700");
		deviceRegis.setSimId("121313das");

		DeviceRegisterResponse registerDevice = deviceService.selfRegisterDevice(deviceRegis);
		// expect will pass challenge
		assertEquals(0, registerDevice.getChallengeCode());
	}

	@Test
	public void testSelfRegisterWithRegisterOnPortal_Success() {
		DeviceRegister deviceRegis = new DeviceRegister();
		deviceRegis.setHardwareId(TestContants.DEVICE_HARDWARE_ID_REGISTED_ON_PORTAL);
		deviceRegis.setImei("imei12sdwqefasd");
		deviceRegis.setModel("E700");
		deviceRegis.setSimId("121313das");

		DeviceRegisterResponse registerDevice = deviceService.selfRegisterDevice(deviceRegis);
		// expect will pass challenge
		assertEquals(0, registerDevice.getChallengeCode());
	}

	@Test
	public void testMultiDeviceRequestRegister() throws InterruptedException, ExecutionException {
		int numberOfThreads = 10;
		ExecutorService service = Executors.newFixedThreadPool(numberOfThreads);
		Long totalDeviceBeforeActive = deviceRepository.count();
		Future<List<String>> result = service.submit(() -> {
			List<String> listDeviceUid = new ArrayList<String>();
			for (int i = 0; i < numberOfThreads; i++) {
				try {
					Device devicePortal = deviceService.activateDeviceWithChallenge("Code" + i);
					listDeviceUid.add(devicePortal.getId());
				} catch (Exception e) {

				}
			}
			return listDeviceUid;
		});
		Thread.sleep(2000);
		Long totalDeviceAfterActive = deviceRepository.count();
		assertEquals(10, result.get().size());
		assertEquals(totalDeviceBeforeActive + 10, totalDeviceAfterActive);

	}

	@Test
	public void testActive_failure_expired_time() {
		DeviceRegistration deviceRegis = new DeviceRegistration();
		String activeCode = "12345678";
		deviceRegis.setChallenge(activeCode);
		deviceRegis.setExpiryTime(1L);
		deviceRegis.setHardwareId("text_expired_time");
		deviceRegis.setModel("E700");
		deviceRegis.setChallengeCode(DeviceRegistration.ACTIVATION_CODE_CHALLENGE);
		entityManager.persist(deviceRegis);
		entityManager.flush();

		ServiceException exception = assertThrows(ServiceException.class, () -> {
			deviceService.activateDeviceWithChallenge(activeCode);
		});
		assertEquals(ErrorCode.INVALID_ACTIVATION_CODE.getErrorCode(), exception.getErrorCode());
	}

	@Test
	public void testCheckRegister_WithDeviceActive() {
		DeviceActivationStateResponse result = deviceService.deviceActivation(TestContants.DEVICE_HARDWARE_ID);
		assertEquals(true, result.isActive());
	}

	@Test
	public void testCheckRegister_WithDeviceNotActive() {
		DeviceActivationStateResponse result = deviceService.deviceActivation("HardwareIdNotExist");
		assertEquals(false, result.isActive());
	}

	@Test
	public void testDeviceActivationWithPassChallenge() {
		String hardwareId = "testDeviceActivationWithPassChallenge";
		DeviceRegistration deviceRegis = new DeviceRegistration();
		deviceRegis.setExpiryTime(1L);
		deviceRegis.setHardwareId(hardwareId);
		deviceRegis.setModel("E700");
		deviceRegis.setChallengeCode(DeviceRegistration.NO_CHALLENGE_REQUIRED);
		entityManager.persist(deviceRegis);
		entityManager.flush();

		DeviceActivationStateResponse deviceActivation = deviceService.deviceActivation(hardwareId);
		assertEquals(hardwareId, deviceActivation.getHardwareId());
		assertNotNull(deviceActivation.getDeviceUid());

	}

	/**
	 * 1. Device -> register to sever <br>
	 * 2. Operator input challenge on portal <br>
	 * 3. device check activation state with deviceActivation function <br>
	 * 4. Device startup <br>
	 * 
	 */
	@Test
	public void testFlowSelfRegister_Success() {
		String hardwareId = "HardwareId_test_flow_selfRegister";

		// Device sefl-register
		DeviceRegister deviceRegis = new DeviceRegister();
		deviceRegis.setHardwareId(hardwareId);
		deviceRegis.setImei("imei12sdwqefasd");
		deviceRegis.setModel("E700");
		deviceRegis.setSimId("121313das");
		DeviceRegisterResponse registerDevice = deviceService.selfRegisterDevice(deviceRegis);

		// device check activation state before pass challenge
		DeviceActivationStateResponse deviceActivationBeforePassChallenge = deviceService.deviceActivation(hardwareId);
		assertEquals(false, deviceActivationBeforePassChallenge.isActive());

		// Operator input challenge
		Device result = deviceService.activateDeviceWithChallenge(registerDevice.getChallenge());
		assertNotNull(result);
		assertEquals(hardwareId, result.getHardwareId());
		assertNotNull(result.getId());

		// device check activation state after register on portal
		DeviceActivationStateResponse deviceActivationAfterPassChallenge = deviceService.deviceActivation(hardwareId);
		assertEquals(true, deviceActivationAfterPassChallenge.isActive());
		assertEquals(hardwareId, deviceActivationAfterPassChallenge.getHardwareId());

		// Startup
		DeviceStartupRequest startupReq = new DeviceStartupRequest();
		startupReq.setDeviceUid(deviceActivationAfterPassChallenge.getDeviceUid());

		DeviceResponse startup = deviceService.startup(startupReq);
		assertNotNull(startup.getDeviceUid());
		assertEquals(hardwareId, startup.getHardwareId());
		assertEquals(null, startup.getServicePlatform());
	}

	/**
	 * Step 1: Device selt register -> call seltRegister function <br>
	 * Step 2: check activation state <br>
	 * Step 3: Register on portal <br>
	 * Step 4: check activation state <br>
	 * Step 5: Startup
	 */
	@Test
	public void testFlowSelfRegister_register_portal_success() {
		String hardwareId = "HardwareId_selfRegister_and_register_portal";

		// Device sefl-register
		DeviceRegister deviceRegis = new DeviceRegister();
		deviceRegis.setHardwareId(hardwareId);
		deviceRegis.setImei("imei12sdwqefasd");
		deviceRegis.setModel("E700");
		deviceRegis.setSimId("121313das");
		DeviceRegisterResponse registerDevice = deviceService.selfRegisterDevice(deviceRegis);

		// device check activation state before register on portal
		DeviceActivationStateResponse deviceActivationBeforeRegisterOnPortal = deviceService
				.deviceActivation(hardwareId);
		assertEquals(false, deviceActivationBeforeRegisterOnPortal.isActive());

		// Operator input challenge
		Device result = deviceService.activateDeviceWithChallenge(registerDevice.getChallenge());
		assertNotNull(result);
		assertEquals(hardwareId, result.getHardwareId());
		assertNotNull(result.getId());

		// device check activation state after register on portal
		DeviceActivationStateResponse deviceActivationAfterRegisterOnPortal = deviceService
				.deviceActivation(hardwareId);
		assertEquals(true, deviceActivationAfterRegisterOnPortal.isActive());
		assertEquals(hardwareId, deviceActivationAfterRegisterOnPortal.getHardwareId());

		// Startup
		DeviceStartupRequest startupReq = new DeviceStartupRequest();
		startupReq.setDeviceUid(deviceActivationAfterRegisterOnPortal.getDeviceUid());
		DeviceResponse startup = deviceService.startup(startupReq);
		assertNotNull(startup.getDeviceUid());
		assertEquals(hardwareId, startup.getHardwareId());
		assertEquals(null, startup.getServicePlatform());
	}

	@Test
	public void testRegisterOnPortal_Success() {
		String hardwareId = "Register_On_Portal";
		DevicePendingRegisterResponse registerOnPortal = deviceService.registerOnPortal(hardwareId);
		assertEquals(hardwareId, registerOnPortal.getHardwareId());
		assertEquals(0, registerOnPortal.getChallenge());
	}

	@Test
	public void testRegisterOnPortalSeccond_Success() {
		String hardwareId = "RegisterOnPortalWithIdHasRegisted";
		DeviceRegistration deviceRegis = new DeviceRegistration();
		String activeCode = "12345678";
		deviceRegis.setChallenge(activeCode);
		deviceRegis.setExpiryTime(1L);
		deviceRegis.setHardwareId(hardwareId);
		deviceRegis.setModel("E700");
		deviceRegis.setChallengeCode(DeviceRegistration.ACTIVATION_CODE_CHALLENGE);
		entityManager.persist(deviceRegis);
		entityManager.flush();

		DevicePendingRegisterResponse registerOnPortal = deviceService.registerOnPortal(hardwareId);
		assertEquals(DeviceRegistration.NO_CHALLENGE_REQUIRED, registerOnPortal.getChallenge());
	}

	@Test
	public void testRegisterOnPortal_Device_Already_Existed() {
		ServiceException exception = assertThrows(ServiceException.class, () -> {
			deviceService.registerOnPortal(TestContants.DEVICE_HARDWARE_ID);

		});
		assertEquals(ErrorCode.DEVICE_ALREADY_EXISTED.getErrorCode(), exception.getErrorCode());
	}

	@Test
	void testAssignServicePlatform() {
		DeviceAssignRequest assign = new DeviceAssignRequest();
		assign.setServicePlatformId(TestContants.SERVICE_PLATFORM_ID);
		List<String> deviceUids = new ArrayList<String>();
		deviceUids.add(TestContants.DEVICE_UID);
		assign.setDeviceUids(deviceUids);

		deviceService.assignServicePlatform(assign);

		Device existingDevice = deviceRepository.findById(TestContants.DEVICE_UID).orElse(null);
		assertEquals(TestContants.SERVICE_PLATFORM_ID, existingDevice.getServicePlatform().getId());

	}

	@Test
	void testAssignServicePlatform_error() {
		ServicePlatform addServicePlatform = addServicePlatform();
		Device device = addDeviceWithServicePlatform(addServicePlatform);
		DeviceAssignRequest assign = new DeviceAssignRequest();
		assign.setServicePlatformId(TestContants.SERVICE_PLATFORM_ID);
		List<String> deviceUids = new ArrayList<String>();
		deviceUids.add(TestContants.DEVICE_UID);
		deviceUids.add(device.getId());
		assign.setDeviceUids(deviceUids);

		ServiceException exception = assertThrows(ServiceException.class, () -> {
			deviceService.assignServicePlatform(assign);
		});
		assertEquals(ErrorCode.DEVICE_HAS_BEEN_ASSIGNED_SP.getErrorCode(), exception.getErrorCode());
	}

	@Test
	void testUnassignServicePlatform() {
		ServicePlatform addServicePlatform = addServicePlatform();
		Device device = addDeviceWithServicePlatform(addServicePlatform);

		assertNotNull(device.getServicePlatform());

		DeviceUnassignRequest unassign = new DeviceUnassignRequest();
		unassign.setDeviceUid(device.getId());

		Device deviceAfterUnassign = deviceService.unassignServicePlatform(unassign);
		assertNull(deviceAfterUnassign.getServicePlatform());
	}

	@Test
	void testForceRenewCert_Success() {
		Device existingDevice = deviceRepository.findById(TestContants.DEVICE_UID).orElse(null);
		assertEquals(false, existingDevice.isForceRenew());
		ForceRenewRequest request = new ForceRenewRequest();
		request.setDeviceUid(TestContants.DEVICE_UID);
		request.setForceRenew(true);
		Device forceRenew = deviceService.forceRenew(request);
		assertEquals(true, forceRenew.isForceRenew());
	}

	@Test
	void testAllowTransport() {
		Device existingDevice = deviceRepository.findById(TestContants.DEVICE_UID).orElse(null);
		assertEquals(false, existingDevice.isForceRenew());
		AllowTransportRequest request = new AllowTransportRequest();
		request.setDeviceUid(TestContants.DEVICE_UID);
		request.setAllowTransport(true);
		Device result = deviceService.allowTransport(request);
		assertEquals(true, result.isAllowTransport());
	}

	private void addDevice(int start, int end) {
		DeviceModel model = deviceModelService.findByModel(TestContants.MODEL);
		for (int i = start; i <= end; i++) {
			Device device = new Device();
			device.setHardwareId("hardwareId-" + i);
			device.setCreatedBy("Dino");
			device.setCreatedTime(121131313L);
			device.setFirstRegistrationTime(121131313L);
			device.setImei("Imeiiiiiiiii");
			device.setSimId("12222222");
			device.setState(Device.AVAILABLE);
			device.setModel(model);
			entityManager.persist(device);
			entityManager.flush();
		}
	}

	private Device addDeviceWithServicePlatform(ServicePlatform sp) {
		DeviceModel model = deviceModelService.findByModel(TestContants.MODEL);
		Device device = new Device();
		device.setHardwareId("hardwareId-WithServicePlatfrom");
		device.setCreatedBy("Dino");
		device.setCreatedTime(121131313L);
		device.setFirstRegistrationTime(121131313L);
		device.setImei("Imei");
		device.setSimId("789789789");
		device.setModel(model);
		device.setServicePlatform(sp);
		device.setState(Device.ASSIGNED);
		device.setCurrentSoftwares(new ArrayList<DeviceSoftware>());
		entityManager.persist(device);
		entityManager.flush();
		return device;
	}

	private ServicePlatform addServicePlatform() {
		ServicePlatform newSp = new ServicePlatform();
		newSp.setName("STYL SOLUTIONS TEST");
		newSp.setShortName("STYL_TEST");
		newSp.setCreatedTime(123456789L);
		newSp.setCreatedBy("Test");
		newSp.setUrl("https://styl.solutions");
		entityManager.persist(newSp);
		entityManager.flush();
		return newSp;
	}

}
