/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.device;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.persistence.device.DeviceService;
import com.styl.device.management.rest.device.DeviceRegister;
import com.styl.device.management.rest.device.DeviceRegisterResponse;
import com.styl.device.management.utils.Utils;

/**
 * <AUTHOR> Lam
 *
 */
@SpringBootTest(properties = { "spring.sql.init.mode=never", "security.api.enabled=false" })
@ActiveProfiles("test")
@AutoConfigureMockMvc
public class DeviceControllerTest {

	@Autowired
	private MockMvc mockMvc;

	@MockBean
	private DeviceService deviceService;
	
	@Test
	public void testRegisterDevice_Invalid_HardwareId() throws Exception {
		DeviceRegister deviceRegis = new DeviceRegister();
		deviceRegis.setHardwareId("");
		deviceRegis.setImei("imei12sdwqefasd");
		deviceRegis.setModel("E700");
		deviceRegis.setSimId("121313das");
		
		DeviceRegisterResponse expectResult = new DeviceRegisterResponse();
		when(deviceService.selfRegisterDevice(any())).thenReturn(expectResult);
		
		mockMvc.perform(post("/device/register").contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(deviceRegis))).andExpect(MockMvcResultMatchers.jsonPath("errors").exists())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isNotEmpty()).andExpect(MockMvcResultMatchers
						.jsonPath("errors[0].errorCode").value(ErrorCode.INVALID_FIELD.getErrorCode()));

	}
	 
	@Test
	public void testRegisterDevice_Invalid_Model() throws Exception {
		DeviceRegister deviceRegis = new DeviceRegister();
		deviceRegis.setHardwareId("ewewdsa131313");
		deviceRegis.setImei("imei12sdwqefasd");
		deviceRegis.setModel("");
		deviceRegis.setSimId("121313das");
		
		DeviceRegisterResponse expectResult = new DeviceRegisterResponse();
		when(deviceService.selfRegisterDevice(any())).thenReturn(expectResult);
		
		mockMvc.perform(post("/device/register").contentType(MediaType.APPLICATION_JSON)
				.content(Utils.mapToJson(deviceRegis))).andExpect(MockMvcResultMatchers.jsonPath("errors").exists())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("errors").isNotEmpty()).andExpect(MockMvcResultMatchers
						.jsonPath("errors[0].errorCode").value(ErrorCode.INVALID_FIELD.getErrorCode()));

	}
	
	@Test
	public void testRegisterDevice_Success() throws Exception {
		DeviceRegister deviceRegis = new DeviceRegister();
		deviceRegis.setHardwareId("New HardwareId Regis");
		deviceRegis.setImei("imei12sdwqefasd");
		deviceRegis.setModel("E700");
		deviceRegis.setSimId("121313das");
		
		DeviceRegisterResponse expectResult = new DeviceRegisterResponse();
		when(deviceService.selfRegisterDevice(any())).thenReturn(expectResult);
		MockHttpServletResponse response = mockMvc.perform(post("/device/register")
				.contentType(MediaType.APPLICATION_JSON).content(Utils.mapToJson(deviceRegis))).andReturn()
				.getResponse();


		assertEquals(200, response.getStatus());
	}
	
	
}
