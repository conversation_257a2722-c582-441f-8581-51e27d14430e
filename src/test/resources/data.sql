
INSERT INTO tbl_service_platform
(id, short_name, "name", url, contact_email, contact_name, contact_phone, created_by, created_time, updated_time, updated_by)
VALUES (1, 'STYL', 'STYL SOLUTION', 'https://www.styl.com.sg', '<EMAIL>', 'styl', '12345678', '<PERSON>_lam', 1666586080410, null, null );


INSERT INTO tbl_software
( description, "name", package_name, service_platform_id)
VALUES('Software Test','Caribbean-POS', 'com.styl.caribbean.pos.amex', 1);

INSERT INTO tbl_software_version (created_by, created_time, file_path, checksum, software_size, "version", software_id, update_mode)
	VALUES ('<PERSON>_Lam',1666585280410,'ota/software/123456789',100000,29428723,'0.1.1',1,1),
			('<PERSON>_<PERSON>',1666585380410,'ota/software/123456789',100000,29428723,'0.1.2',1,2),
			('<PERSON>_<PERSON>',1666585480410,'ota/software/123456789',100000,29428723,'0.1.3',1,1),
			('Dino_Lam',1666585580410,'ota/software/123456789',100000,29428723,'0.1.4',1,2),
			('Dino_Lam',1666585680410,'ota/software/123456789',100000,29428723,'0.1.5',1,1),
			('Dino_Lam',1666585780410,'ota/software/123456789',100000,29428723,'0.1.6',1,3),
			('Dino_Lam',1666585880410,'ota/software/123456789',100000,29428723,'0.1.7',1,1),
			('Dino_Lam',1666585980410,'ota/software/123456789',100000,29428723,'0.1.8',1,1),
			('Dino_Lam',1666586080410,'ota/software/123456789',100000,29428723,'0.1.9',1,3),
			('Dino_Lam',1666587080410,'ota/software/123456789',100000,29428723,'0.2.0',1,3);


INSERT INTO tbl_software
( description, "name", package_name, service_platform_id)
VALUES('Software Test','Caribbean-SOK', 'com.styl.caribbean.sok.amex', null);

INSERT INTO tbl_software
( description, "name", package_name, service_platform_id)
VALUES('Software Test 2','DMS Test', 'com.styl.dms.test', null);

INSERT INTO tbl_software
( description, "name", package_name, service_platform_id)
VALUES('Software Test','test remove', 'test.remove', null);

INSERT INTO tbl_software_version (created_by, created_time, file_path, checksum, software_size, "version", software_id, update_mode)
	VALUES ('Dino_Lam',1666585280410,'ota/software/123456789', 100000,29428723,'0.1.1',2,1),
			('Dino_Lam',1666585380410,'ota/software/123456789', 100000,29428723,'0.1.2',2,2),
			('Dino_Lam',1666585480410,'ota/software/123456789', 100000,29428723,'0.1.3',2,1),
			('Dino_Lam',1666585580410,'ota/software/123456789', 100000,29428723,'0.1.4',2,2),
			('Dino_Lam',1666585680410,'ota/software/123456789', 100000,29428723,'0.1.5',2,1),
			('Dino_Lam',1666585780410,'ota/software/123456789', 100000,29428723,'0.1.6',2,3),
			('Dino_Lam',1666585880410,'ota/software/123456789', 100000,29428723,'0.1.7',2,1),
			('Dino_Lam',1666585980410,'ota/software/123456789', 100000,29428723,'0.1.8',2,1),
			('Dino_Lam',1666586080410,'ota/software/123456789', 100000,29428723,'0.1.9',2,3),
			('Dino_Lam',1666587080410,'ota/software/123456789', 100000,29428723,'0.2.0',2,3);

INSERT INTO tbl_device_model (model, description) VALUES ('E700', 'POS MODEL');


INSERT INTO tbl_software
( description, "name", package_name, service_platform_id)
VALUES('Software Test remove','test remove 2', 'test.remove-serviceplatform', 1);

INSERT INTO tbl_device
( id, created_by, created_time, first_registration_time, hardware_id, imei, state, sim_id, model, service_platform_id)
VALUES('T-0001','Dino',12321312, 12321312, '1212131deafa', '212dsaf314', 1, '21421421414', 'E700', null);

INSERT INTO tbl_registration_device
(challenge, expiry_time, hardware_id, model, sim_id, imei, challenge_code)
VALUES  ('qweadse2', 1669256992308, 'hardware_id_register', 'E700', '123456789', 'qedasda', 1),
		('portal12', 1669256992308, 'hardware_id_registed_on_portal', 'E700', '123456789', 'qedasda', 0),
		('Code0', 2669256992308, 'hardware_id_register0', 'E700', '123456789', 'qedasda', 1),
		('Code1', 2669256992308, 'hardware_id_register1', 'E700', '123456789', 'qedasda', 1),
		('Code2', 2669256992308, 'hardware_id_register2', 'E700', '123456789', 'qedasda', 1),
		('Code3', 2669256992308, 'hardware_id_register3', 'E700', '123456789', 'qedasda', 1),
		('Code4', 2669256992308, 'hardware_id_register4', 'E700', '123456789', 'qedasda', 1),
		('Code5', 2669256992308, 'hardware_id_register5', 'E700', '123456789', 'qedasda', 1),
		('Code6', 2669256992308, 'hardware_id_register6', 'E700', '123456789', 'qedasda', 1),
		('Code7', 2669256992308, 'hardware_id_register7', 'E700', '123456789', 'qedasda', 1),
		('Code8', 2669256992308, 'hardware_id_register8', 'E700', '123456789', 'qedasda', 1),
		('Code9', 2669256992308, 'hardware_id_register9', 'E700', '123456789', 'qedasda', 1),
		('Code10', 2669256992308, 'hardware_id_register10', 'E700', '123456789', 'qedasda', 1);
		
INSERT INTO tbl_tag (name, description) VALUES ('Tag constant', 'Description constant');
ALTER TABLE tbl_service_platform ALTER COLUMN id RESTART WITH 1000;

-- add test data
INSERT INTO tbl_device
( id, created_by, created_time, first_registration_time, hardware_id, imei, state, sim_id, model, service_platform_id)
VALUES('T-0002','Dino',12321312, 12321312, '1212131defab', '212dsaf314', 1, '21421421414', 'E700', null);

INSERT INTO tbl_tag (name, description) VALUES ('Tag name', 'Tag escription');

INSERT INTO tbl_tag_assign (tag_id, device_id) VALUES (1, 'T-0001');
INSERT INTO tbl_tag_assign (tag_id, device_id) VALUES (2, 'T-0002');
INSERT INTO tbl_tag_assign (tag_id, device_id) VALUES (1, 'T-0002');