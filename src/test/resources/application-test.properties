# (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved. -->
# This source code and any compilation or derivative thereof is the sole 
# property of STYL Solutions Pte. Ltd. and is provided pursuant to a Software 
# License Agreement. This code is the proprietary information of STYL Solutions 
# Pte. Ltd. and is confidential in nature. Its use and dissemination by any 
# party other than STYL Solutions Pte. Ltd. is strictly limited by the
# confidential information provisions of the Agreement referenced above.


logging.level.org.springframework.test=WARN	
logging.level.org.apache.kafka=WARN

# H2 Database
spring.datasource.url=jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
spring.datasource.username=sa
spring.datasource.password=sa
spring.datasource.driver-class-name=org.h2.Driver
spring.h2.console.enabled=true
spring.h2.console.path=/h2
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.open-in-view=true
spring.jpa.hibernate.ddl-auto=none
hibernate.show_sql=true
spring.flyway.baseline-on-migrate=true
hibernate.discriminator.ignore_explicit_for_joined=true

logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

com.styl.device.management.error.config:classpath:errors.properties
com.styl.device.management.version=@project.version@

#AWS Configure
com.styl.device.management.aws.bucket=bucket
com.styl.device.management.aws.key=key
com.styl.device.management.aws.key.secret=secret
com.styl.device.management.aws.region=ap-southeast-1
com.styl.device.management.aws.endpoint=https://endpoint
com.styl.device.management.aws.prefix=truststore

#device configure
com.styl.device.management.persistence.device.portal.url=https://portal-test/

# Kafka 
com.styl.device.management.kafka.bootstrap-servers=http://localhost:9093
com.styl.device.management.kafka.groupId=1
com.styl.device.management.kafka.listener.enabled=true
com.styl.device.management.kafka.serviceplatform.event.topic=com.styl.dms.event.raw.json.v1
com.styl.device.management.kafka.serviceplatform.event.failed.topic=com.styl.dms.event.raw.failed.json.v1
com.styl.device.management.kafka.serviceplatform.webhooks.topic=com.styl.dms.event.webhooks.json.v1
com.styl.device.management.kafka.serviceplatform.webhooks.failed.topic=com.styl.dms.event.webhooks.failed.json.v1
com.styl.device.management.kafka.serviceplatform.event.topic.prefix=DMS

com.styl.device.management.kafka.ssl.truststore.location=/mnt/d/dms/private/ssl2/kafka.server.truststore.jks
com.styl.device.management.kafka.ssl.truststore.password=password
com.styl.device.management.kafka.security.protocol=SASL_SSL	
com.styl.device.management.kafka.sasl.mechanism=SCRAM-SHA-512
com.styl.device.management.kafka.ssl.admin.user=demouser
com.styl.device.management.kafka.ssl.admin.password=secret
com.styl.device.management.kafka.ssl.producer.user=demouser
com.styl.device.management.kafka.ssl.producer.password=secret
com.styl.device.management.kafka.ssl.consumer.user=demouser
com.styl.device.management.kafka.ssl.consumer.password=secret
com.styl.device.management.kafka.hostname=localhost
com.styl.device.management.kafka.ssl.enabled=false
com.styl.device.management.kafka.acl.enabled=false