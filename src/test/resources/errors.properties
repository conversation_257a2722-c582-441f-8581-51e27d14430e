# (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved.
# This source code and any compilation or derivative thereof is the sole 
# property of STYL Solutions Pte. Ltd. and is provided pursuant to a Software 
# License Agreement. This code is the proprietary information of STYL Solutions 
# Pte. Ltd. and is confidential in nature. Its use and dissemination by any 
# party other than STYL Solutions Pte. Ltd. is strictly limited by the
# confidential information provisions of the Agreement referenced above.

#Software Error
errors.software.not.found=Software does not exist!!
errors.software.existed=Software existed!!
errors.software.generate.url=Can not generate presigned url to upload software
errors.software.version.file.not.found"=Can not generate presigned url to upload software
errors.software.version.existed=Software version existed!!
errors.software.version.not.found=Software version does not exist!!
errors.software.update.mode.not.found = Update mode not found
errors.device.model.not.found=Device model not found

# Service platform error
errors.service.platform.webhook.url.not.found=Service platform webhook url not found

#General Error
errors.99999=INVALID API
errors.99999=Unknown Error

#Service platform authentication error
errors.service.platform.auth.api.key.empty=API-Key is empty
errors.service.platform.auth.api.key.invalid=API-Key is invalid
errors.service.platform.auth.api.key.expired=API-Key is expired
errors.service.platform.auth.signature.empty=Signature is empty
errors.service.platform.auth.signature.not.match=Signature not match
errors.service.platform.auth.nonce.empty=Nonce is empty
errors.service.platform.auth.nonce.timeout=Nonce is timeout
errors.service.platform.auth.nonce.random.length.invalid=Random length is invalid
errors.service.platform.auth.nonce.invalid=Nonce is invalid
errors.service.platform.auth.failed=Service platform authentication failed

# Api Key
errors.api.key.not.found=API-Key does not exist
errors.api.key.expired=API-Key is expired

#PKI error
errors.pki.vault.exception=Vault Exception
errors.pki.issuer.existed=Issuer already existed 
errors.pki.ca.not.found=CA not found
errors.pki.root.issuer.not.found=Root issuer not found
errors.pki.issuer.not.found=Issuer not found
errors.pki.certificate.not.found=Certificate not found
errors.pki.csr.not.found=Csr not found
errors.pki.issuer.default.not.stored.in.db=Can not find issuer default
