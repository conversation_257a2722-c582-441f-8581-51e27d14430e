# (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved.
# This source code and any compilation or derivative thereof is the sole 
# property of STYL Solutions Pte. Ltd. and is provided pursuant to a Software 
# License Agreement. This code is the proprietary information of STYL Solutions 
# Pte. Ltd. and is confidential in nature. Its use and dissemination by any 
# party other than STYL Solutions Pte. Ltd. is strictly limited by the
# confidential information provisions of the Agreement referenced above.

spring.application.name=@project.name@
com.styl.device.management.version=@project.version@

aws.secret.manager.enabled=${AWS_SECRET_MANAGER_ENABLED:false}

#Logback configure
logging.level.root=${LOG_LEVEL_ROOT:INFO}
logging.level.com.styl=${LOG_LEVEL_DMS:DEBUG} 	
logging.file.path=${LOG_PATH:/tmp/logs}
logging.file.name=${logging.file.path}/${spring.application.name}.log
logging.logback.rollingpolicy.file-name-pattern=${spring.application.name}-%d{yyyy-MM-dd}.%i.log.gz
logging.logback.rollingpolicy.max-file-size=${LOG_FILE_MAX:100MB}
logging.logback.rollingpolicy.total-size-cap=${LOG_FILE_TOTAL_SIZE:1GB}
logging.logback.rollingpolicy.max-history=${LOG_FILE_MAX_HISTORY:30}

# Swagger Configure
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.enabled=${SWAGGER_API_DOCS_ENABLED:false}
springdoc.swagger-ui.enabled=${SWAGGER_UI_ENABLED:false}

# actuator
management.endpoints.enabled-by-default=false
management.endpoint.info.enabled=true
management.endpoint.health.enabled=true
management.endpoint.health.probes.enabled=true
management.endpoint.health.show-details=ALWAYS

# actuator
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.info.build.enabled=true
management.info.env.enabled=true

#Error file
com.styl.device.management.error.config=classpath:errors.properties


#Database connect
spring.datasource.url=jdbc:postgresql://${DATABASE_URL}:${DATABASE_PORT}/${DATABASE_NAME}
spring.datasource.username=${DATABASE_USER:}
spring.datasource.password=${DATABASE_PASSWORD:}
spring.jpa.hibernate.ddl-auto=none
spring.jpa.open-in-view=false
hibernate.show_sql=false

spring.servlet.multipart.max-file-size=${MAX_FILE_SIZE:100MB}
spring.servlet.multipart.max-request-size=${MAX_REQUEST_SIZE:100MB}

#AWS Configure
com.styl.device.management.aws.bucket=${AWS_BUCKET}
com.styl.device.management.aws.key=${AWS_KEY}
com.styl.device.management.aws.key.secret=${AWS_KEY_SECRET}
com.styl.device.management.aws.region=${AWS_REGION}
com.styl.device.management.aws.endpoint=${AWS_ENDPOINT}
com.styl.device.management.aws.enabled.key:${AWS_ENABLED_KEY:false}

#Vault configure
com.styl.device.management.vault.endpoint=${VAULT_ENPOINT:http://*************:8200/}
com.styl.device.management.vault.token=${VAULT_TOKEN:}

# Kafka 
com.styl.device.management.kafka.bootstrap-servers=${KAFKA_SERVER}
com.styl.device.management.kafka.groupId=${KAFKA_GROUP:1}
com.styl.device.management.kafka.listener.enabled=${KAFKA_LISTENER_ENABLED:true}
com.styl.device.management.kafka.serviceplatform.event.topic=${KAFKA_EVENT_TOPIC:com.styl.dms.event.raw.json.v1}
com.styl.device.management.kafka.serviceplatform.event.failed.topic=${KAFKA_EVENT_FAIL_TOPIC:com.styl.dms.event.raw.failed.json.v1}
com.styl.device.management.kafka.serviceplatform.webhooks.topic=${WEBHOOK_TOPIC:com.styl.dms.event.webhooks.json.v1}
com.styl.device.management.kafka.serviceplatform.webhooks.failed.topic=${WEBHOOK_FAIL_TOPIC:com.styl.dms.event.webhooks.failed.json.v1}
com.styl.device.management.kafka.serviceplatform.event.topic.prefix=${EVENT_TOPIC:DMS}

com.styl.device.management.kafka.ssl.truststore.location=${KAFKA_SSL_TRUSTSTORE_LOCATION:/mnt/d/dms/private/ssl2/kafka.server.truststore.jks}
com.styl.device.management.kafka.ssl.truststore.password=${KAFKA_SSL_TRUSTSTORE_PASSWORD:password}
com.styl.device.management.kafka.security.protocol=${KAFKA_SECURITY_PROTOCAL:SASL_SSL}
com.styl.device.management.kafka.sasl.mechanism=${KAFKA_SASL_MECHANISM:SCRAM-SHA-512}
com.styl.device.management.kafka.ssl.admin.user=${KAFKA_SSL_ADMIN_USER:demouser}
com.styl.device.management.kafka.ssl.admin.password=${KAFKA_SSL_ADMIN_PASSWORD:secret}
com.styl.device.management.kafka.ssl.producer.user=${KAFKA_SSL_PRODUCER_USER:demouser}
com.styl.device.management.kafka.ssl.producer.password=${KAFKA_SSL_PRODUCER_USER:secret}
com.styl.device.management.kafka.ssl.consumer.user=${KAFKA_SSL_CONSUMER_USER:demouser}
com.styl.device.management.kafka.ssl.consumer.password=${KAFKA_SSL_CONSUMER_PASSWORD:secret}
com.styl.device.management.kafka.hostname=${KAFKA_HOSTNAME:*}
com.styl.device.management.kafka.ssl.enabled=${KAFKA_SSL_ENABLED:false}
com.styl.device.management.kafka.acl.enabled=${KAFKA_ACL_ENABLED:false}


#Device management
com.styl.device.management.persistence.device.portal.url=${ACTIVE_DEVICE_URL:http://localhost:8080}/management/device/
com.styl.device.management.persistence.device.activation.timeout=${ACTIVE_DEVICE_TIMEOUT:86400000}
com.styl.device.management.vault.pki.ca.ttl.default=${VAULT_PKI_TTL_DEFAULT:31556952}
com.styl.device.management.model.software.version.timeout=${UPLOAD_FILE_TIMEOUT:3600000}

keycloak.enabled=true
keycloak.realm=Ocean-admin
keycloak.resource=dms
keycloak.auth-server-url=https://ciam.styl.solutions/auth
keycloak.ssl-required=external
keycloak.public-client=true
keycloak.bearer-only=true
keycloak.principal-attribute=preferred_username
keycloak.security-constraints[0].authRoles[0]=default-roles-oceam-admin
keycloak.security-constraints[0].securityCollections[0].patterns[0]=/api/*

spring.security.oauth2.resourceserver.jwt.issuer-uri=https://accounts.google.com
spring.security.oauth2.resourceserver.jwt.audiences=