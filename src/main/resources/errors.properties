# (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved.
# This source code and any compilation or derivative thereof is the sole 
# property of STYL Solutions Pte. Ltd. and is provided pursuant to a Software 
# License Agreement. This code is the proprietary information of STYL Solutions 
# Pte. Ltd. and is confidential in nature. Its use and dissemination by any 
# party other than STYL Solutions Pte. Ltd. is strictly limited by the
# confidential information provisions of the Agreement referenced above.

#General Error
errors.general=INVALID API
errors.unknown=Unknown Error

#Software Error
errors.software.not.found=Software does not exist
errors.software.existed=Software existed
errors.software.generate.url=Can not generate presigned url to upload software
errors.software.not.found=Software not found
errors.software.existed=Software already exists
errors.software.name.existed=Software name already exists
errors.software.can.not.remove=Software can't remove
errors.software.update.mode.not.found=Update mode not exist

#Software version error
errors.software.version.file.not.found"=Software version file does not exist
errors.software.version.existed=Software version already exists
errors.software.version.not.found=Software version does not exist
errors.software.version.can.not.add=Software version can't add 
errors.software.version.can.not.remove=Software version can't remove 
errors.software.version.invalid.checksum=invalid checksum
errors.software.version.can.not.download=Software version can't download 

# Software packages error
errors.software.package.not.found=Software package id does not exist

# Device error
errors.device.model.not.found=Device model not exist
errors.device.not.found=Device not found
errors.device.pending.register.not.found=Device register pending not exist
errors.device.invalid.activation.code=Invalid challenge
errors.device.already.existed=Device already existed
errors.device.has.been.assign.sp=Please unassigned service platform before assign new
errors.device.not.allow.transport=Device not allow transport
errors.device.challenge.not.found=Device challenge not found
errors.device.model.not.found=Device model not found
errors.device.need.assign.sp=Device need assign service platform


# Service platform error
errors.service.platform.not.found=Service platform not found
errors.service.platform.existed=Service platform already existed
errors.service.platform.name.existed=Service platform name already existed
errors.service.platform.shortname.existed=Service platform short name already existed
errors.service.platform.can.not.remove = Service platform can not remove
errors.service.platform.webhook.url.not.found=Service platform webhook url not found
    
#Service platform authentication error
errors.service.platform.auth.api.key.empty=API-Key is empty
errors.service.platform.auth.api.key.invalid=API-Key is invalid
errors.service.platform.auth.api.key.expired=API-Key is expired
errors.service.platform.auth.signature.empty=Signature is empty
errors.service.platform.auth.signature.not.match=Signature not match
errors.service.platform.auth.nonce.empty=Nonce is empty
errors.service.platform.auth.nonce.timeout=Nonce is timeout
errors.service.platform.auth.nonce.random.length.invalid=Random length is invalid
errors.service.platform.auth.nonce.invalid=Nonce is invalid
errors.service.platform.auth.failed=Service platform authentication failed

# Api Key
errors.api.key.not.found=API-Key does not exist
errors.api.key.expired=API-Key is expired

# Tag error
errors.tag.already.existed=Tag already existed
errors.tag.not.found=Tag not found
errors.tag.can.not.remove=Tag can not remove
errors.tag.name.already.existed=name already existed

#PKI error
errors.pki.vault.exception=Vault Exception
errors.pki.issuer.existed=Issuer already existed 
errors.pki.ca.not.found=CA not found
errors.pki.root.issuer.not.found=Root issuer not found
errors.pki.issuer.not.found=Issuer not found
errors.pki.certificate.not.found=Certificate not found
errors.pki.csr.not.found=Csr not found
errors.pki.issuer.default.not.stored.in.db=Can not find issuer default