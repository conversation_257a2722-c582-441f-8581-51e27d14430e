# (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved.
# This source code and any compilation or derivative thereof is the sole 
# property of STYL Solutions Pte. Ltd. and is provided pursuant to a Software 
# License Agreement. This code is the proprietary information of STYL Solutions 
# Pte. Ltd. and is confidential in nature. Its use and dissemination by any 
# party other than STYL Solutions Pte. Ltd. is strictly limited by the
# confidential information provisions of the Agreement referenced above.

server.port=9596
com.styl.device.management.version=@project.version@

# Secret manager
com.styl.device.config.aws.secret.manager.enabled=false
com.styl.device.config.aws.secret.manager.region=ap-southeast-1
com.styl.device.config.aws.secret.manager.name=AWS_SM_DMS

#Logback configure
logging.level.com.styl=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.keycloak=DEBUG

# Swagger Configure
springdoc.swagger-ui.path=/swagger-ui
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true

#Error file
com.styl.device.management.error.config=classpath:errors.properties

#Database connect
spring.datasource.url=*************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.jpa.hibernate.ddl-auto=none
spring.jpa.open-in-view=false
hibernate.show_sql=false



#AWS Configure
com.styl.device.management.aws.enabled.key=false
com.styl.device.management.aws.bucket=bis-sit
com.styl.device.management.aws.prefix=truststore
com.styl.device.management.aws.key=********************
com.styl.device.management.aws.key.secret=KRj6PCHvKswiffSe3KNtUa8Ks96jiq/QF6caWgZN
com.styl.device.management.aws.region=ap-southeast-1
com.styl.device.management.aws.endpoint=http://s3.ap-southeast-1.amazonaws.com


com.styl.device.management.vault.endpoint=http://localhost:8200/
com.styl.device.management.vault.token=root

keycloak.enabled=false
keycloak.realm=Ocean-admin
Keycloak.resource=dms-local
keycloak.auth-server-url=http://localhost:8800/
keycloak.ssl-required=external
keycloak.public-client=true
keycloak.bearer-only=true
keycloak.principal-attribute=preferred_username
keycloak.security-constraints[0].authRoles[0]=default-roles-oceam-admin
keycloak.security-constraints[0].securityCollections[0].patterns[0]=/api/*

# Kafka
com.styl.device.management.kafka.bootstrap-servers=http://localhost:9092
com.styl.device.management.kafka.groupId=1
com.styl.device.management.kafka.listener.enabled=false
com.styl.device.management.kafka.serviceplatform.event.topic=com.styl.dms.event.raw.json.v1
com.styl.device.management.kafka.serviceplatform.event.failed.topic=com.styl.dms.event.raw.failed.json.v1
com.styl.device.management.kafka.serviceplatform.webhooks.topic=com.styl.dms.event.webhooks.json.v1
com.styl.device.management.kafka.serviceplatform.webhooks.failed.topic=com.styl.dms.event.webhooks.failed.json.v1
com.styl.device.management.kafka.serviceplatform.event.topic.prefix=DMS

com.styl.device.management.kafka.ssl.truststore.location=/mnt/d/dms/private/ssl2/kafka.server.truststore.jks
com.styl.device.management.kafka.ssl.truststore.password=password
com.styl.device.management.kafka.security.protocol=SASL_SSL	
com.styl.device.management.kafka.sasl.mechanism=SCRAM-SHA-512
com.styl.device.management.kafka.ssl.admin.user=demouser
com.styl.device.management.kafka.ssl.admin.password=secret
com.styl.device.management.kafka.ssl.producer.user=demouser
com.styl.device.management.kafka.ssl.producer.password=secret
com.styl.device.management.kafka.ssl.consumer.user=demouser
com.styl.device.management.kafka.ssl.consumer.password=secret
com.styl.device.management.kafka.hostname=*
com.styl.device.management.kafka.ssl.enabled=false
com.styl.device.management.kafka.acl.enabled=false

spring.security.oauth2.resourceserver.jwt.issuer-uri=https://accounts.google.com
spring.security.oauth2.resourceserver.jwt.audiences=************-7linjd2jt7dlfjsvcluhi54ssamgrhtk.apps.googleusercontent.com