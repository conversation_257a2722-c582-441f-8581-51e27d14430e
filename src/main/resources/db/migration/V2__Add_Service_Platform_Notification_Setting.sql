-- Diff code generated with pg<PERSON><PERSON><PERSON> (PostgreSQL Database Modeler)
-- pgModeler version: 0.9.4
-- Diff date: 2023-01-09 15:18:53
-- Source model: dms
-- Database: dms
-- PostgreSQL version: 11.0

-- [ Diff summary ]
-- Dropped objects: 0
-- Created objects: 2
-- Changed objects: 0


-- [ Created objects ] --
-- object: public.tbl_service_platform_notification_setting | type: TABLE --
-- DROP TABLE IF EXISTS public.tbl_service_platform_notification_setting CASCADE;
CREATE TABLE tbl_service_platform_notification_setting (
	id integer NOT NULL,
	event_topic character varying(256),
	event_version character varying(50),
	enabled_webhook boolean DEFAULT false,
	webhook_url character varying(2083),
	created_by character varying(45) NOT NULL,
	created_time bigint NOT NULL,
	updated_by character varying(45),
	updated_time bigint,
	CONSTRAINT fk_sp_notification_setting_sp FOREIGN KEY (id) REFERENCES tbl_service_platform(id) ON DELETE RESTRICT ON UPDATE RESTRICT

);

