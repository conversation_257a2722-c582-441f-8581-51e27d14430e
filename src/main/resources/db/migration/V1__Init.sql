-- -----------------------------------------------------
-- Table tbl_device_model
-- -----------------------------------------------------
CREATE TABLE tbl_device_model (
	model varchar(25) NOT NULL,
	description varchar(100) NULL,
	CONSTRAINT tbl_device_model_pkey PRIMARY KEY (model)
);


-- -----------------------------------------------------
-- Table tbl_service_platform
-- -----------------------------------------------------

CREATE TABLE tbl_service_platform (
	id serial NOT NULL,
	contact_email varchar(256) NULL,
	contact_name varchar(45) NULL,
	contact_phone varchar(20) NULL,
	created_by varchar(45) NOT NULL,
	created_time int8 NOT NULL,
	"name" varchar(256) NOT NULL,
	short_name varchar(20) NOT NULL,
	updated_by varchar(45) NULL,
	updated_time int8 NULL,
	url varchar(2083) NOT NULL,
	ca_name varchar(100),
	description varchar(256) NULL,
	CONSTRAINT tbl_service_platform_pkey PRIMARY KEY (id),
	CONSTRAINT sp_short_name_unique UNIQUE (short_name),
	CONSTRAINT sp_name_unique UNIQUE (name)
);

-- -----------------------------------------------------
-- Table tbl_device
-- -----------------------------------------------------


CREATE TABLE tbl_device (
	id varchar(45) NOT NULL,
	created_by varchar(45) NULL,
	created_time int8 NULL,
	first_registration_time int8 NOT NULL,
	hardware_id varchar(256) NOT NULL,
	imei varchar(64) NULL,
	state int4 NULL,
	last_registration_time int8 NULL,
	sim_id varchar(64) NULL,
	model varchar(25) NOT NULL,
	service_platform_id int4 NULL,
	current_softwares varchar(1048),
	allow_transport boolean default TRUE,
	force_renew boolean default FALSE,
	csr varchar(2048),
	CONSTRAINT tbl_device_pkey PRIMARY KEY (id),
	CONSTRAINT device_hardware_unique UNIQUE (hardware_id),
	CONSTRAINT fk_device_sp FOREIGN KEY (service_platform_id) REFERENCES tbl_service_platform(id) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- Create seq device
CREATE SEQUENCE tbl_seq_device;

-- -----------------------------------------------------
-- Table tbl_registration_device
-- -----------------------------------------------------

CREATE TABLE tbl_registration_device (
	id serial NOT NULL,
	challenge varchar(8) NULL,
	expiry_time int8 NULL,
	hardware_id varchar(256) NOT NULL,
	challenge_code int NOT NULL,
	model varchar(45) NULL,
	sim_id varchar(64) NULL,
	imei varchar(64) NULL,
	registration_time int8,
	updated_by varchar(45) NULL,
	updated_time int8 NULL,
	CONSTRAINT tbl_registration_device_pkey PRIMARY KEY (id),
	CONSTRAINT challenge_unique UNIQUE (challenge)
);


-- -----------------------------------------------------
-- Table tbl_software
-- -----------------------------------------------------

CREATE TABLE tbl_software (
	id bigserial NOT NULL,
	description varchar(200) NULL,
	"name" varchar(45) NOT NULL,
	package_name varchar(45) NULL,
	device_model varchar(25) NULL,
	service_platform_id int4 NULL,
	CONSTRAINT tbl_software_pkey PRIMARY KEY (id),
	CONSTRAINT sof_name_unique UNIQUE (name),
	CONSTRAINT sof_package_unique UNIQUE (package_name),
	CONSTRAINT fk_sof_sp FOREIGN KEY (service_platform_id) REFERENCES tbl_service_platform(id) ON DELETE RESTRICT ON UPDATE RESTRICT,
	CONSTRAINT fk_sof_device_model FOREIGN KEY (device_model) REFERENCES tbl_device_model(model) ON DELETE RESTRICT ON UPDATE RESTRICT
);


-- -----------------------------------------------------
-- Table tbl_update_mode
-- -----------------------------------------------------

CREATE TABLE tbl_update_mode (
	id int8 NOT NULL,
	"mode" varchar(255) NOT NULL,
	description varchar(256) NULL,
	CONSTRAINT tbl_update_mode_pkey PRIMARY KEY (id)
);

	
INSERT INTO tbl_update_mode (id, mode, description) VALUES (1, 'M', 'Manual update(M): Device can skip this update and install later.'), 
	(2, 'S', 'Manual or Startup update (S): Device can skip this update and install later. When device restart it must update.'), 
	(3, 'F', 'Force update (F): Force device to update the software as soon as possible.');
			
-- -----------------------------------------------------
-- Table tbl_software_version
-- -----------------------------------------------------

CREATE TABLE tbl_software_version (
	id bigserial NOT NULL,
	created_by varchar(45) NOT NULL,
	created_time int8 NOT NULL,
	file_path varchar(256) NOT NULL,
	checksum varchar(256) NULL,
	signature varchar(2048) NULL,
	software_size int4 NULL,
	updated_by varchar(45) NULL,
	updated_time int8 NULL,
	"version" varchar(45) NOT NULL,
	software_id int8 NOT NULL,
	update_mode int8 NOT NULL,
	release_note varchar(256) NULL,
	CONSTRAINT tbl_software_version_pkey PRIMARY KEY (id),
	CONSTRAINT fk_sof_version_sof FOREIGN KEY (software_id) REFERENCES tbl_software(id) ON DELETE RESTRICT ON UPDATE RESTRICT,
	CONSTRAINT fk_sof_version_update_mode FOREIGN KEY (update_mode) REFERENCES tbl_update_mode(id) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- -----------------------------------------------------
-- Table tbl_software_packages
-- -----------------------------------------------------

CREATE TABLE tbl_software_packages (
	id bigserial NOT NULL,
	assigned_by varchar(255) NOT NULL,
	assigned_time int8 NOT NULL,
	state int4 NOT NULL,
	device_id varchar(45) NOT NULL,
	software_id int8 NOT NULL,
	software_version_id int8 NOT NULL,
	update_mode int8 NOT NULL,
	remarks varchar(2048),
	retry_fail int DEFAULT 0,
	app_version varchar(45),
	CONSTRAINT tbl_software_packages_pkey PRIMARY KEY (id),
	CONSTRAINT fk_software_packages_device FOREIGN KEY (device_id) REFERENCES tbl_device(id) ON DELETE RESTRICT ON UPDATE RESTRICT,
	CONSTRAINT fk_software_packages_sof FOREIGN KEY (software_id) REFERENCES tbl_software(id) ON DELETE RESTRICT ON UPDATE RESTRICT,
	CONSTRAINT fk_software_packages_sof_version FOREIGN KEY (software_version_id) REFERENCES tbl_software_version(id) ON DELETE RESTRICT ON UPDATE RESTRICT
);



-- -----------------------------------------------------
-- Table tbl_tag
-- -----------------------------------------------------
CREATE TABLE tbl_tag (
	id serial NOT NULL,
	description varchar(256) NULL,
	"name" varchar(45) NOT NULL,
	CONSTRAINT tbl_tag_pkey PRIMARY KEY (id)
);

-- -----------------------------------------------------
-- Table tbl_tag_assign
-- -----------------------------------------------------
CREATE TABLE tbl_tag_assign (
	tag_id serial NOT NULL,
	device_id varchar(45) NOT NULL,
	CONSTRAINT tbl_tag_assign_pkey PRIMARY KEY (device_id, tag_id),
	CONSTRAINT fk_tag_assign_device FOREIGN KEY (device_id) REFERENCES tbl_device(id) ON DELETE RESTRICT ON UPDATE RESTRICT,
	CONSTRAINT fk_tag_assign_tag FOREIGN KEY (tag_id) REFERENCES tbl_tag(id) ON DELETE RESTRICT ON UPDATE RESTRICT
);


-- [ Created objects ] --
-- object: public.tbl_api_key | type: TABLE --
-- DROP TABLE IF EXISTS public.tbl_api_key CASCADE;
CREATE TABLE tbl_api_key (
	id bigserial NOT NULL,
	service_platform_id bigint NOT NULL,
	api_key varchar(64) NOT NULL,
	secret_key varchar(128) NOT NULL,
	created_by varchar(45) NOT NULL,
	created_time bigint NOT NULL,
	expired_time bigint NOT NULL,
	enabled boolean NOT NULL,
	CONSTRAINT tbl_api_key_pk PRIMARY KEY (id),
	CONSTRAINT fk_api_key_service_platform FOREIGN KEY (service_platform_id) REFERENCES tbl_service_platform(id) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- object: idx_api_key_service_platform_id | type: INDEX --
-- DROP INDEX IF EXISTS public.idx_api_key_service_platform_id CASCADE;
CREATE INDEX idx_api_key_service_platform_id ON tbl_api_key
USING btree
(
	service_platform_id ASC NULLS LAST
);
-- ddl-end --

CREATE TABLE tbl_certificate (
	serial_number varchar(60) NULL,
	ca_name varchar(100) NULL,
	common_name varchar(200) NULL,
	issuer_name varchar(200),
	issued_time int8 NULL,
	expiry_time int8 NULL,
	issuer_id varchar(36) null,
	predecessor varchar(60) NULL,
	revoked_time BIGINT,
	certificate varchar(2048),
	CONSTRAINT tbl_certificate_pkey PRIMARY KEY (serial_number)
);

CREATE TABLE tbl_certificate_issuer (
	issuer_id varchar(36) NULL,
	ca_name varchar(100) NULL,
	issuer_name varchar(200) NULL,
	created_time int8 NULL,
	expiry_time int8 NULL,
	is_default boolean NULL,
	serial_number varchar(60),
	revoked_time BIGINT,
	CONSTRAINT tbl_certificate_issuer_pkey PRIMARY KEY (issuer_id)
);

