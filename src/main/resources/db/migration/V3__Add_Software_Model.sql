
ALTER TABLE tbl_software
DROP CONSTRAINT fk_sof_device_model;

ALTER TABLE tbl_software DROP COLUMN device_model; 

CREATE TABLE tbl_software_device_model (
	software_id bigint NOT NULL,
	device_model varchar(25) NOT NULL,
	CONSTRAINT tbl_software_device_model_pkey PRIMARY KEY (software_id, device_model),
	CONSTRAINT fk_software_device_model_software FOREIGN KEY (software_id) REFERENCES tbl_software(id) ON DELETE RESTRICT ON UPDATE RESTRICT,
	CONSTRAINT fk_software_device_model_model FOREIGN KEY (device_model) REFERENCES tbl_device_model(model) ON DELETE RESTRICT ON UPDATE RESTRICT
);

