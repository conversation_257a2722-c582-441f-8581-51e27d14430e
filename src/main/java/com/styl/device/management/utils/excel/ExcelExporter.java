/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.utils.excel;

import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import com.styl.device.management.utils.file.DataRecord;
import com.styl.device.management.utils.file.FileExporter;
import com.styl.device.management.utils.file.RecordHeader;

/**
 * <AUTHOR>
 *
 */
public class ExcelExporter implements FileExporter {

	@Override
	public <T extends DataRecord> void export(OutputStream os, List<T> records, Class<T> clazz, boolean includeHeader) {
		try (SXSSFWorkbook wb = new SXSSFWorkbook(SXSSFWorkbook.DEFAULT_WINDOW_SIZE)) {
			// keep 100 rows in memory, exceeding rows will be flushed to disk

			Sheet sh = wb.createSheet();
			Field[] fields = clazz.getDeclaredFields();
			int noOfColumns = fields.length;
			int rowNum = 0;
			Row row = sh.createRow(rowNum);
			if (includeHeader) {
				for (int i = 0; i < noOfColumns; i++) {
					Cell cell = row.createCell(i);

					RecordHeader recordHeader = fields[i].getAnnotation(RecordHeader.class);
					if (recordHeader == null || StringUtils.isBlank(recordHeader.name())) {
						cell.setCellValue(fields[i].getName());
					} else {
						cell.setCellValue(recordHeader.name());
					}

				}
			}
			for (DataRecord record : records) {
				row = sh.createRow(rowNum + 1);
				int colnum = 0;
				for (Field field : fields) {
					String fieldName = field.getName();
					Cell cell = row.createCell(colnum);
					Method method = null;
					try {
						method = clazz.getMethod("get" + StringUtils.capitalize(fieldName));
					} catch (NoSuchMethodException nme) {
						method = clazz.getMethod("get" + fieldName);
					}
					Object value = method.invoke(record, (Object[]) null);
					cell.setCellValue((String) value);
					colnum++;
				}
				rowNum++;
			}
			wb.write(os);
		} catch (Exception ex) {

		}
	}

}
