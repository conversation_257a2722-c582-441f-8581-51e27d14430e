/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.utils;

import java.util.List;

/**
 * <AUTHOR> Lam
 *
 */
public class Pagination<T> {

	private Long total;
	private int page;
	private int pageSize;
	private List<T> data;

	/**
	 * @return the totalItems
	 */
	public Long getTotal() {
		return total;
	}

	/**
	 * @param totalItems the totalItems to set
	 */
	public void setTotal(Long totalItems) {
		this.total = totalItems;
	}

	/**
	 * @return the currentPage
	 */
	public int getPage() {
		return page;
	}

	/**
	 * @param currentPage the currentPage to set
	 */
	public void setPage(int currentPage) {
		this.page = currentPage;
	}

	/**
	 * @return the pageSize
	 */
	public int getPageSize() {
		return pageSize;
	}

	/**
	 * @param pageSize the pageSize to set
	 */
	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	/**
	 * @return the data
	 */
	public List<T> getData() {
		return data;
	}

	/**
	 * @param data the data to set
	 */
	public void setData(List<T> data) {
		this.data = data;
	}

	/**
	 * @param totalItems
	 * @param currentPage
	 * @param pageSize
	 * @param data
	 */
	public Pagination(long totalItems, int currentPage, int pageSize, List<T> data) {
		super();
		this.total = totalItems;
		this.page = currentPage;
		this.pageSize = pageSize;
		this.data = data;
	}

	/**
	 * 
	 */
	public Pagination() {

	}

}
