/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.utils;

import java.util.Collections;
import java.util.List;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;


/**
 * <AUTHOR> Yee
 *
 */
public class JAXBUtils {

	private static final ObjectMapper objectMapper = new ObjectMapper()
			.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

	/**
	 * @param obj
	 * @return
	 */
	public static <T> T unmarshal(String json, Class<T> clazz) {
		try {
			return objectMapper.readValue(json, clazz);
		} catch (Exception e) {
			//logger.error("Unable to unmarshal json string", e);
			throw new RuntimeException(e.getMessage());
		}
	}

	/**
	 * @param obj
	 * @return
	 */
	public static String marshal(Object obj) {
		try {
			String json = objectMapper.writeValueAsString(obj);
			//logger.debug("JSON Serialization: \n" + json);
			System.out.println(json);
			return json;
		} catch (Exception e) {
			//logger.error("Unable to marshal object", e);
			throw new RuntimeException(e.getMessage());
		}
	}

	@SuppressWarnings("unchecked")
	public static <T> List<T> clone(Iterable<T> obj) {
		String marshal = marshal(obj);
		//logger.debug("Clone Result: \n" + marshal);
		if (obj.iterator().hasNext()) {
			return (List<T>) unmarshal(marshal, obj.iterator().next().getClass());
		} else {
			return Collections.emptyList();
		}
	}

	@SuppressWarnings("unchecked")
	public static <T> T clone(T obj) {
		T cloned = (T) unmarshal(marshal(obj), obj.getClass());
		//logger.debug("Clone Result: \n" + marshal(cloned));
		return cloned;
	}

}
