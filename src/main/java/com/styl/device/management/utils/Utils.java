/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.utils;

import java.io.IOException;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.boot.json.JsonParseException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.device.management.persistence.software.update.mode.UpdateMode;

/**
 * <AUTHOR> Lam
 *
 */
public class Utils {

	private static final String ACTIVATION_CODE = "0123456789";
	private static final String CHARACTERS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
	private static SecureRandom random = new SecureRandom();

	public static final int PAGE_SIZE_DEFAULT = 10;
	public static final int PAGE_SIZE_MAX = 100;

	public static String generateChallenge(int size) {
		return generateToken(ACTIVATION_CODE, size);
	}

	public static String generateToken(int size) {
		return generateToken(CHARACTERS, size);
	}

	public static String generateToken(String tokenSpace, int size) {
		StringBuilder builder = new StringBuilder(size);
		for (int i = 0; i < size; i++) {
			builder.append(tokenSpace.charAt(random.nextInt(tokenSpace.length())));
		}
		return builder.toString();
	}

	public static void validatePagination(int page, int pageSize) {
		if (page < 0) {
			page = 0;
		}
		if (pageSize <= 0) {
			pageSize = PAGE_SIZE_DEFAULT;
		} else if (pageSize > 100) {
			pageSize = PAGE_SIZE_MAX;
		}
	}

	/**
	 * <p>
	 * Use to convert update modeId to update mode <br>
	 * Example: 1 -> Optional mode
	 * <p>
	 * 
	 * @param updateModeId
	 * @return Update mode
	 */
	public static String convertUpdateMode(Integer updateModeId) {
		switch (updateModeId) {
		case 1:
			return UpdateMode.MANUAL_MODE;
		case 2:
			return UpdateMode.STARTUP_MODE;
		case 3:
			return UpdateMode.FORCE_MODE;
		default:
			throw new IllegalArgumentException("Unexpected value: " + updateModeId);
		}
	}

	public static String mapToJson(Object obj) throws JsonProcessingException {
		ObjectMapper objectMapper = new ObjectMapper();
		return objectMapper.writeValueAsString(obj);
	}

	public static <T> T mapFromJson(String json, Class<T> clazz)
			throws JsonParseException, JsonMappingException, IOException {

		ObjectMapper objectMapper = new ObjectMapper();
		return objectMapper.readValue(json, clazz);
	}

	public static String getDateTimeString(Long timeInMilli, String format) {
		Date date = new Date(timeInMilli);
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
		return simpleDateFormat.format(date);
	}

	public static String convertObjectToJson(Object object) throws JsonProcessingException {
		if (object == null) {
			return null;
		}
		ObjectMapper mapper = new ObjectMapper();
		return mapper.writeValueAsString(object);
	}

	public static <T> T getObject(String data, Class<T> type) throws JsonMappingException, JsonProcessingException {
		T target = null;
		ObjectMapper objectMapper = new ObjectMapper();
		target = objectMapper.readValue(data, type);
		return target;
	}

	public static boolean isSpecialCharacterFree(String str) {
		if (str == null) {
			return false;
		}
		String pattern = "^[a-zA-Z0-9\\-\\.\\#]+$";
		return str.matches(pattern);
	}
}
