/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.utils;

import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.MGF1ParameterSpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.OAEPParameterSpec;
import javax.crypto.spec.PSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR> Lam
 *
 */
public class RSAUtils {

	public static final Logger logger = LoggerFactory.getLogger(RSAUtils.class);

	public static final String RSA_PKCS1PADDING = "RSA/ECB/PKCS1Padding";
	public static final String RSA_OAEPWITHSHA1 = "RSA/ECB/OAEPWithSHA-1AndMGF1Padding";
	public static final String RSA_OAEPWITHSHA256 = "RSA/ECB/OAEPWithSHA-256AndMGF1Padding";

	/**
	 * @param pubKey
	 * @param plainText
	 * @param rsaCipher
	 * @return base64Encode
	 */
	public static String encrypt(String publicKey, String plainText, String rsaCipher) {
		PublicKey pubKey = getPublicKey(publicKey);
		byte[] encryptedData = encrypt(pubKey, plainText.getBytes(), rsaCipher);
		return new String(Base64.getEncoder().encode(encryptedData));
	}

	public static byte[] encrypt(PublicKey pubKey, byte[] plainData, String rsaCipher) {
		byte[] encryptedData = null;

		try {
			if (RSA_OAEPWITHSHA256.equals(rsaCipher)) {
				Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPPadding");
				cipher.init(Cipher.ENCRYPT_MODE, pubKey,
						new OAEPParameterSpec("SHA-256", "MGF1", MGF1ParameterSpec.SHA256, PSource.PSpecified.DEFAULT));
				encryptedData = cipher.doFinal(plainData);
			} else {
				Cipher cipher = Cipher.getInstance(rsaCipher);
				cipher.init(Cipher.ENCRYPT_MODE, pubKey);
				encryptedData = cipher.doFinal(plainData);
			}
		} catch (Exception e) {
			logger.info("Encryption error", e);
		}
		return encryptedData;
	}

	/**
	 * @param privateKey
	 * @param encData
	 * @param rsaCipher
	 * @return
	 */
	public static String decrypt(String privateKey, String encData, String rsaCipher) {
		PrivateKey priKey = getPrivateKey(privateKey);
		byte[] decryptData = decrypt(priKey, Base64.getDecoder().decode(encData.getBytes()), rsaCipher);
		return new String(Base64.getEncoder().encode(decryptData));
	}

	public static byte[] decrypt(PrivateKey pteKey, byte[] encData, String rsaCipher) {
		byte[] clearData = null;
		try {
			if (RSA_OAEPWITHSHA256.equals(rsaCipher)) {
				Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPPadding");
				cipher.init(Cipher.DECRYPT_MODE, pteKey,
						new OAEPParameterSpec("SHA-256", "MGF1", MGF1ParameterSpec.SHA256, PSource.PSpecified.DEFAULT));
				clearData = cipher.doFinal(encData);
			} else {
				Cipher cipher = Cipher.getInstance(rsaCipher);
				cipher.init(Cipher.DECRYPT_MODE, pteKey);
				clearData = cipher.doFinal(encData);
			}
		} catch (Exception e) {
			logger.info("Decryption error", e);
		}
		return clearData;
	}

	public static PublicKey getPublicKey(String base64PublicKey) {
		PublicKey publicKey = null;
		try {
			X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(base64PublicKey.getBytes()));
			KeyFactory keyFactory = KeyFactory.getInstance("RSA");
			publicKey = keyFactory.generatePublic(keySpec);
			return publicKey;
		} catch (NoSuchAlgorithmException e) {
			logger.info("getPublicKey error", e);
		} catch (InvalidKeySpecException e) {
			logger.info("getPublicKey error", e);
		}
		return publicKey;
	}

	public static PrivateKey getPrivateKey(String base64PrivateKey) {
		PrivateKey publicKey = null;
		try {
			PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(
					Base64.getDecoder().decode(base64PrivateKey.getBytes()));
			KeyFactory keyFactory = KeyFactory.getInstance("RSA");
			publicKey = keyFactory.generatePrivate(keySpec);
			return publicKey;
		} catch (NoSuchAlgorithmException e) {
			logger.info("getPublicKey error", e);
		} catch (InvalidKeySpecException e) {
			logger.info("getPublicKey error", e);
		}
		return publicKey;
	}
}
