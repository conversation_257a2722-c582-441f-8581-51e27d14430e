/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.utils;

import java.security.spec.AlgorithmParameterSpec;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR> Lam
 *
 */
public class AESUtils {

	public static final Logger logger = LoggerFactory.getLogger(AESUtils.class);

	public static final String AES_PKCS5PADDING = "AES/CBC/PKCS5Padding";
	public static final String AES_PKCS7PADDING = "AES/CBC/PKCS7Padding";
	public static final int GCM_DEFAULT_TAG_LENGTH = 16;

	// create ASE base64 encode
	public static byte[] generateSymmetricKey() {
		SecretKey secKey = null;
		try {
			KeyGenerator generator = KeyGenerator.getInstance("AES");
			generator.init(256); // The AES key size in number of bits
			secKey = generator.generateKey();
			return secKey.getEncoded();
		} catch (Exception e) {
			logger.info("Cannot generate key", e);
		}
		return null;
	}

	// Encrypt data By AES
	public static String encrypt(String content, byte[] secKey, String cipher, AlgorithmParameterSpec algoSpecs) {
		byte[] byteCipherText = encrypt(content.getBytes(), secKey, cipher, algoSpecs);
		return new String(Base64.getEncoder().encode(byteCipherText));
	}

	// Encrypt data By AES
	public static byte[] encrypt(byte[] content, byte[] decodedKey, String cipher, AlgorithmParameterSpec algoSpecs) {
		byte[] byteCipherText = null;

		try {
			SecretKey originalKey = new SecretKeySpec(decodedKey, 0, decodedKey.length, "AES");
			Cipher aesCipher = Cipher.getInstance(cipher);
			aesCipher.init(Cipher.ENCRYPT_MODE, originalKey, algoSpecs);
			byteCipherText = aesCipher.doFinal(content);
		} catch (Exception e) {
			logger.info("Encryption error", e);
		}
		return byteCipherText;
	}

	/*
	 * DecryptByAes
	 */
	public static String decrypt(String decryptedKey, String encryptedData, String cipher,
			AlgorithmParameterSpec algoSpec) {
		try {
			byte[] decodedKey = Base64.getDecoder().decode(decryptedKey);
			return decrypt(decodedKey, encryptedData, cipher, algoSpec);
		} catch (Exception e) {
			logger.info("Decryption error", e);
		}

		return null;
	}

	/*
	 * DecryptByAes
	 */
	public static String decrypt(byte[] decryptedKey, String encryptedData, String cipher,
			AlgorithmParameterSpec algoSpecs) {
		byte[] decodedEncryptedData = Base64.getDecoder().decode(encryptedData.getBytes());

		return new String(decrypt(decryptedKey, decodedEncryptedData, cipher, algoSpecs));
	}

	/*
	 * DecryptByAes
	 */
	public static byte[] decrypt(byte[] decryptedKey, byte[] decodedEncryptedData, String cipher,
			AlgorithmParameterSpec algoSpecs) {
		try {
			SecretKey originalKey = new SecretKeySpec(decryptedKey, 0, decryptedKey.length, "AES");
			Cipher aesCipher = Cipher.getInstance(cipher);
			aesCipher.init(Cipher.DECRYPT_MODE, originalKey, algoSpecs);
			byte[] byteCipherText = aesCipher.doFinal(decodedEncryptedData);

			return byteCipherText;
		} catch (Exception e) {
			logger.info("Decryption error", e);
		}

		return null;
	}
}
