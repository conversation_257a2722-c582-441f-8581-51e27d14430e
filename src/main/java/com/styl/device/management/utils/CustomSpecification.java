package com.styl.device.management.utils;

import java.time.Instant;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;

/**
 * <AUTHOR>
 *
 */
public abstract class CustomSpecification<T> implements Specification<T> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5769865951520853982L;

	protected boolean isNull(Object object) {
		return null == object;
	}

	protected boolean isNotNull(Object object) {
		return !isNull(object);
	}

	protected boolean isBlank(String value) {
		return StringUtils.isBlank(value);
	}

	protected boolean isNotBlank(String value) {
		return !isBlank(value);
	}

	protected Predicate equals(CriteriaBuilder cb, Path<Object> field, Object value) {
		return cb.equal(field, value);
	}

	protected Predicate notEquals(CriteriaBuilder cb, Path<Object> field, Object value) {
		return cb.notEqual(field, value);
	}

	protected Predicate greaterThan(CriteriaBuilder cb, Path<Long> field, Long value) {
		return cb.greaterThan(field, value);
	}

	protected Predicate greaterThan(CriteriaBuilder cb, Path<Integer> field, Integer value) {
		return cb.greaterThan(field, value);
	}

	protected Predicate greaterThan(CriteriaBuilder cb, Path<Instant> field, Instant value) {
		return cb.greaterThan(field, value);
	}

	protected Predicate greaterThanOrEqualTo(CriteriaBuilder cb, Path<Long> field, Long value) {
		return cb.greaterThanOrEqualTo(field, value);
	}

	protected Predicate greaterThanOrEqualTo(CriteriaBuilder cb, Path<Integer> field, Integer value) {
		return cb.greaterThanOrEqualTo(field, value);
	}

	protected Predicate greaterThanOrEqualTo(CriteriaBuilder cb, Path<Instant> field, Instant value) {
		return cb.greaterThanOrEqualTo(field, value);
	}

	protected Predicate lessThan(CriteriaBuilder cb, Path<Long> field, Long value) {
		return cb.lessThan(field, value);
	}

	protected Predicate lessThan(CriteriaBuilder cb, Path<Integer> field, Integer value) {
		return cb.lessThan(field, value);
	}

	protected Predicate lessThan(CriteriaBuilder cb, Path<Instant> field, Instant value) {
		return cb.lessThan(field, value);
	}

	protected Predicate lessThanOrEqualTo(CriteriaBuilder cb, Path<Long> field, Long value) {
		return cb.lessThanOrEqualTo(field, value);
	}

	protected Predicate lessThanOrEqualTo(CriteriaBuilder cb, Path<Integer> field, Integer value) {
		return cb.lessThanOrEqualTo(field, value);
	}

	protected Predicate lessThanOrEqualTo(CriteriaBuilder cb, Path<Instant> field, Instant value) {
		return cb.lessThanOrEqualTo(field, value);
	}

	protected Predicate like(CriteriaBuilder cb, Path<String> field, String searchTerm) {
		return cb.like(cb.lower(field), "%" + searchTerm.toLowerCase() + "%");
	}

	protected Predicate between(CriteriaBuilder cb, Path<Integer> field, int min, int max) {
		return cb.between(field, min, max);
	}

	protected Predicate between(CriteriaBuilder cb, Path<Long> field, Long min, long max) {
		return cb.between(field, min, max);
	}

	protected Predicate inStringList(Path<String> field, List<String> range) {
		return field.in(range);
	}

	protected Predicate inLongList(Path<Long> field, List<Long> range) {
		return field.in(range);
	}

	protected Predicate inIntegerList(Path<Integer> field, List<Integer> range) {
		return field.in(range);
	}

	protected Predicate and(CriteriaBuilder cb, List<Predicate> predicates) {
		List<Predicate> pre = predicates.stream().filter(x -> x != null).toList();
		return cb.and(pre.toArray(new Predicate[predicates.size()]));
	}

	protected Order orderBy(String direction, CriteriaBuilder cb, Expression<?> expression) {
		if (direction.equalsIgnoreCase(Direction.ASC.name())) {
			return cb.asc(expression);
		} else {
			return cb.desc(expression);
		}
	}
}