/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.utils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.device.management.rest.device.software.DeviceSoftware;

/**
 * <AUTHOR> Lam
 *
 */
@Converter
public class JsonToMapConverter implements AttributeConverter<List<DeviceSoftware>, String> {

	private static final Logger logger = LoggerFactory.getLogger(JsonToMapConverter.class);

	@Override
	public String convertToDatabaseColumn(List<DeviceSoftware> attribute) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			return objectMapper.writeValueAsString(attribute);
		} catch (JsonProcessingException e) {
			logger.error("Could not convert map to json string.");
		}
		return "[]";
	}

	@Override
	public List<DeviceSoftware> convertToEntityAttribute(String dbData) {
		if (dbData == null) {
			return new ArrayList<DeviceSoftware>();
		}
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			return objectMapper.readValue(dbData, new TypeReference<List<DeviceSoftware>>() {
			});
		} catch (IOException e) {
			logger.error("Convert error while trying to convert string(JSON) to map data structure.");
		}
		return new ArrayList<DeviceSoftware>();
	}

}
