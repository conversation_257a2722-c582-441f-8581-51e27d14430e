/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.utils;

import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR> Yee
 *
 */
public class CryptoUtils {

	private static final String HMACSHA256_ALGORITHM = "HmacSHA256";

	public static String hmacSHA256(String secretKey, String text)
			throws NoSuchAlgorithmException, InvalidKeyException {
		Mac sha256HMAC = Mac.getInstance(HMACSHA256_ALGORITHM);
		SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8),
				HMACSHA256_ALGORITHM);
		sha256HMAC.init(secretKeySpec);

		return Base64.getEncoder().encodeToString(sha256HMAC.doFinal(text.getBytes()));
	}

	public static String generateKey(final int byteLen) throws NoSuchAlgorithmException {

		SecureRandom random = new SecureRandom();
		byte bytes[] = new byte[byteLen / 2];
		random.nextBytes(bytes);
		return toHex(bytes).toLowerCase();
	}

	public static String toHex(final byte[] data) {
		final StringBuilder sb = new StringBuilder(data.length * 2);
		for (byte b : data) {
			sb.append(String.format("%02X", b));
		}
		return sb.toString();
	}

}
