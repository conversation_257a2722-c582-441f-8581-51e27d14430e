/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.utils.excel;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import com.styl.device.management.utils.file.DataRecord;
import com.styl.device.management.utils.file.FileReader;
import com.styl.device.management.utils.file.RecordHeader;

/**
 * <AUTHOR>
 *
 */
@Service
public class ExcelReader implements FileReader {

	@Override
	public <T extends DataRecord> List<T> read(InputStream is, Class<T> clazz)
			throws InstantiationException, IllegalAccessException, IllegalArgumentException, InvocationTargetException,
			NoSuchMethodException, SecurityException, IOException, NoSuchFieldException {
		List<T> listRecords = new ArrayList<>();

		// Get workbook
		try (Workbook workbook = new XSSFWorkbook(is)) {

			// Get sheet
			Sheet sheet = workbook.getSheetAt(0);

			// Get all rows
			Iterator<Row> iterator = sheet.rowIterator();
			Field[] headers = null;
			Map<String, Field> declaredFields = new HashMap<>();
			for (Field declaredField : clazz.getDeclaredFields()) {
				RecordHeader recordHeader = declaredField.getAnnotation(RecordHeader.class);
				if (recordHeader != null && StringUtils.isNotBlank(recordHeader.name())) {
					declaredFields.put(recordHeader.name(), declaredField);
				} else {
					declaredFields.put(declaredField.getName(), declaredField);
				}
			}

			while (iterator.hasNext()) {
				Row nextRow = iterator.next();
				// Get all cells
				Iterator<Cell> cellIterator = nextRow.cellIterator();
				if (nextRow.getRowNum() == 0) {
					headers = new Field[nextRow.getLastCellNum()];
					while (cellIterator.hasNext()) {
						Cell cell = cellIterator.next();
						int columnIndex = cell.getColumnIndex();

						String headerName = getCellValue(cell, String.class);

						Field field = declaredFields.get(headerName);
						if (field == null) {
							field = clazz.getDeclaredField(headerName);
						}
						headers[columnIndex] = field;
					}
					continue;
				}

				// Read cells and set value for book object
				T record = clazz.getDeclaredConstructor().newInstance();

				if (!cellIterator.hasNext()) {
					break;
				}
				while (cellIterator.hasNext()) {
					// Read cell
					Cell cell = cellIterator.next();
					int columnIndex = cell.getColumnIndex();
					if (headers == null) {
						break;
					}
					Field field = headers[columnIndex];
					Object cellValue = getCellValue(cell, field.getType());
					if (cellValue == null || cellValue.toString().isEmpty()) {
						continue;
					}
					Method method = null;
					try {
						method = clazz.getMethod("set" + StringUtils.capitalize(field.getName()), field.getType());
					} catch (NoSuchMethodException nme) {
						method = clazz.getMethod("set" + field.getName(), field.getType());
					}

					method.invoke(record, cellValue);

				}

				listRecords.add(record);
			}
		}

		return listRecords;
	}

	// Get cell value
	@SuppressWarnings("unchecked")
	private static <T> T getCellValue(Cell cell, Class<T> responseClass) {
		CellType cellType = cell.getCellType();
		String cellValue = null;
		switch (cellType) {
		case BOOLEAN:
			cellValue = String.valueOf(cell.getBooleanCellValue());
			break;
		case FORMULA:
			Workbook workbook = cell.getSheet().getWorkbook();
			FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
			cellValue = evaluator.evaluate(cell).getStringValue();
			break;
		case NUMERIC:
			cellValue = String.valueOf((long) cell.getNumericCellValue());
			break;
		case STRING:
			cellValue = cell.getStringCellValue();
			break;
		case _NONE:
		case BLANK:
		case ERROR:
			break;
		default:
			break;
		}

		if (cellValue == null) {
			return null;
		} else if (responseClass.equals(String.class)) {
			return (T) cellValue;
		} else if (responseClass.equals(Integer.class)) {
			return (T) Integer.valueOf(cellValue);
		} else if (responseClass.equals(Long.class)) {
			return (T) Long.valueOf(cellValue);
		} else if (responseClass.equals(Double.class)) {
			return (T) Double.valueOf(cellValue);
		} else if (responseClass.equals(Boolean.class)) {
			return (T) Boolean.valueOf(cellValue);
		} else {
			throw new IllegalArgumentException("Record data type is not supported: " + responseClass.getName());
		}
	}

}
