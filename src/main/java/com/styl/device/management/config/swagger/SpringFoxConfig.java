/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.config.swagger;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;

/**
 * <AUTHOR> Lam
 *
 */
@Configuration
public class SpringFoxConfig {

    @Value("${com.styl.device.management.version:}")
    private String versionApp;

    @Bean
    public OpenAPI stylDMSOpenAPI() {
        return new OpenAPI()
                .info(new Info().title("STYL API Documents.")
                        .description("STYL Device Management Service API Document")
                        .version(versionApp)
                        .license(new License().name("STYL Solutions").url("https://styl.solutions/")))
                .externalDocs(new ExternalDocumentation()
                        .description("STYL API Documents")
                        .url("https://styl.solutions/"));
    }

}
