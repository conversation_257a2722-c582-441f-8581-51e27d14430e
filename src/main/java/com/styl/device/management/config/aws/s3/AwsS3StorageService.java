/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.config.aws.s3;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Map;

import com.styl.device.management.rest.portal.admin.pki.ca.TestS3ConnectionRequest;

/**
 * <AUTHOR> Lam
 *
 */
public interface AwsS3StorageService {

	String SOFTWARE_FOLDER = "software";

	boolean fileExist(String filePath);

	URL requestUploadUrl(String filePath, long timeout, String md5checksum) throws IOException;

	URL requestDownloadUrl(String filePath, long timeout) throws IOException;

	void deleteFile(String filePath) throws IOException;

	String storeFile(String filePath, InputStream is, long length, String contentType) throws IOException;

	/**
	 * @param filePath
	 * @param metadata
	 */
	void updateMetadata(String filePath, Map<String, String> metadata);

	void testConnection(TestS3ConnectionRequest request);

	/**
	 * @param filePath
	 * @param bytes
	 * @param contentType
	 * @return
	 * @throws IOException
	 */
	String storeFile(String filePath, byte[] bytes, String contentType) throws IOException;

}
