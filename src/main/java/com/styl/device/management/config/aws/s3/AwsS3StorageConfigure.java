/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.config.aws.s3;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

/**
 * <AUTHOR> Lam
 *
 */
@Configuration
public class AwsS3StorageConfigure {

	private static final Logger logger = LoggerFactory.getLogger(AwsS3StorageConfigure.class);

	@Value("${com.styl.device.management.aws.key}")
	private String accessKeyId;

	@Value("${com.styl.device.management.aws.key.secret}")
	private String accessSecretKey;

	@Value("${com.styl.device.management.aws.region}")
	private String region;

	@Value("${com.styl.device.management.aws.enabled.key:false}")
	private Boolean isEnabledSecretKey;

	@Bean
	public S3Client s3client() {
		logger.info("AWS Enabled secretKey: " + isEnabledSecretKey);
		if (isEnabledSecretKey) {
			return createS3Client(region, accessKeyId, accessSecretKey);
		}
		return createS3Client(region);
	}

	public static S3Client createS3Client(String region) {
		return S3Client.builder().credentialsProvider(DefaultCredentialsProvider.create()).region(Region.of(region))
				.build();
	}

	public static S3Client createS3Client(String region, String accessKeyId, String accessSecretKey) {
		AwsCredentials credentials = AwsBasicCredentials.create(accessKeyId, accessKeyId);
		return S3Client.builder().region(Region.of(region))
				.credentialsProvider(StaticCredentialsProvider.create(credentials)).build();
	}

}
