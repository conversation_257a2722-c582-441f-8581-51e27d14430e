/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.config.aws.secret.manager;

import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.json.JsonParser;
import org.springframework.boot.json.JsonParserFactory;
import org.springframework.boot.logging.DeferredLog;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertySource;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.util.ClassUtils;
import org.springframework.web.context.support.StandardServletEnvironment;

import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;

/**
 * <AUTHOR>
 *
 */
public class SecretManagerConfiguration implements EnvironmentPostProcessor, Ordered {

	private static final DeferredLog logger = new DeferredLog();

	public static final String PROPERTY_SECRET_MANAGER_ENABLED = "aws.secret.manager.enabled";
	public static final String PROPERTY_SECRET_MANAGER_NAME = "aws.secret.manager.name";
	public static final String PROPERTY_SECRET_MANAGER_REGION = "aws.secret.manager.region";

	private static final String SERVLET_ENVIRONMENT_CLASS = "org.springframework.web."
			+ "context.support.StandardServletEnvironment";

	private static final Set<String> SERVLET_ENVIRONMENT_PROPERTY_SOURCES = new LinkedHashSet<>(
			Arrays.asList(StandardServletEnvironment.JNDI_PROPERTY_SOURCE_NAME,
					StandardServletEnvironment.SERVLET_CONTEXT_PROPERTY_SOURCE_NAME,
					StandardServletEnvironment.SERVLET_CONFIG_PROPERTY_SOURCE_NAME));

	private static RuntimeException error = null;

	private static final int MAX_RETRY = 3;

	private static final int RETRY_DELAY_MS = 3000; // milisecond

	/**
	 * The default order for the processor.
	 */
	public static final int DEFAULT_ORDER = Ordered.LOWEST_PRECEDENCE;

	private int order = DEFAULT_ORDER;

	@Override
	public int getOrder() {
		return this.order;
	}

	public void setOrder(int order) {
		this.order = order;
	}

	public static RuntimeException getError() {
		return error;
	}

	@Override
	public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
		application.addInitializers(ctx -> logger.replayTo(SecretManagerConfiguration.class));
		try {
			boolean enabled = Boolean.valueOf(environment
					.resolvePlaceholders("${aws.secret.manager.enabled:${AWS_SECRET_MANAGER_ENABLED:true}}"));
			logger.info("AWS Secret Manager Configuration is " + (enabled ? "enabled" : "disabled"));
			if (!enabled) {
				return;
			}

			logger.info("Getting properties from AWS Secret Manager");
			String regionName = environment
					.resolvePlaceholders("${aws.secret.manager.region:${AWS_SECRET_MANAGER_REGION:}}");
			String secretName = environment
					.resolvePlaceholders("${aws.secret.manager.name:${AWS_SECRET_MANAGER_NAME:}}");
			if (StringUtils.isAnyBlank(regionName, secretName)) {
				error = new RuntimeException("Secret name or region is empty");
				return;
			}

			String secretJson = getSecret(secretName, regionName);
			logger.info("secret json: " + secretJson);

			if (StringUtils.isBlank(secretJson)) {
				error = new RuntimeException("Secret JSON is empty");
				return;
			}
			processJson(environment, secretJson);
		} catch (Exception e) {
			error = new RuntimeException(e);
		}

	}

	private void processJson(ConfigurableEnvironment environment, String json) {
		try {
			JsonParser parser = JsonParserFactory.getJsonParser();
			Map<String, Object> map = parser.parseMap(json);
			if (!map.isEmpty()) {
				addJsonPropertySource(environment, new MapPropertySource("aws.secret.manager", map));
			}
		} catch (Exception ex) {
			error = new RuntimeException("Cannot parse JSON secret value", ex);
			return;
		}
	}

	private String getSecret(String secretName, String regionName) {
		// Create a Secrets Manager client
		SecretsManagerClient client = SecretsManagerClient.builder().region(Region.of(regionName)).build();

		GetSecretValueRequest getSecretValueRequest = GetSecretValueRequest.builder().secretId(secretName).build();

		int counter = 0;
		GetSecretValueResponse getSecretValueResult = null;

		while (getSecretValueResult == null && counter < MAX_RETRY) {
			logger.info("Getting JSON secret from AWS Secret Manager...");
			try {
				counter++;
				getSecretValueResult = client.getSecretValue(getSecretValueRequest);
			} catch (Exception e) {
				logger.error("AWS Secret Manager connection error: " + e.getMessage());
				if (counter < MAX_RETRY) {
					try {
						logger.warn("Retry after " + RETRY_DELAY_MS + "ms");
						Thread.sleep(RETRY_DELAY_MS);
					} catch (InterruptedException e1) {
						logger.warn("Could not sleep: " + e.getMessage());
						Thread.currentThread().interrupt();
					}
				}
			}
		}

		if (getSecretValueResult != null)

		{
			return getSecretValueResult.secretString();
		}
		return null;
	}

	private void addJsonPropertySource(ConfigurableEnvironment environment, PropertySource<?> source) {
		MutablePropertySources sources = environment.getPropertySources();
		String name = findPropertySource(sources);
		if (sources.contains(name)) {
			sources.addBefore(name, source);
		} else {
			sources.addFirst(source);
		}
	}

	private String findPropertySource(MutablePropertySources sources) {
		if (ClassUtils.isPresent(SERVLET_ENVIRONMENT_CLASS, null)) {
			PropertySource<?> servletPropertySource = sources.stream()
					.filter((source) -> SERVLET_ENVIRONMENT_PROPERTY_SOURCES.contains(source.getName())).findFirst()
					.orElse(null);
			if (servletPropertySource != null) {
				return servletPropertySource.getName();
			}
		}
		return StandardEnvironment.SYSTEM_PROPERTIES_PROPERTY_SOURCE_NAME;
	}

}
