/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.config.aws.s3;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.rest.portal.admin.pki.ca.TestS3ConnectionRequest;

import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.CopyObjectRequest;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetUrlRequest;
import software.amazon.awssdk.services.s3.model.HeadBucketRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectResponse;
import software.amazon.awssdk.services.s3.model.NoSuchBucketException;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.s3.model.S3Exception;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

/**
 * <AUTHOR> Lam
 *
 */
@Service
public class AwsS3StorageServiceImpl implements AwsS3StorageService {

	private static final Logger logger = LoggerFactory.getLogger(AwsS3StorageServiceImpl.class);

	public static final String SOFTWARE_FOLDER = "ota/software";

	public static final String REPORT_FOLDER = "report";

	public static final String TRUSTSTORE = "truststore";

	@Autowired
	private S3Client amazonS3Client;

	@Value("${com.styl.device.management.aws.bucket}")
	private String bucket;

	@Override
	public void updateMetadata(String filePath, Map<String, String> metadata) {

		HeadObjectResponse headResponse = amazonS3Client
				.headObject(HeadObjectRequest.builder().bucket(bucket).key(filePath).build());
		if (headResponse == null) {
			return;
		}
		Map<String, String> objectMetadata = headResponse.metadata();
		for (Entry<String, String> entry : metadata.entrySet()) {
			objectMetadata.put(entry.getKey(), entry.getValue());
		}

		CopyObjectRequest request = CopyObjectRequest.builder().sourceBucket(bucket).sourceKey(filePath)
				.destinationBucket(bucket).destinationKey(filePath).metadata(objectMetadata).build();

		amazonS3Client.copyObject(request);
	}

	@Override
	public String storeFile(String filePath, byte[] bytes, String contentType) throws IOException {
		return storeFile(filePath, new ByteArrayInputStream(bytes), bytes.length, contentType);
	}

	@Override
	public String storeFile(String filePath, InputStream is, long length, String contentType) throws IOException {
		Map<String, String> metaData = new HashMap<>();

		if (length > 0) {
			metaData.put("Content-Length", String.valueOf(length));
		}
		if (contentType != null) {
			metaData.put("Content-Type", contentType);
		}
		PutObjectRequest request = PutObjectRequest.builder().bucket(bucket).key(filePath).metadata(metaData).build();
		PutObjectResponse result = amazonS3Client.putObject(request, RequestBody.fromInputStream(is, length));
		if (result != null) {

			return amazonS3Client.utilities().getUrl(GetUrlRequest.builder().bucket(bucket).key(filePath).build())
					.toString();
		}
		return null;
	}

	@Override
	public void deleteFile(String filePath) throws IOException {
		DeleteObjectRequest request = DeleteObjectRequest.builder().bucket(bucket).key(filePath).build();
		amazonS3Client.deleteObject(request);
	}

	@Override
	public URL requestDownloadUrl(String filePath, long timeout) throws IOException {
		try (S3Presigner presigner = S3Presigner.create()) {
			GetObjectRequest getObjectRequest = GetObjectRequest.builder().bucket(bucket).key(filePath).build();
			GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
					.signatureDuration(Duration.ofMillis(timeout)).getObjectRequest(getObjectRequest).build();
			presigner.presignGetObject(presignRequest);
			return presigner.presignGetObject(presignRequest).url();
		}

	}

	@Override
	public URL requestUploadUrl(String filePath, long timeout, String md5checksum) throws IOException {
		try (S3Presigner presigner = S3Presigner.create()) {
			PutObjectRequest putObjectRequest = PutObjectRequest.builder().bucket(bucket).key(filePath).build();
			PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
					.signatureDuration(Duration.ofMillis(timeout)).putObjectRequest(putObjectRequest).build();

			return presigner.presignPutObject(presignRequest).url();
		}
	}

	@Override
	public boolean fileExist(String filePath) {
		try {
			amazonS3Client.headObject(HeadObjectRequest.builder().bucket(bucket).key(filePath).build());
			return true;
		} catch (Exception e) {
			logger.info("Head object fail", e);
		}
		return false;
	}

	@Override
	public void testConnection(TestS3ConnectionRequest request) {
		try {
			S3Client testClient = null;

			if (request.isUseAccessKey()) {
				testClient = AwsS3StorageConfigure.createS3Client(request.getRegion(), request.getAccessKeyId(),
						request.getSecretAccesKey());

			} else {
				testClient = AwsS3StorageConfigure.createS3Client(request.getRegion());
			}

			HeadBucketRequest headBucketRequest = HeadBucketRequest.builder().bucket(request.getBucket()).build();
			testClient.headBucket(headBucketRequest);
		} catch (NoSuchBucketException e) {
			throw new ServiceException(ErrorCode.PKI_TRUSTSTORE_BUCKET_NOT_FOUND);
		} catch (S3Exception e) {
			if (e.statusCode() == 403) {
				throw new ServiceException(ErrorCode.PKI_TRUSTSTORE_CREDENTIALS_INVALID);
			} else {
				throw new ServiceException(ErrorCode.UNKNOWN);
			}
		} catch (Exception e) {
			logger.info("Exception", e);
			throw new ServiceException(ErrorCode.UNKNOWN);
		}
	}
}
