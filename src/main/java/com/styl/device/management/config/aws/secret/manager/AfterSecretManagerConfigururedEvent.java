/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.config.aws.secret.manager;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationPreparedEvent;
import org.springframework.context.ApplicationListener;

/**
 * <AUTHOR>
 *
 */
public class AfterSecretManagerConfigururedEvent implements ApplicationListener<ApplicationPreparedEvent> {

	private static final Logger logger = LoggerFactory.getLogger(AfterSecretManagerConfigururedEvent.class);

	@Override
	public void onApplicationEvent(ApplicationPreparedEvent event) {
		if (SecretManagerConfiguration.getError() != null) {
			logger.info("Secret manager configuration error", SecretManagerConfiguration.getError());
			System.exit(1);
		}
	}
}

