/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.software.version;

import java.util.Set;

import com.styl.device.management.persistence.software.version.SoftwareVersion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 *
 */
public class SoftwareVersionResponse {

	@Schema(description = "Software version Id")
	private Long id;

	@Schema(description = "name of software", example = "POS Software")
	private String name;

	@Schema(description = "packageName of software", example = "com.styl.crb.pos")
	private String packageName;

	@Schema(description = "Description", example = "This is POS software of caribbean project ( sodexo tenant)")
	private String description;

	@Schema(description = "Device model, note: All = all device")
	private Set<String> deviceModel;

	@Schema(description = "Version of software", example = "1.0.0")
	private String version;

	@Schema(description = "Update mode, we have 3 type: \n- Startup update (S): update software in next restart \n- Mandatory update (M): update madatory \n- Optional update(O): User can update later ", example = "M")
	private Integer updateMode;

	private Long uploadedTime;

	private String releaseNote;

	private Boolean assignable;

	public SoftwareVersionResponse(SoftwareVersion softwareVersion) {
		if (softwareVersion != null) {
			this.id = softwareVersion.getId();
			this.name = softwareVersion.getSoftware().getName();
			this.packageName = softwareVersion.getSoftware().getPackageName();
			this.description = softwareVersion.getSoftware().getDescription();
			this.deviceModel = softwareVersion.getSoftware().getListNameDeviceModels();
			this.version = softwareVersion.getVersion();
			this.updateMode = softwareVersion.getUpdateMode().getModeId();
			this.releaseNote = softwareVersion.getReleaseNote();
			this.uploadedTime = softwareVersion.getCreatedTime();
			this.assignable = softwareVersion.isAssignable();
		}
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Set<String> getDeviceModel() {
		return deviceModel;
	}

	public void setDeviceModel(Set<String> deviceModel) {
		this.deviceModel = deviceModel;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public Integer getUpdateMode() {
		return updateMode;
	}

	public void setUpdateMode(Integer updateMode) {
		this.updateMode = updateMode;
	}

	public String getReleaseNote() {
		return releaseNote;
	}

	public void setReleaseNote(String releaseNote) {
		this.releaseNote = releaseNote;
	}

	public Long getUploadedTime() {
		return uploadedTime;
	}

	public void setUploadedTime(Long uploadedTime) {
		this.uploadedTime = uploadedTime;
	}

	public Boolean getAssignable() {
		return assignable;
	}

	public void setAssignable(Boolean assignable) {
		this.assignable = assignable;
	}

}
