/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.pki;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import com.styl.device.management.persistence.pki.certificate.Certificate;
import com.styl.device.management.persistence.pki.certificate.issuer.CertificateIssuer;
import com.styl.device.management.pki.PkiService;
import com.styl.device.management.service.vault.pki.VaultPkiService;

import io.swagger.v3.oas.annotations.Operation;

/**
 * <AUTHOR>
 *
 */

@Controller
@RequestMapping("/public/pki")
public class PKIPublicController {

//	private static final Logger logger = LoggerFactory.getLogger(PKIPublicController.class);

	@Autowired
	private VaultPkiService vaultPkiService;

	@Autowired
	private PkiService pkiService;

	@GetMapping("/authority/{caName}/certificate/{serialNumber}/status")
	@Operation(summary = "Get certificate status")
	public ResponseEntity<String> getStatus(@PathVariable("caName") String caName,
			@PathVariable("serialNumber") String serialNumber) {

		if (StringUtils.isAnyBlank(caName, serialNumber)) {
			return ResponseEntity.ok().body("NOTFOUND");
		}
		String sn = serialNumber.toLowerCase().replaceAll(":", "").replaceAll("..(?!$)", "$0:");

		Certificate certificate = pkiService.findCertificate(caName, sn);
		if (certificate == null) {
			return ResponseEntity.notFound().build();
		}
		if (certificate.isRevoked()) {
			return ResponseEntity.ok().body("REVOKED");
		}

		return ResponseEntity.ok().body("GOOD");
	}

	@GetMapping("/authority/{caName}/issuer/{issuerId}/pem")
	@Operation(summary = "Download issuer Ca Certificate in PEM encoded format")
	public ResponseEntity<ByteArrayResource> downloadIssuerCertificate(@PathVariable("caName") String caName,
			@PathVariable("issuerId") String issuerId) {

		CertificateIssuer issuer = pkiService.findIssuer(caName, issuerId);
		if (issuer == null) {
			return ResponseEntity.notFound().build();
		}
		String pemCert = vaultPkiService.getPemCertificate(vaultPkiService.getRootCaName(), issuer.getSerialNumber())
				.getCertificate();

		String fileName = caName + "_ca_cert.crt";

		HttpHeaders header = new HttpHeaders();
		header.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
		header.add("Cache-Control", "no-cache, no-store, must-revalidate");
		header.add("Pragma", "no-cache");
		header.add("Expires", "0");

		ByteArrayResource resource = new ByteArrayResource(pemCert.getBytes());

		return ResponseEntity.ok().headers(header).contentLength(resource.contentLength())
				.contentType(MediaType.parseMediaType("application/octet-stream")).body(resource);
	}

	@GetMapping("/authority/{caName}/certificate/{serialNumber}/pem")
	@Operation(summary = "Download Certificate in PEM encoded format")
	public ResponseEntity<ByteArrayResource> downloadPemCertificate(@PathVariable("caName") String caName,
			@PathVariable("serialNumber") String serialNumber) {

		Certificate certificate = pkiService.findCertificate(caName, serialNumber);
		if (certificate == null) {
			return ResponseEntity.notFound().build();
		}

		String pemCert = vaultPkiService.getPemCertificate(caName, serialNumber).getCertificate();

		String fileName = certificate.getCommonName().replaceAll("[^\\w]+", "_") + "cert.crt";

		HttpHeaders header = new HttpHeaders();
		header.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
		header.add("Cache-Control", "no-cache, no-store, must-revalidate");
		header.add("Pragma", "no-cache");
		header.add("Expires", "0");

		ByteArrayResource resource = new ByteArrayResource(pemCert.getBytes());

		return ResponseEntity.ok().headers(header).contentLength(resource.contentLength())
				.contentType(MediaType.parseMediaType("application/octet-stream")).body(resource);
	}

}
