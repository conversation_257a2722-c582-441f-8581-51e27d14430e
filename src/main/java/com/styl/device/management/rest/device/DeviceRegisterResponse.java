/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.device;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.styl.device.management.persistence.device.registraion.DeviceRegistration;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 *
 */
public class DeviceRegisterResponse {

	@Schema(description = "challenge: 0 is not challange, 1 is activation code challenge")
	private Integer challengeCode;

	@JsonInclude(value = Include.NON_NULL)
	private String challenge;
	
	@JsonInclude(value = Include.NON_NULL)
	private Long expiryTime;
	
	@JsonInclude(value = Include.NON_NULL)
	private String url;
	
	@JsonInclude(value = Include.NON_NULL)
	private String qrString;
	
	public DeviceRegisterResponse() {
		
	}
	
	public DeviceRegisterResponse(Integer challengeCode) {
		this.challengeCode = challengeCode;
	}

	public DeviceRegisterResponse(DeviceRegistration deviceRegis, String url, String qrString) {
		this.challengeCode = deviceRegis.getChallengeCode();
		this.challenge = deviceRegis.getChallenge();
		this.expiryTime = deviceRegis.getExpiryTime();
		this.url = url;
		this.qrString = qrString;
	}

	public void setChallenge(String challenge) {
		this.challenge = challenge;
	}

	public Long getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(Long expiredTime) {
		this.expiryTime = expiredTime;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getQrString() {
		return qrString;
	}

	public void setQrString(String qrString) {
		this.qrString = qrString;
	}

	public Integer getChallengeCode() {
		return challengeCode;
	}

	public void setChallengeCode(Integer challengeCode) {
		this.challengeCode = challengeCode;
	}

	public String getChallenge() {
		return challenge;
	}

}
