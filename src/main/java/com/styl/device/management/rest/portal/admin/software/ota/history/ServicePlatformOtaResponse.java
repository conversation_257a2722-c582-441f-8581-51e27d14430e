package com.styl.device.management.rest.portal.admin.software.ota.history;

import jakarta.validation.constraints.NotBlank;

import com.styl.device.management.persistence.service.platform.ServicePlatform;

public class ServicePlatformOtaResponse {

	@NotBlank
	private String name;
	private String shortName;
	private String url;

	public ServicePlatformOtaResponse() {
	}

	public ServicePlatformOtaResponse(String name, String shortName, String url) {
		this.name = name;
		this.shortName = shortName;
		this.url = url;
	}

	public ServicePlatformOtaResponse(ServicePlatform sp) {
		this.name = sp.getName();
		this.shortName = sp.getShortName();
		this.url = sp.getUrl();
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

}
