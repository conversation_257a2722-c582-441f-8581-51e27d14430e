/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.device.software;

import java.util.List;

/**
 * <AUTHOR> Lam
 *
 */
public class UpgradeResponse {

	private boolean updates;
	
	private List<SoftwareResponse> softwares;

	/**
	 * @param updates
	 * @param softwares
	 */
	public UpgradeResponse(boolean updates, List<SoftwareResponse> softwares) {
		this.updates = updates;
		this.softwares = softwares;
	}

	public boolean isUpdates() {
		return updates;
	}

	public void setUpdates(boolean updates) {
		this.updates = updates;
	}

	public List<SoftwareResponse> getSoftwares() {
		return softwares;
	}

	public void setSoftwares(List<SoftwareResponse> softwares) {
		this.softwares = softwares;
	}	
}
