package com.styl.device.management.rest.portal.admin.software.ota.history;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.styl.device.management.persistence.software.packages.SoftwarePackagesService;
import com.styl.device.management.utils.Pagination;

import io.swagger.v3.oas.annotations.Parameter;

@RestController
@RequestMapping("/api/management/software/ota/history")
public class OTAHistoryController {

	@Autowired
	SoftwarePackagesService softwarePackagesService;

	@GetMapping
	public ResponseEntity<Pagination<OTAHistoryResponse>> getOtaHistory(
			@RequestParam(name = "page", required = false, defaultValue = "1") int page,
			@RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
			@Parameter(name = "sortBy", description = "assignedBy, assignedTime, lastUpdated, otaState, deviceId, deviceModel, servicePlatformName, softwareName, originalVersion, assignedVersion") @RequestParam(name = "sortBy", required = false, defaultValue = "lastUpdated") String sortBy,
			@RequestParam(name = "orderBy", required = false, defaultValue = "DESC") String orderBy,
			@RequestParam(name = "deviceId", required = false) String deviceId,
			@RequestParam(name = "deviceModel", required = false) String deviceModel,
			@RequestParam(name = "servicePlatformId", required = false) Integer servicePlatformId,
			@RequestParam(name = "softwareId", required = false) Long softwareId,
			@RequestParam(name = "originalVersion", required = false) String originalVersion,
			@RequestParam(name = "assignedVersion", required = false) String assignedVersion,
			@RequestParam(name = "assignedBy", required = false) String assignedBy,
			@RequestParam(name = "otaStates", required = false) List<Integer> otaStates,
			@RequestParam(name = "tags", required = false) List<Integer> tags) {
		return ResponseEntity.ok(
				softwarePackagesService.getOtaHistory(deviceId, deviceModel, servicePlatformId, softwareId, otaStates,
						tags, originalVersion, assignedVersion, assignedBy, page, pageSize, sortBy, orderBy));
	}
}
