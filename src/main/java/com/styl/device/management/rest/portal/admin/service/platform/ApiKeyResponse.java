/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.rest.portal.admin.service.platform;

import com.styl.device.management.persistence.api.key.ApiKey;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Yee
 *
 */
public class ApiKeyResponse {

	@Schema(description = "Id")
	private Long id;

	@Schema(description = "Service Platform Id")
	private Integer servicePlatformId;

	@Schema(description = "Api Key")
	private String apiKey;

	@Schema(description = "Api Key Creation Time")
	private long createdTime;

	@Schema(description = "Api Key Created By")
	private String createdBy;

	@Schema(description = "Api Key Expired Time")
	private Long expiredTime;

	public ApiKeyResponse() {

	}

	public ApiKeyResponse(ApiKey apiKey) {
		this.id = apiKey.getId();
		this.servicePlatformId = apiKey.getServicePlatformId();
		this.apiKey = apiKey.getApiKey();
		this.createdTime = apiKey.getCreatedTime();
		this.createdBy = apiKey.getCreatedBy();
		this.expiredTime = apiKey.getExpiredTime();

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getServicePlatformId() {
		return servicePlatformId;
	}

	public void setServicePlatformId(Integer servicePlatformId) {
		this.servicePlatformId = servicePlatformId;
	}

	public String getApiKey() {
		return apiKey;
	}

	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}

	public long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Long getExpiredTime() {
		return expiredTime;
	}

	public void setExpiredTime(Long expiredTime) {
		this.expiredTime = expiredTime;
	}

}
