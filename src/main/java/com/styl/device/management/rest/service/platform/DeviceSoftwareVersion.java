/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.rest.service.platform;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Yee
 *
 */
public class DeviceSoftwareVersion {

	@Schema(description = "Device id", example = "deviceId")
	private String deviceUId;

	@Schema(description = "Software version id", example = "1")
	private Long softwareVersionId;

	public DeviceSoftwareVersion() {

	}

	public DeviceSoftwareVersion(String deviceUId, Long softwareVersionId) {
		this.deviceUId = deviceUId;
		this.softwareVersionId = softwareVersionId;
	}

	public String getDeviceUId() {
		return deviceUId;
	}

	public void setDeviceUId(String deviceUId) {
		this.deviceUId = deviceUId;
	}

	public Long getSoftwareVersionId() {
		return softwareVersionId;
	}

	public void setSoftwareVersionId(Long softwareVersionId) {
		this.softwareVersionId = softwareVersionId;
	}

}
