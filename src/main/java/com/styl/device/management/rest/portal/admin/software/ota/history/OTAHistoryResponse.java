package com.styl.device.management.rest.portal.admin.software.ota.history;

import java.util.Set;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

public class OTAHistoryResponse {

	private Long id;

	@NotBlank
	private String deviceId;

	private String deviceModel;

	@NotNull
	private ServicePlatformOtaResponse servicePlatform;

	@NotBlank
	private String softwareName;

	@NotBlank
	private String originalVersion;

	@NotBlank
	private String assignedVersion;

	@NotBlank
	private String assignedBy;

	@NotBlank
	private Long assignedTime;

	@NotBlank
	private String otaState;

	private String otaRemarks;

	private Long lastUpdated;

	private Set<String> tags;

	public OTAHistoryResponse(String deviceId, String deviceModel, ServicePlatformOtaResponse servicePlatform,
			String softwareName, String originalVersion, String assignedVersion, String assignedBy, Long assignedTime,
			String otaState, String otaRemarks, Long lastUpdated, Set<String> tags) {
		super();
		this.deviceId = deviceId;
		this.deviceModel = deviceModel;
		this.servicePlatform = servicePlatform;
		this.softwareName = softwareName;
		this.originalVersion = originalVersion;
		this.assignedVersion = assignedVersion;
		this.assignedBy = assignedBy;
		this.assignedTime = assignedTime;
		this.otaState = otaState;
		this.otaRemarks = otaRemarks;
		this.lastUpdated = lastUpdated;
		this.tags = tags;
	}

	public OTAHistoryResponse() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public String getDeviceModel() {
		return deviceModel;
	}

	public void setDeviceModel(String deviceModel) {
		this.deviceModel = deviceModel;
	}

	public ServicePlatformOtaResponse getServicePlatform() {
		return servicePlatform;
	}

	public void setServicePlatform(ServicePlatformOtaResponse servicePlatform) {
		this.servicePlatform = servicePlatform;
	}

	public String getSoftwareName() {
		return softwareName;
	}

	public void setSoftwareName(String softwareName) {
		this.softwareName = softwareName;
	}

	public String getOriginalVersion() {
		return originalVersion;
	}

	public void setOriginalVersion(String originalVersion) {
		this.originalVersion = originalVersion;
	}

	public String getAssignedVersion() {
		return assignedVersion;
	}

	public void setAssignedVersion(String assignedVersion) {
		this.assignedVersion = assignedVersion;
	}

	public String getAssignedBy() {
		return assignedBy;
	}

	public void setAssignedBy(String assignedBy) {
		this.assignedBy = assignedBy;
	}

	public Long getAssignedTime() {
		return assignedTime;
	}

	public void setAssignedTime(Long assignedTime) {
		this.assignedTime = assignedTime;
	}

	public String getOtaState() {
		return otaState;
	}

	public void setOtaState(String otaState) {
		this.otaState = otaState;
	}

	public String getOtaRemarks() {
		return otaRemarks;
	}

	public void setOtaRemarks(String otaRemarks) {
		this.otaRemarks = otaRemarks;
	}

	public Long getLastUpdated() {
		return lastUpdated;
	}

	public void setLastUpdated(Long lastUpdated) {
		this.lastUpdated = lastUpdated;
	}

	public Set<String> getTags() {
		return tags;
	}

	public void setTags(Set<String> tags) {
		this.tags = tags;
	}
}
