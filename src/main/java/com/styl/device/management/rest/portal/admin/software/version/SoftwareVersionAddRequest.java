/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.software.version;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 *
 */
public class SoftwareVersionAddRequest {

	@NotBlank
	@Schema(description = "packageName of software", example = "com.styl.crb.pos")
	private String packageName;

	@Schema(description = "Software size")
	private Long softwareSize;

	@NotBlank
	@Schema(description = "Version of software", example = "1.0.0")
	private String version;

	@NotNull
	@Schema(description = "Update mode, we have 3 type: \n- Startup update (S): update software in next restart \n- Mandatory update (M): update madatory \n- Optional update(O): User can update later ", example = "M")
	private Integer updateModeId;

	@Schema(description = "file path of software upload", example = "/ota/software/12345678")
	private String filePath;

	@NotNull
	private MultipartFile software;

	@Schema(description = "checksum", example = "5eb63bbbe01eeed093cb22bb8f5acdc3")
	private String checksum;

	private String signature;

	private String releaseNote;

	/**
	 * @param packageName
	 * @param version
	 * @param updateModeId
	 * @param software
	 * @param checksum
	 * @param signature
	 * @param releaseNote
	 */
	public SoftwareVersionAddRequest(@NotBlank String packageName, @NotBlank String version,
			@NotNull Integer updateModeId, @NotNull MultipartFile software, String checksum, String signature,
			String releaseNote) {
		super();
		this.packageName = packageName;
		this.version = version;
		this.updateModeId = updateModeId;
		this.software = software;
		this.checksum = checksum;
		this.signature = signature;
		this.releaseNote = releaseNote;
	}

	public SoftwareVersionAddRequest(@NotBlank String packageName, @NotBlank String version,
			@NotNull Integer updateModeId, @NotNull String filePath, String checksum, String signature,
			String releaseNote) {
		super();
		this.packageName = packageName;
		this.version = version;
		this.updateModeId = updateModeId;
		this.filePath = filePath;
		this.checksum = checksum;
		this.signature = signature;
		this.releaseNote = releaseNote;
	}

	public SoftwareVersionAddRequest() {

	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public Long getSoftwareSize() {
		return softwareSize;
	}

	public void setSoftwareSize(Long softwareSize) {
		this.softwareSize = softwareSize;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public Integer getUpdateModeId() {
		return updateModeId;
	}

	public void setUpdateModeId(Integer updateModeId) {
		this.updateModeId = updateModeId;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getChecksum() {
		return checksum;
	}

	public void setChecksum(String checksum) {
		this.checksum = checksum;
	}

	public String getReleaseNote() {
		return releaseNote;
	}

	public void setReleaseNote(String releaseNote) {
		this.releaseNote = releaseNote;
	}

	public MultipartFile getSoftware() {
		return software;
	}

	public void setSoftware(MultipartFile software) {
		this.software = software;
	}

	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

}
