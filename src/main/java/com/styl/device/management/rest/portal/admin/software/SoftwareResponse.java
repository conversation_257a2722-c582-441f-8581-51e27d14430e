/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.software;

import java.util.Set;

import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformPortalResponse;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 *
 *         SoftwareDTO
 */
public class SoftwareResponse {

	@Schema(description = "Software Id")
	private Long id;

	@Schema(description = "name of software", example = "POS Software")
	private String name;

	@Schema(description = "packageName of software", example = "com.styl.crb.pos")
	private String packageName;

	@Schema(description = "Description", example = "This is POS software of caribbean project ( sodexo tenant)")
	private String description;

	@Schema(description = "Device model, note: All = all device")
	private Set<String> deviceModel;

	@Schema(description = "ServicePlatformId")
	private ServicePlatformPortalResponse servicePlatform;

	/**
	 * @param id
	 * @param name
	 * @param packageId
	 * @param description
	 * @param deviceModel
	 * @param servicePlatform
	 */
	public SoftwareResponse(Software software) {
		if (software != null) {
			this.id = software.getId();
			this.name = software.getName();
			this.packageName = software.getPackageName();
			this.description = software.getDescription();
			this.deviceModel = software.getListNameDeviceModels();
			if (software.getServicePlatform() != null) {
				this.servicePlatform = new ServicePlatformPortalResponse(software.getServicePlatform());
			}
		}

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Set<String> getDeviceModel() {
		return deviceModel;
	}

	public void setDeviceModel(Set<String> deviceModel) {
		this.deviceModel = deviceModel;
	}

	public ServicePlatformPortalResponse getServicePlatform() {
		return servicePlatform;
	}

	public void setServicePlatform(ServicePlatformPortalResponse servicePlatform) {
		this.servicePlatform = servicePlatform;
	}

}
