/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.tag;

import com.styl.device.management.persistence.tag.Tag;
import com.styl.device.management.persistence.tag.TagRepository.TagWithCountDevice;

/**
 * <AUTHOR> Lam
 *
 */
public class TagResponse {

	private Integer id;
	
	private String name;
	
	private String description;
	
	private Long totalDevices;
	
	public TagResponse() {
		
	}
	
	public TagResponse(Tag tag) {
		this.id = tag.getId();
		this.name=tag.getName();
		this.description=tag.getDescription();
	}
	
	public TagResponse(TagWithCountDevice tag) {
		this.id = tag.getId();
		this.name=tag.getName();
		this.description=tag.getDescription();
		this.totalDevices = tag.getTotalDevices();
	}


	public TagResponse(TagAddRequest tagAdd) {
		this.name = tagAdd.getName();
		this.description = tagAdd.getDescription();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Long getTotalDevices() {
		return totalDevices;
	}

	public void setTotalDevices(Long totalDevices) {
		this.totalDevices = totalDevices;
	}
	
}
