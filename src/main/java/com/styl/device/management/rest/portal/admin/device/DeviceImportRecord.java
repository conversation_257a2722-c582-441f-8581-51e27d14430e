/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.device;

import com.styl.device.management.utils.file.DataRecord;
import com.styl.device.management.utils.file.RecordHeader;

/**
 * <AUTHOR>
 *
 */
public class DeviceImportRecord implements DataRecord {

	@RecordHeader(name = "Hardware-ID")
	private String hardwareId;

	public DeviceImportRecord() {
		super();
	}

	public DeviceImportRecord(String hardwareId) {
		super();
		this.hardwareId = hardwareId;
	}

	public String getHardwareId() {
		return hardwareId;
	}

	public void setHardwareId(String hardwareId) {
		this.hardwareId = hardwareId;
	}

}
