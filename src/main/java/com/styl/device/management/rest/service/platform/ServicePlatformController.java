/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.service.platform;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.styl.device.management.auth.service.platform.AuthenticationContextHolder;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.DeviceRepository;
import com.styl.device.management.persistence.device.DeviceService;
import com.styl.device.management.persistence.device.DeviceSpecification;
import com.styl.device.management.persistence.service.platform.ServicePlatformService;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.SoftwareRepository;
import com.styl.device.management.persistence.software.SoftwareSpecification;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesRepository;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesSpecification;
import com.styl.device.management.utils.Pagination;
import com.styl.device.management.utils.Utils;

import io.swagger.v3.oas.annotations.Operation;

/**
 * <AUTHOR> Yee
 *
 */
@RestController
@RequestMapping("/serviceplatform")
public class ServicePlatformController {

	@Autowired
	private ServicePlatformService servicePlatformService;

	@Autowired
	private DeviceRepository deviceRepository;

	@Autowired
	private DeviceService deviceService;

	@Autowired
	private SoftwareRepository softwareRepository;

	@Autowired
	private SoftwarePackagesRepository softwarePackageRepository;

	private Integer getServicePlatformId() {
		return AuthenticationContextHolder.getServicePlatformId();
	}

	@GetMapping(path = "/detail")
	@Operation(description = "Get service platform detail", summary = "Get service platform detail")
	public ResponseEntity<ServicePlatformDetailResponse> getDetail() {
		return ResponseEntity.ok(new ServicePlatformDetailResponse(
				servicePlatformService.findByServicePlatformId(getServicePlatformId())));
	}

	@GetMapping(path = "/device")
	@Operation(description = "List devices", summary = "List devices assigned to service platform")
	public ResponseEntity<Pagination<DeviceByServicePlatformResponse>> listDeviceByServicePlatform(
			@RequestParam(value = "page", required = false, defaultValue = "0") int page,
			@RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize,
			@RequestParam(value = "sort", required = false, defaultValue = "id") String orderBy,
			@RequestParam(value = "ordering", required = false, defaultValue = "asc") String orderDirection,
			@RequestParam(value = "id", required = false) String id,
			@RequestParam(value = "model", required = false, defaultValue = "") String model,
			@RequestParam(value = "hardwareId", required = false, defaultValue = "") String hardwareId,
			@RequestParam(value = "state", required = false) Integer state,
			@RequestParam(value = "simId", required = false, defaultValue = "") String simId,
			@RequestParam(value = "imei", required = false, defaultValue = "") String imei,
			@RequestParam(value = "registrationFrom", required = false) Long registrationFrom,
			@RequestParam(value = "registrationTo", required = false) Long registrationTo) {

		Utils.validatePagination(page, pageSize);
		Sort sort = null;
		if ("model".equalsIgnoreCase(orderBy)) {
			orderBy = "model.model";
		}
		if ("registrationFrom".equalsIgnoreCase(orderBy) || "registrationTo".equalsIgnoreCase(orderBy)
				|| "registrationTime".equalsIgnoreCase(orderBy)) {
			orderBy = "createdTime";
		}
		if (orderDirection.equalsIgnoreCase("ASC")) {
			sort = Sort.by(orderBy).ascending();
		} else {
			sort = Sort.by(orderBy).descending();
		}
		Pageable paging = PageRequest.of(page, pageSize, sort);

		List<Integer> states = new ArrayList<Integer>();
		if (state != null) {
			states.add(state);
		}
		DeviceSpecification specs = new DeviceSpecification(id, model, getServicePlatformId(), hardwareId, states,
				simId, imei, registrationFrom, registrationTo);

		Page<Device> result = deviceRepository.findAll(specs, paging);

		Long totalItems = result.getTotalElements();

		// transfer to DTO
		List<DeviceByServicePlatformResponse> listResult = result.stream().map(d -> {
			return new DeviceByServicePlatformResponse(d);
		}).collect(Collectors.toList());

		return ResponseEntity
				.ok(new Pagination<DeviceByServicePlatformResponse>(totalItems, page + 1, pageSize, listResult));
	}

	@GetMapping(path = "/device/{id}/software/package/latest")
	@Operation(description = "List latest software packages by device", summary = "List latest software packages by device. One device might have many software assigned, DMS only return one latest package for each software")
	public ResponseEntity<Collection<SoftwarePackagesResponse>> listLatestSoftwarePackagesByDevice(
			@PathVariable("id") String deviceUId) {
		List<Device> deviceList = deviceService.listByServicePlatformAndIds(getServicePlatformId(),
				Collections.singletonList(deviceUId));
		if (deviceList.isEmpty()) {
			throw new ServiceException(ErrorCode.DEVICE_NOT_FOUND);
		}

		SoftwarePackagesSpecification specs = new SoftwarePackagesSpecification(getServicePlatformId(), deviceUId, null,
				null, null, null, null);
		List<SoftwarePackages> result = softwarePackageRepository.findAll(specs);

		Collection<SoftwarePackagesResponse> pckg = result.stream().map(sp -> new SoftwarePackagesResponse(sp))
				.collect(Collectors.groupingBy(SoftwarePackagesResponse::getSoftwareId,
						Collectors.collectingAndThen(
								Collectors.maxBy(Comparator.comparing(SoftwarePackagesResponse::getAssignedTime)),
								(Optional<SoftwarePackagesResponse> sp) -> sp.isPresent() ? sp.get() : null)))
				.values();
		return ResponseEntity.ok(pckg);
	}

	@GetMapping(path = "/software")
	@Operation(description = "List softwares", summary = "List softwares assigned to service platform")
	public ResponseEntity<Pagination<SoftwareByServicePlatformResponse>> listSoftwareByServicePlatform(
			@RequestParam(value = "page", required = false, defaultValue = "0") int page,
			@RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize,
			@RequestParam(value = "sort", required = false, defaultValue = "id") String orderBy,
			@RequestParam(value = "ordering", required = false, defaultValue = "asc") String orderDirection,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(name = "packageName", required = false, defaultValue = "") String packageName,
			@RequestParam(value = "name", required = false, defaultValue = "") String name,
			@RequestParam(value = "description", required = false, defaultValue = "") String description,
			@RequestParam(value = "deviceModels", required = false) Set<String> deviceModels) {

		Utils.validatePagination(page, pageSize);
		Sort sort = null;
		if ("deviceModels".equalsIgnoreCase(orderBy)) {
			orderBy = "deviceModels.model";
		}
		if (orderDirection.equalsIgnoreCase("ASC")) {
			sort = Sort.by(orderBy).ascending();
		} else {
			sort = Sort.by(orderBy).descending();
		}
		if (!orderBy.equals("id")) {
			sort = sort.and(Sort.by("id").ascending());
		}
		Pageable paging = PageRequest.of(page, pageSize, sort);
		SoftwareSpecification specs = new SoftwareSpecification(id, getServicePlatformId(), name, description,
				packageName, deviceModels);
		Page<Software> result = softwareRepository.findAll(specs, paging);

		Long totalItems = result.getTotalElements();

		// transfer to DTO
		List<SoftwareByServicePlatformResponse> listResult = result.stream().map(sv -> {
			return new SoftwareByServicePlatformResponse(sv);
		}).collect(Collectors.toList());

		return ResponseEntity
				.ok(new Pagination<SoftwareByServicePlatformResponse>(totalItems, page + 1, pageSize, listResult));
	}

	@GetMapping(path = "/software/packages")
	@Operation(description = "List devices' software packages", summary = "List devices' software packages")
	public ResponseEntity<Pagination<SoftwarePackagesResponse>> listSoftwarePackagesByServicePlatform(
			@RequestParam(value = "page", required = false, defaultValue = "0") int page,
			@RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize,
			@RequestParam(value = "sort", required = false, defaultValue = "id") String orderBy,
			@RequestParam(value = "ordering", required = false, defaultValue = "asc") String orderDirection,
			@RequestParam(value = "deviceUId", required = false, defaultValue = "") String deviceUId,
			@RequestParam(value = "softwareId", required = false) Long softwareId,
			@RequestParam(value = "softwareName", required = false, defaultValue = "") String softwareName,
			@RequestParam(value = "softwareVersion", required = false, defaultValue = "") String softwareVersion,
			@RequestParam(value = "state", required = false) Integer state,
			@RequestParam(value = "updateMode", required = false) Integer updateMode) {

		Utils.validatePagination(page, pageSize);
		Sort sort = null;
		if ("deviceUId".equalsIgnoreCase(orderBy)) {
			orderBy = "device.id";
		}
		if ("softwareId".equalsIgnoreCase(orderBy)) {
			orderBy = "software.id";
		}
		if ("softwareName".equalsIgnoreCase(orderBy)) {
			orderBy = "software.name";
		}
		if ("softwareVersion".equalsIgnoreCase(orderBy)) {
			orderBy = "softwareVersion.version";
		}
		if (orderDirection.equalsIgnoreCase("ASC")) {
			sort = Sort.by(orderBy).ascending();
		} else {
			sort = Sort.by(orderBy).descending();
		}
		if (!orderBy.equals("id")) {
			sort = sort.and(Sort.by("id").ascending());
		}
		Pageable paging = PageRequest.of(page, pageSize, sort);

		SoftwarePackagesSpecification specs = new SoftwarePackagesSpecification(getServicePlatformId(), deviceUId,
				softwareId, softwareName, softwareVersion, state, updateMode);

		Page<SoftwarePackages> result = softwarePackageRepository.findAll(specs, paging);

		Long totalItems = result.getTotalElements();

		// transfer to DTO
		List<SoftwarePackagesResponse> listResult = result.stream().map(sp -> {
			return new SoftwarePackagesResponse(sp);
		}).collect(Collectors.toList());

		return ResponseEntity.ok(new Pagination<SoftwarePackagesResponse>(totalItems, page + 1, pageSize, listResult));
	}

}
