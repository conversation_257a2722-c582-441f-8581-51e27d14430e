/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.device;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.styl.device.management.persistence.device.DeviceService;

import io.swagger.v3.oas.annotations.Operation;

/**
 * <AUTHOR> Lam
 *
 */
@RestController
@RequestMapping("/device")
public class DeviceController {

	@Autowired
	private DeviceService deviceService;

	@RequestMapping(method = RequestMethod.GET, value = "/time")
	@Operation(summary = "Get Server Time", description = "Get Server Time")
	public ResponseEntity<ServerTime> getServerTime() {
		return ResponseEntity.ok(new ServerTime(System.currentTimeMillis()));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/register")
	@Operation(summary = "Register", description = "Register")
	public ResponseEntity<DeviceRegisterResponse> seflRegister(@Valid @RequestBody DeviceRegister deviceRegister) {
		return ResponseEntity.ok(deviceService.selfRegisterDevice(deviceRegister));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/activation")
	@Operation(summary = "Device activation state", description = "Get device activation state")
	public ResponseEntity<DeviceActivationStateResponse> activateDevice(
			@Valid @RequestBody DeviceActivationStateRequest activattionRequest) {
		return ResponseEntity.ok(deviceService.deviceActivation(activattionRequest.getHardwareId()));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/startup")
	@Operation(summary = "Startup", description = "startup")
	public ResponseEntity<DeviceResponse> startup(@Valid @RequestBody DeviceStartupRequest startupRequest) {
		return ResponseEntity.ok(deviceService.startup(startupRequest));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/certificate/sign")
	@Operation(summary = "Sign Device Certificate")
	public ResponseEntity<SignCertificateResponse> signDeviceCertificate(
			@Valid @RequestBody SignCertificateRequest signCert) {
		return ResponseEntity.ok(deviceService.signCertificate(signCert));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/certificate/renew")
	@Operation(summary = "Renew Device Certificate")
	public ResponseEntity<RenewCertificateRespone> renewCertificate(
			@Valid @RequestBody RenewCertificateRequest renewCertificateRequest) {
		return ResponseEntity.ok(deviceService.renewCert(renewCertificateRequest));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/certificate")
	@Operation(summary = "Get Device Certificate")
	public ResponseEntity<DeviceCertResponse> getCertificate(@Valid @RequestBody DeviceCertRequest deviceCertRequest) {
		return ResponseEntity.ok(deviceService.getDeviceCert(deviceCertRequest));
	}
}
