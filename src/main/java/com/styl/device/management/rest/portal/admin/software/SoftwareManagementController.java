/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.software;

import java.util.Set;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.styl.device.management.error.ErrorResponse;
import com.styl.device.management.persistence.software.SoftwareService;
import com.styl.device.management.utils.Pagination;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

/**
 * <AUTHOR> Lam
 *
 */
@RestController
@RequestMapping("/api/management/software")
public class SoftwareManagementController {

	@Autowired
	private SoftwareService softwareService;

	@RequestMapping(method = RequestMethod.POST, value = "")
	@Operation(summary = "Add Software", description = "Add new software")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = SoftwareResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<SoftwareResponse> addSoftware(@Valid @RequestBody SoftwareAddRequest softwareAddRequest) {
		return ResponseEntity.ok(new SoftwareResponse(softwareService.addSoftware(softwareAddRequest)));
	}

    
    @RequestMapping(method = RequestMethod.PUT, value = "")
    @Operation(summary = "Update Software", description = "Update software")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = SoftwareResponse.class))),
            @ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
    })
    public ResponseEntity<SoftwareResponse> updateSoftware(@Valid @RequestBody SoftwareUpdateRequest softwareUpdateRequest) {
        return ResponseEntity.ok(new SoftwareResponse(softwareService.updateSoftware(softwareUpdateRequest)));
    }
    
    @RequestMapping(method = RequestMethod.DELETE, value = "/{id}")
    @Operation(summary = "Remove Software", description = "Remove software")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = SoftwareResponse.class))),
            @ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
    })
    public ResponseEntity<Boolean> removeSoftware(@PathVariable("id") Long softwareId) {
        return ResponseEntity.ok(softwareService.removeSoftware(softwareId));
    }
    
    @RequestMapping(method = RequestMethod.GET, value = "")
    @Operation(summary = "List Software details", description = "View list software by pagination")
    public ResponseEntity<Pagination<SoftwareResponse>> getListSoftware(
            @RequestParam(name = "page", required = false, defaultValue = "1") int page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
            @RequestParam(name = "packageName", required = false) String packageName,
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "deviceModels", required = false) Set<String> deviceModels,
            @RequestParam(name = "servicePlatformId", required = false) Integer servicePlatformId,
        	@Parameter(name = "sortBy", description = "name, packageId, description, servicePlatform.name, deviceModels.model")
            @RequestParam(name = "sortBy", required = false, defaultValue = "id") String sortBy,
            @RequestParam(name = "order", required = false, defaultValue = "ASC") String order) {
        return ResponseEntity.ok(softwareService.listSoftware(packageName, name, deviceModels, servicePlatformId, sortBy, order, page -1, pageSize));
    }
}
