/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.rest.portal.admin.service.platform.notification.setting;

import java.util.Collection;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.styl.device.management.error.ErrorResponse;
import com.styl.device.management.persistence.service.platform.ServicePlatformService;
import com.styl.device.management.persistence.service.platform.notification.setting.ServicePlatformNotificationSettingService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

/**
 * <AUTHOR> Yee
 *
 */
@RestController
@RequestMapping("/api/management/service-platform/notification-setting")
public class ServicePlatformNotificationSettingController {

	@Autowired
	private ServicePlatformNotificationSettingService spNotificationSettingService;

	@Autowired
	private ServicePlatformService spService;

	@RequestMapping(method = RequestMethod.GET, value = "/{servicePlatformId}")
	@Operation(description = "Get Service Platform Notification Setting", summary = "Get Service Platform Notification Setting")
	public ResponseEntity<ServicePlatformNotificationSettingResponse> getServicePlatformNotificationSetting(
			@PathVariable("servicePlatformId") Integer servicePlatformId) {
		spService.findByServicePlatformId(servicePlatformId);
		return ResponseEntity.ok(new ServicePlatformNotificationSettingResponse(
				spNotificationSettingService.findById(servicePlatformId)));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/{servicePlatformId}")
	@Operation(description = "Add or update Service Platform Notification Setting", summary = "Add or update Service Platform Notification Setting")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = ServicePlatformNotificationSettingResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<ServicePlatformNotificationSettingResponse> manipulate(
			@PathVariable("servicePlatformId") Integer servicePlatformId,
			@Valid @RequestBody ServicePlatformNotificationSettingUpdateRequest request) {
		spService.findByServicePlatformId(servicePlatformId);
		request.setServicePlatformId(servicePlatformId);
		return ResponseEntity
				.ok(new ServicePlatformNotificationSettingResponse(spNotificationSettingService.manipulate(request)));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/{servicePlatformId}/createtopic")
	@Operation(description = "Create Event Topic", summary = "Create event topic")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = ServicePlatformNotificationSettingResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<ServicePlatformNotificationSettingResponse> createTopic(
			@PathVariable("servicePlatformId") Integer servicePlatformId) {
		return ResponseEntity.ok(new ServicePlatformNotificationSettingResponse(
				spNotificationSettingService.createTopic(servicePlatformId)));
	}

	@RequestMapping(method = RequestMethod.GET, value = "/eventversions")
	@Operation(description = "List available event version", summary = "List available event version")
	public ResponseEntity<Collection<String>> listEventVersion() {
		return ResponseEntity.ok(spNotificationSettingService.listEventVersion());
	}

}
