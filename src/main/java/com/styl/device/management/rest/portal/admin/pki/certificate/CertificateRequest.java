/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.pki.certificate;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * <AUTHOR>
 *
 */
public class CertificateRequest {

	@NotEmpty
	private String csr;

	@NotEmpty
	@Pattern(regexp = "^[a-zA-Z0-9.]+$")
	private String commonName;

	@NotNull
	private Long ttl;
	
	public CertificateRequest() {

	}

	public CertificateRequest(@NotEmpty String csr, @NotEmpty String commonName, @NotNull Long ttl) {
		this.csr = csr;
		this.commonName = commonName;
		this.ttl = ttl;
	}

	public String getCsr() {
		return csr;
	}

	public void setCsr(String csr) {
		this.csr = csr;
	}

	public String getCommonName() {
		return commonName;
	}

	public void setCommonName(String commonName) {
		this.commonName = commonName;
	}

	public Long getTtl() {
		return ttl;
	}

	public void setTtl(Long ttl) {
		this.ttl = ttl;
	}

	
}
