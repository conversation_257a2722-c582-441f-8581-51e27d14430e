/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.device;

/**
 * <AUTHOR> Lam
 *
 */
public class SignCertificateResponse {

	private String certificate;
	
	private String serialNumber;
	
	private Long expiryTime;
	
	public SignCertificateResponse() {

	}


	/**
	 * @param certificate
	 * @param serialNumber
	 * @param expiryTime
	 */
	public SignCertificateResponse(String certificate, String serialNumber, Long expiryTime) {
		this.certificate = certificate;
		this.serialNumber = serialNumber;
		this.expiryTime = expiryTime;
	}

	/**
	 * @param certificate
	 * @param expiryTime
	 */
	public SignCertificateResponse(String certificate, Long expiryTime) {
		this.certificate = certificate;
		this.expiryTime = expiryTime;
	}

	public String getCertificate() {
		return certificate;
	}

	public void setCertificate(String certificate) {
		this.certificate = certificate;
	}

	public Long getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(Long expiryTime) {
		this.expiryTime = expiryTime;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}
	
}
