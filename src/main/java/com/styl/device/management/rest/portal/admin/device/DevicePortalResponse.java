/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.device;

import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.rest.device.software.DeviceSoftware;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformPortalResponse;
import com.styl.device.management.rest.portal.admin.tag.TagResponse;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 * 
 *         <p>
 *         device DTO for portal
 *         </p>
 */
public class DevicePortalResponse {

	@Schema(description = "Unique identifier of device", example = "POS-0001")
	private String deviceUid;

	@Schema(description = "Hardware id of device", example = "866bc626030279199eb")
	private String hardwareId;

	@Schema(description = "Service platform")
	@JsonInclude(content = Include.NON_NULL)
	private ServicePlatformPortalResponse servicePlatform;

	@Schema(description = "model of device", example = "E700")
	private String model;

	@Schema(description = "Sim Id")
	private String simId;

	@Schema(description = "imei")
	private String imei;

	@Schema(description = "List tags")
	private List<TagResponse> tags;

	private Long createdTime;

	private String state;

	private List<DeviceSoftware> currentSoftwares;
	
	private boolean allowTransport;
	
	private boolean forceRenew;

	/**
	 * 
	 */
	public DevicePortalResponse() {

	}

	public DevicePortalResponse(Device device) {
		if (device != null) {
			this.deviceUid = device.getId();
			this.hardwareId = device.getHardwareId();
			if (device.getServicePlatform() != null) {
				this.servicePlatform = new ServicePlatformPortalResponse(device.getServicePlatform());
			}
			this.model = device.getModel().getModel();
			this.simId = device.getSimId();
			this.imei = device.getImei();
			this.tags = null;
			this.createdTime = device.getCreatedTime();
			this.state = convertState(device.getState());
			this.currentSoftwares = device.getCurrentSoftwares();
			if (device.getTags() != null) {
				this.tags = device.getTags().stream().map(t -> {
					return new TagResponse(t);
				}).collect(Collectors.toList());
			}
			this.allowTransport = device.isAllowTransport();
			this.forceRenew = device.isForceRenew();
		}
	}

	private String convertState(int stateNumber) {
		switch (stateNumber) {
		case 1:
			return "AVAILABLE";
		case 2:
			return "ASSIGNED";
		case 3:
			return "RETIRED";
		}
		throw new ServiceException(ErrorCode.INVALID_FORMAT);
	}

	public DevicePortalResponse(String deviceUid, String hardwareId, ServicePlatformPortalResponse servicePlatform,
			String model, String simId, String imei, List<TagResponse> tags) {
		super();
		this.deviceUid = deviceUid;
		this.hardwareId = hardwareId;
		this.servicePlatform = servicePlatform;
		this.model = model;
		this.simId = simId;
		this.imei = imei;
		this.tags = tags;
	}

	public String getDeviceUid() {
		return deviceUid;
	}

	public void setDeviceUid(String deviceUid) {
		this.deviceUid = deviceUid;
	}

	public String getHardwareId() {
		return hardwareId;
	}

	public void setHardwareId(String hardwareId) {
		this.hardwareId = hardwareId;
	}

	public ServicePlatformPortalResponse getServicePlatform() {
		return servicePlatform;
	}

	public void setServicePlatform(ServicePlatformPortalResponse servicePlatform) {
		this.servicePlatform = servicePlatform;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getSimId() {
		return simId;
	}

	public void setSimId(String simId) {
		this.simId = simId;
	}

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	public List<TagResponse> getTags() {
		return tags;
	}

	public void setTags(List<TagResponse> tags) {
		this.tags = tags;
	}

	public Long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(Long createdTime) {
		this.createdTime = createdTime;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public List<DeviceSoftware> getCurrentSoftwares() {
		return currentSoftwares;
	}

	public void setCurrentSoftwares(List<DeviceSoftware> currentSoftwares) {
		this.currentSoftwares = currentSoftwares;
	}

	public boolean isAllowTransport() {
		return allowTransport;
	}

	public void setAllowTransport(boolean allowTransport) {
		this.allowTransport = allowTransport;
	}

	public boolean isForceRenew() {
		return forceRenew;
	}

	public void setForceRenew(boolean forceRenew) {
		this.forceRenew = forceRenew;
	}	
}
