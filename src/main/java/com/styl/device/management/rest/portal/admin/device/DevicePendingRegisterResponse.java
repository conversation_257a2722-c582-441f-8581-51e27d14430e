/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.device;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.styl.device.management.persistence.device.registraion.DeviceRegistration;

/**
 * <AUTHOR> Lam
 *
 */
public class DevicePendingRegisterResponse {
	
	private Long id;

	private String hardwareId;
	
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String model;
	
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String simId;
	
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String imei;
	
	private Integer challenge;
	
	private Long expiryTime;
	
	private Long registedTime;
	
	public DevicePendingRegisterResponse(DeviceRegistration deviceRegis) {
		this.id = deviceRegis.getId();
		this.hardwareId = deviceRegis.getHardwareId();
		this.model = deviceRegis.getModel();
		this.simId = deviceRegis.getSimId();
		this.imei = deviceRegis.getImei();
		this.challenge = deviceRegis.getChallengeCode();
		this.expiryTime = deviceRegis.getExpiryTime();
		this.registedTime = deviceRegis.getRegistrationTime();
	}
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getHardwareId() {
		return hardwareId;
	}

	public void setHardwareId(String hardwareId) {
		this.hardwareId = hardwareId;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getSimId() {
		return simId;
	}

	public void setSimId(String simId) {
		this.simId = simId;
	}

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	public Integer getChallenge() {
		return challenge;
	}

	public void setChallenge(Integer challenge) {
		this.challenge = challenge;
	}

	public Long getRegistedTime() {
		return registedTime;
	}

	public void setRegistedTime(Long registedTime) {
		this.registedTime = registedTime;
	}

	public Long getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(Long expiryTime) {
		this.expiryTime = expiryTime;
	}
	
}
