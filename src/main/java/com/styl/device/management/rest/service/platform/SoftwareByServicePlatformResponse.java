/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.rest.service.platform;

import java.util.List;
import java.util.Set;

import jakarta.persistence.Column;

import com.styl.device.management.persistence.software.Software;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Yee
 *
 */
public class SoftwareByServicePlatformResponse {

	@Schema(description = "Software id")
	private Long id;

	@Schema(description = "Device model")
	private Set<String> deviceModels;

	@Schema(description = "Service platform id")
	private Integer servicePlatformId;

	@Schema(description = "Package name")
	private String packageName;

	@Schema(description = "Name")
	private String name;

	@Column(name = "Description")
	private String description;

	@Schema(description = "Software version list")
	private List<SoftwareVersionByServicePlatformResponse> softwareVersions;

	public SoftwareByServicePlatformResponse() {

	}

	public SoftwareByServicePlatformResponse(Software software) {
		this.id = software.getId();
		this.deviceModels = software.getDeviceModels() != null ? software.getListNameDeviceModels() : null;
		if (software.getServicePlatform() != null) {
			this.servicePlatformId = software.getServicePlatform().getId();
		}
		this.packageName = software.getPackageName();
		this.name = software.getName();
		this.description = software.getDescription();
		if (software.getSoftwareVersions() != null && !software.getSoftwareVersions().isEmpty()) {
			this.softwareVersions = software.getSoftwareVersions().stream()
					.map(sf -> new SoftwareVersionByServicePlatformResponse(sf)).toList();
		}

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Set<String> getDeviceModels() {
		return deviceModels;
	}

	public void setDeviceModels(Set<String> deviceModels) {
		this.deviceModels = deviceModels;
	}

	public Integer getServicePlatformId() {
		return servicePlatformId;
	}

	public void setServicePlatformId(Integer servicePlatformId) {
		this.servicePlatformId = servicePlatformId;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public List<SoftwareVersionByServicePlatformResponse> getSoftwareVersions() {
		return softwareVersions;
	}

	public void setSoftwareVersions(List<SoftwareVersionByServicePlatformResponse> softwareVersions) {
		this.softwareVersions = softwareVersions;
	}

}
