/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.device;

/**
 * <AUTHOR> Lam
 *
 */
public class DeviceCertResponse {

	private String certificate;
	
	private String serialNumber;
	
	private String certificateKey;
	
	private String exchangedKey;
	
	private Long expiryTime;

	
	/**
	 * 
	 */
	public DeviceCertResponse() {

	}

	/**
	 * @param certificate
	 * @param serialNumber
	 * @param certificateKey
	 * @param exchangedKey
	 * @param expiryTime
	 */
	public DeviceCertResponse(String certificate, String serialNumber, String certificateKey, String exchangedKey, String ivParameter,
			Long expiryTime) {
		this.certificate = certificate;
		this.serialNumber = serialNumber;
		this.certificateKey = certificateKey;
		this.exchangedKey = exchangedKey;
		this.expiryTime = expiryTime;
	}

	public String getCertificate() {
		return certificate;
	}

	public void setCertificate(String certificate) {
		this.certificate = certificate;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getCertificateKey() {
		return certificateKey;
	}

	public void setCertificateKey(String certificateKey) {
		this.certificateKey = certificateKey;
	}

	public String getExchangedKey() {
		return exchangedKey;
	}

	public void setExchangedKey(String exchangedKey) {
		this.exchangedKey = exchangedKey;
	}

	public Long getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(Long expiryTime) {
		this.expiryTime = expiryTime;
	}
}
