/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.software;

import java.util.Set;

import jakarta.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 *
 */
public class SoftwareUpdateRequest {
	
	@NotNull
	@Schema(description = "Id of software version", example = "1")
	private Long id;

	@NotNull
	@Schema(description = "name of software", example = "POS Software")
	private String name;
	
	private Integer servicePlatformId;
	
	@Schema(description = "Description", example = "This is POS software of caribbean project ( sodexo tenant)")
	private String description;
	
	private Set<String> deviceModels;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getServicePlatformId() {
		return servicePlatformId;
	}

	public void setServicePlatformId(Integer servicePlatformId) {
		this.servicePlatformId = servicePlatformId;
	}

	public Set<String> getDeviceModels() {
		return deviceModels;
	}

	public void setDeviceModels(Set<String> deviceModels) {
		this.deviceModels = deviceModels;
	}
}
