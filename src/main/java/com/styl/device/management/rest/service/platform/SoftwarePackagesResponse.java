/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.rest.service.platform;

import jakarta.validation.constraints.NotNull;

import com.styl.device.management.persistence.software.packages.SoftwarePackages;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> <PERSON>e
 *
 */
public class SoftwarePackagesResponse {

	@NotNull
	@Schema(description = "Software packages id")
	private Long id;

	@NotNull
	@Schema(description = "Device id", example = "POS-0001")
	private String deviceUId;

	@NotNull
	@Schema(description = "Software id")
	private Long softwareId;

	@NotNull
	@Schema(description = "Software name")
	private String softwareName;

	@NotNull
	@Schema(description = "Software package name")
	private String softwarePackageName;

	@NotNull
	@Schema(description = "Software version id")
	private Long softwareVersionId;

	@NotNull
	@Schema(description = "Software version")
	private String softwareVersion;

	@NotNull
	@Schema(description = "Software packages state")
	private Integer state;

	@NotNull
	@Schema(description = "Assigned By")
	private String assignedBy;

	@NotNull
	@Schema(description = "Assigned timestamp")
	private Long assignedTime;

	@NotNull
	@Schema(description = "Update mode of software packages")
	private Integer updateMode;

	public SoftwarePackagesResponse() {

	}

	public SoftwarePackagesResponse(SoftwarePackages softwarePackages) {
		this.id = softwarePackages.getId();
		this.deviceUId = softwarePackages.getDevice() != null ? softwarePackages.getDevice().getId() : null;
		this.softwareId = softwarePackages.getSoftware() != null ? softwarePackages.getSoftware().getId() : null;
		this.softwareName = softwarePackages.getSoftware() != null ? softwarePackages.getSoftware().getName() : null;
		this.softwarePackageName = softwarePackages.getSoftware() != null
				? softwarePackages.getSoftware().getPackageName()
				: null;
		this.softwareVersionId = softwarePackages.getSoftwareVersion() != null
				? softwarePackages.getSoftwareVersion().getId()
				: null;
		this.softwareVersion = softwarePackages.getSoftwareVersion() != null
				? softwarePackages.getSoftwareVersion().getVersion()
				: null;
		this.state = softwarePackages.getState();
		this.assignedBy = softwarePackages.getAssignedBy();
		this.assignedTime = softwarePackages.getAssignedTime();
		this.updateMode = softwarePackages.getUpdateMode();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDeviceUId() {
		return deviceUId;
	}

	public void setDeviceUId(String deviceUId) {
		this.deviceUId = deviceUId;
	}

	public Long getSoftwareId() {
		return softwareId;
	}

	public void setSoftwareId(Long softwareId) {
		this.softwareId = softwareId;
	}

	public String getSoftwareName() {
		return softwareName;
	}

	public void setSoftwareName(String softwareName) {
		this.softwareName = softwareName;
	}

	public String getSoftwarePackageName() {
		return softwarePackageName;
	}

	public void setSoftwarePackageName(String softwarePackageName) {
		this.softwarePackageName = softwarePackageName;
	}

	public Long getSoftwareVersionId() {
		return softwareVersionId;
	}

	public void setSoftwareVersionId(Long softwareVersionId) {
		this.softwareVersionId = softwareVersionId;
	}

	public String getSoftwareVersion() {
		return softwareVersion;
	}

	public void setSoftwareVersion(String softwareVersion) {
		this.softwareVersion = softwareVersion;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getAssignedBy() {
		return assignedBy;
	}

	public void setAssignedBy(String assignedBy) {
		this.assignedBy = assignedBy;
	}

	public Long getAssignedTime() {
		return assignedTime;
	}

	public void setAssignedTime(Long assignedTime) {
		this.assignedTime = assignedTime;
	}

	public Integer getUpdateMode() {
		return updateMode;
	}

	public void setUpdateMode(Integer updateMode) {
		this.updateMode = updateMode;
	}

}
