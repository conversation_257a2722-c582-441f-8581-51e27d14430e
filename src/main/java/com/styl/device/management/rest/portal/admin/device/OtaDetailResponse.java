/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.device;

/**
 * <AUTHOR> Lam
 * 
 *         Software of device detail DTO
 */
public class OtaDetailResponse {

	private Long softwarePackageId;

	private String appVersion;

	private String softwareName;

	private String packageName;

	private String assignedSofVersion;

	private String assignedBy;

	private Long assignedTime;

	private String otaState;

	private String otaRemark;

	private Long lastUpdated;

	public Long getSoftwarePackageId() {
		return softwarePackageId;
	}

	public void setSoftwarePackageId(Long softwarePackageId) {
		this.softwarePackageId = softwarePackageId;
	}

	public String getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	public String getSoftwareName() {
		return softwareName;
	}

	public void setSoftwareName(String softwareName) {
		this.softwareName = softwareName;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getAssignedBy() {
		return assignedBy;
	}

	public void setAssignedBy(String assignedBy) {
		this.assignedBy = assignedBy;
	}

	public Long getAssignedTime() {
		return assignedTime;
	}

	public void setAssignedTime(Long assignedTime) {
		this.assignedTime = assignedTime;
	}

	public String getOtaState() {
		return otaState;
	}

	public void setOtaState(String otaState) {
		this.otaState = otaState;
	}

	public String getOtaRemark() {
		return otaRemark;
	}

	public void setOtaRemark(String otaRemark) {
		this.otaRemark = otaRemark;
	}

	public String getAssignedSofVersion() {
		return assignedSofVersion;
	}

	public void setAssignedSofVersion(String assignedSofVersion) {
		this.assignedSofVersion = assignedSofVersion;
	}

	public Long getLastUpdated() {
		return lastUpdated;
	}

	public void setLastUpdated(Long lastUpdated) {
		this.lastUpdated = lastUpdated;
	}

}
