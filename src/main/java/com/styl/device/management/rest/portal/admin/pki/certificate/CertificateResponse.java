/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.pki.certificate;

import com.styl.device.management.persistence.pki.certificate.Certificate;

/**
 * <AUTHOR>
 *
 */
public class CertificateResponse {

	private String serialNumber;

	private String commonName;

	private String caName;

	private String issuerId;

	private long issuedTime;

	private long expiryTime;

	private String certificate;

	public CertificateResponse() {
		super();
	}

	public CertificateResponse(Certificate certificate, String pem) {
		super();
		this.serialNumber = certificate.getSerialNumber();
		this.commonName = certificate.getCommonName();
		this.issuedTime = certificate.getIssuedTime();
		this.expiryTime = certificate.getExpiryTime();
		this.caName = certificate.getCaName();
		this.issuerId = certificate.getIssuerId();
		this.certificate = pem;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getCommonName() {
		return commonName;
	}

	public void setCommonName(String commonName) {
		this.commonName = commonName;
	}

	public String getCaName() {
		return caName;
	}

	public void setCaName(String caName) {
		this.caName = caName;
	}

	public String getIssuerId() {
		return issuerId;
	}

	public void setIssuerId(String issuerId) {
		this.issuerId = issuerId;
	}

	public long getIssuedTime() {
		return issuedTime;
	}

	public void setIssuedTime(long issuedTime) {
		this.issuedTime = issuedTime;
	}

	public long getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(long expiryTime) {
		this.expiryTime = expiryTime;
	}

	public String getCertificate() {
		return certificate;
	}

	public void setCertificate(String certificate) {
		this.certificate = certificate;
	}

}
