/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.device.software;

import java.util.List;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.styl.device.management.persistence.software.version.SoftwareVersionService;

import io.swagger.v3.oas.annotations.Operation;

/**
 * <AUTHOR> Lam
 *
 *         <p>
 *         Software controller for device
 *         </p>
 */
@RestController
@RequestMapping("/device/software")
public class OTAController {

	@Autowired
	SoftwareVersionService softwareVersionService;

	@RequestMapping(method = RequestMethod.POST, value = "")
	@Operation(summary = "Download Software", description = "Download software")
	public ResponseEntity<SoftwareInformation> downloadSoftware(@RequestBody @Valid DownloadRequest request) {
		return ResponseEntity
				.ok(softwareVersionService.downloadSoftware(request.getDeviceUid(), request.getSoftwarePackageId()));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/check")
	@Operation(summary = "Check OTA", description = "Check current software, need to upgrade?")
	public ResponseEntity<UpgradeResponse> checkNeedUpgrade(@RequestBody @Valid UpgradeRequest request) {
		List<SoftwareResponse> softwares = softwareVersionService.ota(request);
		return ResponseEntity.ok(
				softwares.size() == 0 ? new UpgradeResponse(false, softwares) : new UpgradeResponse(true, softwares));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/state")
	@Operation(summary = "Update State OTA")
	public ResponseEntity<?> updateStateOTA(@RequestBody @Valid StateOTARequest request) {
		softwareVersionService.submitStateOTA(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
