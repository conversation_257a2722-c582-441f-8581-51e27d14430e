/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.software;

import java.util.Set;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 *
 */
public class SoftwareAddRequest {

	@NotBlank
    @Schema(description = "name of software", example = "POS Software")
    private String name;
    
    @Schema(description = "Device model, note: All = all device")
    private Set<String> deviceModels;
    
    @NotNull
    @Schema(description = "ServicePlatformId")
    private Integer servicePlatformId;
    
    @NotBlank
    @Pattern(regexp = "^[a-zA-Z0-9._-]+$", message = "packageName not valid")
    @Schema(description = "packageName of software", example = "com.styl.crb.pos")
    private String packageName;
    
    @Schema(description = "Description", example = "This is POS software of caribbean project ( sodexo tenant)")
    private String description;

    public Set<String> getDeviceModels() {
		return deviceModels;
	}

	public void setDeviceModels(Set<String> deviceModels) {
		this.deviceModels = deviceModels;
	}

	public Integer getServicePlatformId() {
        return servicePlatformId;
    }

    public void setServicePlatformId(Integer servicePlatformId) {
        this.servicePlatformId = servicePlatformId;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

}
