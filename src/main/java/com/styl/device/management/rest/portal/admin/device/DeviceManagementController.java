/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.device;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.security.Principal;
import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.styl.device.management.error.ErrorResponse;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.DeviceService;
import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.device.model.DeviceModelRepository;
import com.styl.device.management.persistence.device.registraion.DeviceRegistrationService;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesService;
import com.styl.device.management.utils.Pagination;
import com.styl.device.management.utils.excel.ExcelExporter;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

/**
 * <AUTHOR> Lam
 *
 *         <p>
 *         Device controller for STYL portal
 *         </p>
 */
@RestController
@RequestMapping("/api/management/device")
public class DeviceManagementController {

	@Autowired
	private DeviceService deviceService;

	@Autowired
	private DeviceRegistrationService deviceRegisService;

	@Autowired
	private DeviceModelRepository deviceModelRepository;

	@Autowired
	private SoftwarePackagesService softwarePackagesService;

	@RequestMapping(method = RequestMethod.GET, value = "/{id}/ota-history")
	@Operation(description = "Get OTA history of device", summary = "Get OTA history of device")
	public ResponseEntity<Pagination<OtaDetailResponse>> getOTAHistory(@PathVariable("id") String deviceUid,
			@RequestParam(name = "page", required = false, defaultValue = "1") int page,
			@RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize) {
		return ResponseEntity.ok(deviceService.getOTAHistoryOfDevice(deviceUid, page, pageSize));
	}

	@RequestMapping(method = RequestMethod.DELETE, value = "/{id}/ota-history/{softwarePackageId}/cancel")
	@Operation(description = "Cancel OTA", summary = "CancelOTA")
	public void cancelOTA(@PathVariable("id") String deviceUid,
			@PathVariable("softwarePackageId") Long softwarePackageId, Principal principal) {
		String cancelledBy = principal != null ? principal.getName() : "";
		softwarePackagesService.cancel(deviceUid, softwarePackageId, cancelledBy);
	}

	@RequestMapping(method = RequestMethod.GET, value = "/model")
	@Operation(description = "Get all device model", summary = "Get all device model")
	public ResponseEntity<List<DeviceModel>> getDeviceModels() {
		return ResponseEntity.ok(deviceModelRepository.findAll());
	}

	@RequestMapping(method = RequestMethod.POST, value = "")
	@Operation(description = "Active device from url", summary = "Active Device")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = DevicePortalResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<DevicePortalResponse> activateDevice(@RequestBody DeviceAddRequest deviceAddRequest) {
		Device newDevice = deviceService.activateDeviceWithChallenge(deviceAddRequest.getChallenge());
		return ResponseEntity.ok(new DevicePortalResponse(newDevice));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/assign/service-platform")
	@Operation(description = "Assign device to service-platform", summary = "Assign service-platform")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = Boolean.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<Boolean> assignServicePlatform(
			@RequestBody DeviceAssignRequest assignServicePlatformRequest) {
		deviceService.assignServicePlatform(assignServicePlatformRequest);
		return ResponseEntity.ok(true);
	}

	@RequestMapping(method = RequestMethod.POST, value = "/unassign/service-platform")
	@Operation(description = "Unassign device to service-platform, remove all configure OTA of device for service-platform", summary = "unassign service-platform")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = Boolean.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<DevicePortalResponse> unassignServicePlatform(
			@RequestBody DeviceUnassignRequest unassignServicePlatformRequest) {
		return ResponseEntity
				.ok(new DevicePortalResponse(deviceService.unassignServicePlatform(unassignServicePlatformRequest)));
	}

	@RequestMapping(method = RequestMethod.GET, value = "")
	@Operation(description = "View list device by pagination", summary = "List Device Details")
	public ResponseEntity<Pagination<DevicePortalResponse>> getListDevice(
			@RequestParam(name = "page", required = false, defaultValue = "1") int page,
			@RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
			@RequestParam(name = "deviceUid", required = false, defaultValue = "") String deviceUid,
			@RequestParam(name = "hardwareId", required = false, defaultValue = "") String hardwareId,
			@RequestParam(name = "model", required = false, defaultValue = "") String model,
			@RequestParam(name = "servicePlatformId", required = false) Integer servicePlatformId,
			@RequestParam(name = "simId", required = false, defaultValue = "") String simId,
			@RequestParam(name = "imei", required = false, defaultValue = "") String imei,
			@RequestParam(name = "fromTime", required = false) Long fromTime,
			@RequestParam(name = "toTime", required = false) Long toTime,
			@RequestParam(name = "state", required = false) List<Integer> states,
			@RequestParam(name = "tags", required = false) List<Integer> tags,
			@RequestParam(name = "otaState", required = false) List<Integer> otaState,
			@Parameter(name = "sortBy", description = "id, model, name, servicePlatform.name, hardwareId, simId, imei, createdTime") @RequestParam(name = "sortBy", required = false, defaultValue = "id") String sortBy,
			@RequestParam(name = "order", required = false, defaultValue = "ASC") String order) {
		return ResponseEntity.ok(deviceService.listDevice(deviceUid, hardwareId, model, servicePlatformId, simId, imei,
				fromTime, toTime, states, otaState, tags, sortBy, order, page - 1, pageSize));
	}

	@RequestMapping(method = RequestMethod.GET, value = "{deviceUid}")
	@Operation(description = "View list device by pagination", summary = "List Device Details")
	public ResponseEntity<DevicePortalResponse> getDeviceDetail(@PathVariable(name = "deviceUid") String deviceUid) {

		return ResponseEntity.ok(deviceService.findDevice(deviceUid));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/register")
	@Operation(description = "Register on portal", summary = "Register on portal")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = DevicePortalResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<DevicePendingRegisterResponse> registerOnPortal(
			@RequestBody DeviceRegisterOnPortalRequest deviceRegisterOnPortalRequest) {
		return ResponseEntity.ok(deviceService.registerOnPortal(deviceRegisterOnPortalRequest.getHardwareId()));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/register/bulk")
	@Operation(description = "Register bulk of devices on portal", summary = "Register bulk of devices on portal")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = DevicePortalResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<List<DeviceImportRecord>> bulkRegisterOnPortal(@RequestParam("file") MultipartFile file,
			@RequestParam(name = "servicePlatformId", required = false) Integer servicePlatformId)
			throws InstantiationException, IllegalAccessException, IllegalArgumentException, InvocationTargetException,
			NoSuchMethodException, SecurityException, NoSuchFieldException, IOException {
		return ResponseEntity.ok(deviceService.importWhiteListHardwareId(file.getInputStream(), servicePlatformId));
	}

	@RequestMapping(method = RequestMethod.GET, value = "/register/bulk/template")
	@Operation(description = "Download template", summary = "Download template")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = DevicePortalResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public void testExporttemplate(HttpServletResponse response) throws IOException {
		response.setContentType("application/octet-stream");

		String headerKey = "Content-Disposition";
		String headerValue = "attachment; filename=template.xlsx";
		response.setHeader(headerKey, headerValue);

		List<DeviceImportRecord> listDevice = new ArrayList<>();
		listDevice.add(new DeviceImportRecord("01-123412346789-125458"));
		listDevice.add(new DeviceImportRecord("ESP32-00-FF-F0-A1-B3-C1-00"));

		new ExcelExporter().export(response.getOutputStream(), listDevice, DeviceImportRecord.class, true);
		response.getOutputStream().close();
	}

	@RequestMapping(method = RequestMethod.GET, value = "/pending")
	@Operation(description = "View list device by pagination", summary = "List Device Pending Registration Details")
	public ResponseEntity<Pagination<DevicePendingRegisterResponse>> getListDevicePendingRegistration(
			@RequestParam(name = "hardwareId", required = false, defaultValue = "") String hardwareId,
			@RequestParam(name = "model", required = false, defaultValue = "") String model,
			@RequestParam(name = "simId", required = false, defaultValue = "") String simId,
			@RequestParam(name = "imei", required = false, defaultValue = "") String imei,
			@RequestParam(name = "fromTime", required = false) Long fromTime,
			@RequestParam(name = "toTime", required = false) Long toTime,
			@RequestParam(name = "page", required = false, defaultValue = "1") int page,
			@RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
			@Parameter(name = "sortBy", description = "id, model, hardwareId, simId, imei") @RequestParam(name = "sortBy", required = false, defaultValue = "id") String sortBy,
			@RequestParam(name = "order", required = false, defaultValue = "DESC") String order) {
		return ResponseEntity.ok(deviceRegisService.findDevicePendingRegister(hardwareId, model, simId, imei, fromTime,
				toTime, sortBy, order, page - 1, pageSize));
	}

	@RequestMapping(method = RequestMethod.DELETE, value = "/pending/{id}")
	@Operation(summary = "Delete device pending", description = "Delete device pending")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = Boolean.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<Boolean> removeSoftware(@PathVariable("id") Long devicePendingId) {
		return ResponseEntity.ok(deviceRegisService.deleteDeviceRegister(devicePendingId));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/assign/tags")
	@Operation(description = "Assign tag to device", summary = "Assign tag to device")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = Boolean.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<Boolean> assignTag(@Valid @RequestBody DeviceAssignTagRequest assignTags) {
		return ResponseEntity.ok(deviceService.assignTag(assignTags));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/force-renew")
	@Operation(summary = "Force renew")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = DevicePortalResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<DevicePortalResponse> forceRenew(@RequestBody ForceRenewRequest forceRenewRequest) {
		return ResponseEntity.ok(new DevicePortalResponse(deviceService.forceRenew(forceRenewRequest)));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/allow-transport")
	@Operation(summary = "Allow transport")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = DevicePortalResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<DevicePortalResponse> allowTransport(
			@RequestBody AllowTransportRequest allowTransportRequest) {
		return ResponseEntity.ok(new DevicePortalResponse(deviceService.allowTransport(allowTransportRequest)));
	}

	@RequestMapping(method = RequestMethod.GET, value = "/download")
	@Operation(description = "Download list device to CSV file", summary = "Export Device")
	public ResponseEntity<ExportDeviceResponse> exportDeviceList(
			@RequestParam(name = "deviceUids", required = false) List<String> deviceUids,
			@RequestParam(name = "deviceUid", required = false) String deviceUid,
			@RequestParam(name = "hardwareId", required = false) String hardwareId,
			@RequestParam(name = "model", required = false) String model,
			@RequestParam(name = "servicePlatformId", required = false) Integer servicePlatformId,
			@RequestParam(name = "simId", required = false) String simId,
			@RequestParam(name = "imei", required = false) String imei,
			@RequestParam(name = "fromTime", required = false) Long fromTime,
			@RequestParam(name = "toTime", required = false) Long toTime,
			@RequestParam(name = "state", required = false) List<Integer> states,
			@RequestParam(name = "tags", required = false) List<Integer> tags,
			@RequestParam(name = "otaState", required = false) List<Integer> otaState,
			@Parameter(name = "sortBy", description = "id, model, name, servicePlatform.name, hardwareId, simId, imei, createdTime") @RequestParam(name = "sortBy", required = false, defaultValue = "id") String sortBy,
			@RequestParam(name = "order", required = false, defaultValue = "ASC") String order) {

		ExportDeviceResponse response = deviceService.exportDevice(deviceUids, deviceUid, hardwareId, model,
				servicePlatformId, simId, imei, fromTime, toTime, states, otaState, tags, sortBy, order);

		return ResponseEntity.ok(response);
	}

}
