/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.software.version;

import java.security.Principal;
import java.util.List;
import java.util.Set;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.styl.device.management.error.ErrorResponse;
import com.styl.device.management.persistence.software.update.mode.UpdateMode;
import com.styl.device.management.persistence.software.update.mode.UpdateModeRepository;
import com.styl.device.management.persistence.software.version.SoftwareVersionService;
import com.styl.device.management.rest.portal.admin.device.DevicePortalResponse;
import com.styl.device.management.rest.portal.admin.software.SoftwareResponse;
import com.styl.device.management.utils.Pagination;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

/**
 * <AUTHOR> Lam
 * 
 *         <p>
 *         Software Version controller for STYL portal
 *         </p>
 */
@RestController
@RequestMapping("/api/management/software-version")
public class SoftwareVersionManagementController {

	@Autowired
	private SoftwareVersionService softwareVersionService;

	@Autowired
	private UpdateModeRepository updateModeRepository;

	@RequestMapping(method = RequestMethod.GET, value = "/upload-url")
	@Operation(summary = "Request URL Upload Software", description = "Get url to upload software")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = SoftwareUrlResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<SoftwareUrlResponse> getUrlUploadSoftware(
			@RequestParam(name = "checksum", required = true) String checksum) {
		return ResponseEntity.ok(softwareVersionService.generateURLUploadSoftware(checksum));
	}

	@RequestMapping(method = RequestMethod.POST, value = "", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	@Operation(summary = "Add Software", description = "Add new software version")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = SoftwareResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<SoftwareVersionResponse> addSoftwareVersion(
			@RequestParam(value = "s3PresignedUploadUrl", required = true, defaultValue = "false") boolean s3PresignedUpload,
			@RequestParam(value = "s3FilePath", required = false) String s3FilePath,
			@RequestParam(value = "packageName", required = true) String packageName,
			@RequestParam(value = "version", required = true) String version,
			@RequestParam(value = "updateModeId", required = true) Integer updateModeId,
			@RequestParam(value = "software", required = false) MultipartFile software,
			@Parameter(description = "Base64 MD5 checksum") @RequestParam(value = "checksum", required = true) String checksum,
			@RequestParam(value = "signature", required = false) String signature,
			@RequestParam(value = "releaseNote", required = false) String releaseNote) {
		if (s3PresignedUpload) {
			return ResponseEntity
					.ok(softwareVersionService.addSoftwareVersion(new SoftwareVersionAddRequest(packageName, version,
							updateModeId, s3FilePath, checksum, signature, releaseNote)));
		} else {
			return ResponseEntity
					.ok(softwareVersionService.addSoftwareVersion(new SoftwareVersionAddRequest(packageName, version,
							updateModeId, software, checksum, signature, releaseNote)));
		}
	}

	@RequestMapping(method = RequestMethod.GET, value = "")
	@Operation(summary = "List Software Version details", description = "View list software version by pagination")
	public ResponseEntity<Pagination<SoftwareVersionResponse>> getListSoftware(
			@RequestParam(name = "page", required = false, defaultValue = "1") int page,
			@RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
			@RequestParam(name = "softwareId", required = false) Long softwareId,
			@RequestParam(name = "packageName", required = false) String packageName,
			@RequestParam(name = "version", required = false) String version,
			@RequestParam(name = "updateModeId", required = false) Integer updateModeId,
			@RequestParam(name = "releaseNote", required = false) String releaseNote,
			@RequestParam(name = "fromTime", required = false) Long fromTime,
			@RequestParam(name = "toTime", required = false) Long toTime,
			@Parameter(name = "sortBy", description = "id, version, software.id, software.name, software.packageName, updateMode, createdTime, releaseNote") @RequestParam(name = "sortBy", required = false, defaultValue = "id") String sortBy,
			@RequestParam(name = "order", required = false, defaultValue = "ASC") String order) {
		return ResponseEntity.ok(softwareVersionService.findSoftwareVersionByPagination(softwareId, packageName,
				version, updateModeId, releaseNote, fromTime, toTime, sortBy, order, page - 1, pageSize));
	}

	@RequestMapping(method = RequestMethod.POST, value = "/assign")
	@Operation(summary = "Assign Software By Device", description = "Assign software")
	public ResponseEntity<?> assignSoftware(@RequestBody AssignSoftware assignSoftware, Principal principal) {
		String assignBy = principal != null ? principal.getName() : null;
		softwareVersionService.assignSoftware(assignSoftware, assignBy);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@RequestMapping(method = RequestMethod.GET, value = "/{id}/assign-devices")
	@Operation(summary = "List Device Details can Assign By Software", description = "List Device Details can Assign By Software")
	public ResponseEntity<Set<DevicePortalResponse>> listDeviceCanAssignBySoftware(
			@PathVariable("id") Long softwareVersionId) {
		return ResponseEntity.ok(softwareVersionService.listDeviceCanBeAssignedBySoftwareVersion(softwareVersionId));
	}

	@RequestMapping(method = RequestMethod.GET, value = "/update-mode")
	public ResponseEntity<List<UpdateMode>> findAllUpdateMode() {
		return ResponseEntity.ok(updateModeRepository.findAll());
	}

	@RequestMapping(method = RequestMethod.PUT, value = "")
	@Operation(summary = "Update Software Version", description = "Update software version")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = SoftwareVersionResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<SoftwareVersionResponse> updateSoftwareVersion(
			@Valid @RequestBody SoftwareVersionUpdateRequest updateRequest) {
		return ResponseEntity.ok(softwareVersionService.updateSoftwareVersion(updateRequest));
	}

	@RequestMapping(method = RequestMethod.DELETE, value = "/{id}")
	@Operation(summary = "Remove Software Version", description = "Remove software version")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = SoftwareResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<Boolean> removeSoftware(@PathVariable("id") Long softwareId) {
		return ResponseEntity.ok(softwareVersionService.removeSoftwareVersion(softwareId));
	}

}
