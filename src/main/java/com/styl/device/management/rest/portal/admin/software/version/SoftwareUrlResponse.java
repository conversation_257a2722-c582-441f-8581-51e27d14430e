/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.software.version;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 *
 */
public class SoftwareUrlResponse {

	@Schema(description = "Url to upload log", example = "https://upload-url")
	private String urlUpload;
	
	@Schema(description = "expired link to upload file (timestamp)")
	private Long expired;
	
	 @Schema(description = "file path of software upload", example = "/ota/software/12345678")
	private String filePath;
	
	 
    /**
	 * @param urlUpload
	 * @param expired
	 * @param filePath
	 */
	public SoftwareUrlResponse(String urlUpload, Long expired, String filePath) {
		this.urlUpload = urlUpload;
		this.expired = expired;
		this.filePath = filePath;
	}

	public String getUrlUpload() {
		return urlUpload;
	}

	public void setUrlUpload(String urlUpload) {
		this.urlUpload = urlUpload;
	}

	public Long getExpired() {
		return expired;
	}

	public void setExpired(Long expired) {
		this.expired = expired;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}
	
}
