/******************************************************************************
  *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.pki.issuer;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.styl.device.management.persistence.pki.certificate.issuer.CertificateIssuer;
import com.styl.device.management.pki.PkiService;
import com.styl.device.management.service.vault.pki.RevokeIssuerResponse;
import com.styl.device.management.service.vault.pki.VaultPkiService;
import com.styl.device.management.utils.Pagination;

import io.swagger.v3.oas.annotations.Operation;

/**
 * <AUTHOR> Lam
 *
 */
@RestController
@RequestMapping("/api/management/pki/authority/{caName}/issuer")
public class IssuerManagementController {

	@Autowired
	private VaultPkiService vaultPkiService;

	@Autowired
	private PkiService pkiService;

	@GetMapping("")
	@Operation(summary = "List Issuers")
	public ResponseEntity<Pagination<CertificateIssuer>> listIssuer(
			@RequestParam(name = "page", required = false, defaultValue = "1") int page,
			@RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
			@PathVariable("caName") String caName) {
		return ResponseEntity.ok(pkiService.listIssuers(page - 1, pageSize, caName));
	}

	@PostMapping("")
	@Operation(summary = "Add issuer")
	public CertificateIssuer addIssuer(@PathVariable("caName") String caName,
			@RequestBody @Valid AddIssuerRequest addIssuerRequest) {
		return pkiService.addIssuer(caName, addIssuerRequest.getIssuerName(), addIssuerRequest.getTtl());
	}

	@PutMapping("/{issuerId}/default")
	@Operation(summary = "Set default issuer")
	public void setDefaultIssuer(@PathVariable("caName") String caName, @PathVariable("issuerId") String issuerId) {
		vaultPkiService.setDefaultIssuer(caName, issuerId);
	}

	@PutMapping("/{issuerId}")
	@Operation(summary = "Update issuer")
	public CertificateIssuer updateIssuer(@PathVariable("caName") String caName,
			@PathVariable("issuerId") String issuerId, @RequestBody @Valid UpdateIssuerRequest updateIssuerRequest) {
		return pkiService.updateIssuer(caName, issuerId, updateIssuerRequest);
	}

	@DeleteMapping("/{issuerId}")
	@Operation(summary = "Revoke issuer")
	public RevokeIssuerResponse revokeIssuer(@PathVariable("caName") String caName,
			@PathVariable("issuerId") String issuerId) {
		return pkiService.revokeIssuer(caName, issuerId);
	}

	@PostMapping("/{issuerId}/manual")
	@Operation(summary = "Manually upload issuer to truststore")
	public ResponseEntity<?> uploadIssuer(@PathVariable("caName") String caName,
			@PathVariable("issuerId") String issuerId) {
		pkiService.uploadIssuer(caName, issuerId);
		return ResponseEntity.ok().build();
	}

	@DeleteMapping("/{issuerId}/manual")
	@Operation(summary = "Manually remove issuer to truststore")
	public ResponseEntity<?> removeIssuer(@PathVariable("caName") String caName,
			@PathVariable("issuerId") String issuerId) {
		pkiService.removeIssuer(caName, issuerId);
		return ResponseEntity.ok().build();
	}

}
