/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.device.software;

import java.util.Set;

import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.utils.Utils;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 *
 *	SoftwareResponse for device
 */
public class SoftwareResponse {

	@Schema(description = "software Package Id")
	private Long softwarePackageId;
	
	private String softwareName;
	
	@Schema(description = "packageName of software", example = "com.styl.caribbean.pos")
	private String packageName;
	
	@Schema(description = "Version of software", example = "1.0.0")
	private String version;
	
	@Schema(description = "Device model, note: null = all device")
	private Set<String> deviceModel;
	
	@Schema(description = "Update mode, we have 3 type: \n- Startup update (S): update software in next restart \n- Mandatory update (M): update madatory \n- Optional update(O): User can update later ", example = "true")
	private String updateMode;
	
	/**
	 * 
	 */
	public SoftwareResponse() {
		
	}

	/**
	 * @param versionControlId
	 * @param packageId
	 * @param version
	 * @param deviceModel
	 * @param updateMode
	 */
	public SoftwareResponse(Long softwarePackageId, String packageId, String version, Set<String> deviceModel, String updateMode) {
		this.softwarePackageId = softwarePackageId;
		this.packageName = packageId;
		this.version = version;
		this.deviceModel = deviceModel;
		this.updateMode = updateMode;
	}
	
	public SoftwareResponse(SoftwarePackages softwarePackages) {
		this.softwarePackageId = softwarePackages.getId();
		this.packageName = softwarePackages.getPackageName();
		this.version = softwarePackages.getVersion();
		this.deviceModel = softwarePackages.getSoftware().getListNameDeviceModels();
		this.updateMode = Utils.convertUpdateMode(softwarePackages.getUpdateMode());
		this.softwareName = softwarePackages.getSoftware().getName();
	}

	public Long getSoftwarePackageId() {
		return softwarePackageId;
	}

	public void setSoftwarePackageId(Long softwarePackageId) {
		this.softwarePackageId = softwarePackageId;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageId) {
		this.packageName = packageId;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}	

	public Set<String> getDeviceModel() {
		return deviceModel;
	}

	public void setDeviceModel(Set<String> deviceModel) {
		this.deviceModel = deviceModel;
	}

	public String getUpdateMode() {
		return updateMode;
	}

	public void setUpdateMode(String updateMode) {
		this.updateMode = updateMode;
	}

	public String getSoftwareName() {
		return softwareName;
	}

	public void setSoftwareName(String softwareName) {
		this.softwareName = softwareName;
	}

}
