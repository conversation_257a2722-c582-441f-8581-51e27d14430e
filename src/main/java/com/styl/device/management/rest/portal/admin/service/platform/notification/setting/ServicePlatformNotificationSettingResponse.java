/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.rest.portal.admin.service.platform.notification.setting;

import com.styl.device.management.persistence.service.platform.notification.setting.ServicePlatformNotificationSetting;

/**
 * <AUTHOR> <PERSON>e
 *
 */
public class ServicePlatformNotificationSettingResponse {

	private Integer servicePlatformId;

	private String eventTopic;

	private String eventVersion;

	private Boolean enabledWebhook;

	private String webhookUrl;

	private Long createdTime;

	public ServicePlatformNotificationSettingResponse() {

	}
	
	public ServicePlatformNotificationSettingResponse(ServicePlatformNotificationSetting ns) {
		this.servicePlatformId = ns.getId();
		this.eventTopic=ns.getEventTopic();
		this.eventVersion = ns.getEventVersion();
		this.enabledWebhook = ns.isEnabledWebhook();
		this.webhookUrl = ns.getWebhookUrl();
		this.createdTime = ns.getCreatedTime();

	}

	public Integer getServicePlatformId() {
		return servicePlatformId;
	}

	public void setServicePlatformId(Integer servicePlatformId) {
		this.servicePlatformId = servicePlatformId;
	}

	public String getEventTopic() {
		return eventTopic;
	}

	public void setEventTopic(String eventTopic) {
		this.eventTopic = eventTopic;
	}

	public String getEventVersion() {
		return eventVersion;
	}

	public void setEventVersion(String eventVersion) {
		this.eventVersion = eventVersion;
	}

	public Boolean getEnabledWebhook() {
		return enabledWebhook;
	}

	public void setEnabledWebhook(Boolean enabledWebhook) {
		this.enabledWebhook = enabledWebhook;
	}

	public String getWebhookUrl() {
		return webhookUrl;
	}

	public void setWebhookUrl(String webhookUrl) {
		this.webhookUrl = webhookUrl;
	}

	public Long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(Long createdTime) {
		this.createdTime = createdTime;
	}

}
