/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.service.platform;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.styl.device.management.error.ErrorResponse;
import com.styl.device.management.persistence.api.key.ApiKey;
import com.styl.device.management.persistence.api.key.ApiKeyRepository;
import com.styl.device.management.persistence.api.key.ApiKeyService;
import com.styl.device.management.persistence.api.key.ApiKeySpecification;
import com.styl.device.management.persistence.service.platform.ServicePlatformService;
import com.styl.device.management.utils.Pagination;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

/**
 * <AUTHOR> Lam
 *
 */
@RestController
@RequestMapping("/api/management/service-platform")
public class ServicePlatformManagementController {

	@Autowired
	private ServicePlatformService spService;

	@Autowired
	private ApiKeyService apiKeyService;

	@Autowired
	private ApiKeyRepository apiKeyRepository;

	@RequestMapping(method = RequestMethod.GET, value = "")
	@Operation(description = "Read service-platform pagination", summary = "List Service Platform Details")
	public ResponseEntity<Pagination<ServicePlatformPortalResponse>> getServicePlatforms(
			@RequestParam(name = "page", required = false, defaultValue = "1") int page,
			@RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
			@RequestParam(name = "shortName", required = false) String shortName,
			@RequestParam(name = "name", required = false) String name,
			@RequestParam(name = "url", required = false) String url,
			@RequestParam(name = "contactPhone", required = false) String contactPhone,
			@RequestParam(name = "contactName", required = false) String contactName,
			@RequestParam(name = "contactEmail", required = false) String contactEmail,
			@RequestParam(name = "fromTime", required = false) Long fromTime,
			@RequestParam(name = "toTime", required = false) Long toTime,
			@RequestParam(name = "caName", required = false) String caName,
			@Parameter(name = "sortBy", description = "id, shortName, name, url, createTime, contactPhone, contactName, contactEmail") @RequestParam(name = "sortBy", required = false, defaultValue = "id") String sortBy,
			@RequestParam(name = "order", required = false, defaultValue = "ASC") String order) {
		return ResponseEntity.ok(spService.listServicePlatform(shortName, name, url, contactName, contactEmail,
				contactPhone, fromTime, toTime, caName, sortBy, order, page - 1, pageSize));
	}

	@RequestMapping(method = RequestMethod.POST, value = "")
	@Operation(description = "Add new ServicePlatform", summary = "Add Service Platform")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = ServicePlatformPortalResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<ServicePlatformPortalResponse> addServicePlatform(
			@Valid @RequestBody ServicePlatformAddRequest servicePlatformReponse) {
		return ResponseEntity
				.ok(new ServicePlatformPortalResponse(spService.addServicePlatform(servicePlatformReponse)));
	}

	@RequestMapping(method = RequestMethod.PUT, value = "")
	@Operation(description = "Update ServicePlatform", summary = "Update Service Platform")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = ServicePlatformPortalResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<ServicePlatformPortalResponse> updateServicePlatform(
			@Valid @RequestBody ServicePlatformUpdateRequest servicePlatformUpdateRequest) {
		return ResponseEntity
				.ok(new ServicePlatformPortalResponse(spService.updateServicePlatform(servicePlatformUpdateRequest)));
	}

	@RequestMapping(method = RequestMethod.DELETE, value = "/{id}")
	@Operation(description = "Delete ServicePlatform", summary = "Delete Service Platform")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = String.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<?> deleteServicePlatform(@PathVariable(name = "id") Integer id) {
		return ResponseEntity.ok(spService.removeServicePlatform(id));
	}

	@RequestMapping(method = RequestMethod.GET, value = "/all")
	@Operation(description = "get all service-platform ", summary = "Get All Service Platform")
	public ResponseEntity<List<ServicePlatformPortalResponse>> getAllServicePlatform() {
		return ResponseEntity.ok(spService.listAllSP());
	}

	@GetMapping(path = "{servicePlatformId}/apikey/list")
	@Operation(description = "List API Key of service platform", summary = "Get service platform detail")
	public ResponseEntity<List<ApiKeyResponse>> listApiKey(@PathVariable("servicePlatformId") Integer servicePlatformId,
			@RequestParam(value = "sortBy", required = false, defaultValue = "expiredTime") String orderBy,
			@RequestParam(value = "order", required = false, defaultValue = "desc") String orderDirection) {

		spService.findByServicePlatformId(servicePlatformId);

		Sort sort = null;
		if ("createdFromTime".equalsIgnoreCase(orderBy) || "createdToTime".equalsIgnoreCase(orderBy)) {
			orderBy = "createdTime";
		}
		if ("expiredFromTime".equalsIgnoreCase(orderBy) || "expiredToTime".equalsIgnoreCase(orderBy)) {
			orderBy = "expiredTime";
		}
		if (orderDirection.equalsIgnoreCase("ASC")) {
			sort = Sort.by(orderBy).ascending();
		} else {
			sort = Sort.by(orderBy).descending();
		}
		if (!orderBy.equals("id")) {
			sort = sort.and(Sort.by("id").descending());
		}

		ApiKeySpecification specs = new ApiKeySpecification(servicePlatformId);

		List<ApiKey> result = apiKeyRepository.findAll(specs, sort);

		return ResponseEntity.ok(result.stream().map(k -> new ApiKeyResponse(k)).collect(Collectors.toList()));

	}

	@PostMapping(path = "{servicePlatformId}/apikey/add")
	@Operation(description = "Add Api Key", summary = "Add Api Key")
	public ResponseEntity<ApiKey> addApiKey(@PathVariable("servicePlatformId") Integer servicePlatformId,
			@Valid @RequestBody AddApiKeyRequest request) {
		spService.findByServicePlatformId(servicePlatformId);
		return ResponseEntity.ok(apiKeyService.addApiKey(servicePlatformId, request.getExpiredTime(), ""));
	}

	@DeleteMapping(path = "{servicePlatformId}/apikey/delete/{id}")
	@Operation(description = "Delete Api Key", summary = "Delete Api Key")
	public ResponseEntity<?> deleteApiKey(@PathVariable("servicePlatformId") Integer servicePlatformId,
			@PathVariable("id") Long id) {
		spService.findByServicePlatformId(servicePlatformId);
		apiKeyService.deleteApiKey(servicePlatformId, id);
		return ResponseEntity.ok(HttpStatus.OK);
	}
}
