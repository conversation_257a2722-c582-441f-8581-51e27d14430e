/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.device;

import java.util.List;

import jakarta.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 *
 */
public class DeviceAssignRequest {

	@NotNull
	@Schema(description = "Unique identify of device", example = "POS-0001")
	private List<String> deviceUids;

	@NotNull
	@Schema(description = "Service-Plaform Id")
	private Integer servicePlatformId;

	public List<String> getDeviceUids() {
		return deviceUids;
	}

	public void setDeviceUids(List<String> deviceUids) {
		this.deviceUids = deviceUids;
	}

	public Integer getServicePlatformId() {
		return servicePlatformId;
	}

	public void setServicePlatformId(Integer servicePlatformId) {
		this.servicePlatformId = servicePlatformId;
	}

}
