/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.software.version;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 *
 */
public class AssignSoftware {

	@Schema(description = "Software Version Id", example = "1")
	private Long softwareVersionId;

	@Schema(description = "List device Id assign")
	private List<String> deviceUids;

	public Long getSoftwareVersionId() {
		return softwareVersionId;
	}

	public void setSoftwareVersionId(Long softwareVersionId) {
		this.softwareVersionId = softwareVersionId;
	}

	public List<String> getDeviceUids() {
		return deviceUids;
	}

	public void setDeviceUids(List<String> deviceUids) {
		this.deviceUids = deviceUids;
	}

}
