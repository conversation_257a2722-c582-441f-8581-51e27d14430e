/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.service.platform;

import jakarta.validation.constraints.NotNull;

import com.styl.device.management.persistence.device.Device;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 * 
 *         Device DTO for service-platform
 */
public class DeviceByServicePlatformResponse {

	@NotNull
	@Schema(description = "Device id", example = "POS-0001")
	private String id;

	@NotNull
	@Schema(description = "Service platform id")
	private Integer servicePlatformId;

	@NotNull
	@Schema(description = "Model of device", example = "E700")
	private String model;

	@NotNull
	@Schema(description = "State of device")
	private Integer state;

	@NotNull
	@Schema(description = "Hardware id of device", example = "866bc626030279199eb")
	private String hardwareId;

	@Schema(description = "Sim Identity")
	private String simId;

	@Schema(description = "International Mobile Equipment Identity")
	private String imei;

	@Schema(description = "Registration timestamp of device")
	private Long registrationTime;

	public DeviceByServicePlatformResponse() {

	}

	public DeviceByServicePlatformResponse(Device device) {
		this.id = device.getId();
		if (device.getServicePlatform() != null) {
			this.servicePlatformId = device.getServicePlatform().getId();
		}
		this.model = device.getModel().getModel();
		this.state = device.getState();
		this.hardwareId = device.getHardwareId();
		this.simId = device.getSimId();
		this.imei = device.getImei();
		this.registrationTime = device.getCreatedTime();
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer getServicePlatformId() {
		return servicePlatformId;
	}

	public void setServicePlatformId(Integer servicePlatformId) {
		this.servicePlatformId = servicePlatformId;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getHardwareId() {
		return hardwareId;
	}

	public void setHardwareId(String hardwareId) {
		this.hardwareId = hardwareId;
	}

	public String getSimId() {
		return simId;
	}

	public void setSimId(String simId) {
		this.simId = simId;
	}

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	public Long getRegistrationTime() {
		return registrationTime;
	}

	public void setRegistrationTime(Long registrationTime) {
		this.registrationTime = registrationTime;
	}

}
