/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.device.software;

import com.styl.device.management.persistence.software.version.SoftwareVersion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 * 
 * <p> Software information response for device download </p>
 */
public class SoftwareInformation {

	@Schema(description = "Url to download software", example = "https://software.download")
	private String softwareUrl;
	
	@Schema(description = "Expired Download Time")
	private Long expiryTime;
	
	@Schema(description = "Update mode, we have 3 type: \n- Startup update (S): update software in next restart \n- Mandatory update (M): update madatory \n- Optional update(O): User can update later ", example = "true")
	private String updateMode;

	@Schema(description = "checksum")
	private String checksum;
	
	/**
	 * 
	 */
	public SoftwareInformation() {
	
	}

	/**
	 * @param softwareUrl
	 * @param expiryTime
	 * @param updateMode
	 * @param checksum
	 */
	public SoftwareInformation(String softwareUrl, Long expiryTime, String updateMode, String checksum) {
		this.softwareUrl = softwareUrl;
		this.expiryTime = expiryTime;
		this.updateMode = updateMode;
		this.checksum = checksum;
	}

	public SoftwareInformation(SoftwareVersion softwareVersion, String url, Long expiryTime) {
		this.softwareUrl = url;
		this.expiryTime = expiryTime;
		this.updateMode = softwareVersion.getUpdateMode().getMode();
		this.checksum = softwareVersion.getChecksum();
	}

	public String getSoftwareUrl() {
		return softwareUrl;
	}

	public void setSoftwareUrl(String softwareUrl) {
		this.softwareUrl = softwareUrl;
	}

	public Long getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(Long expiryTime) {
		this.expiryTime = expiryTime;
	}

	public String getUpdateMode() {
		return updateMode;
	}

	public void setUpdateMode(String updateMode) {
		this.updateMode = updateMode;
	}

	public String getChecksum() {
		return checksum;
	}

	public void setChecksum(String checksum) {
		this.checksum = checksum;
	}
	
}
