/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.pki.ca;

/**
 * <AUTHOR>
 *
 */
public class TruststoreConfiguration {

	private boolean autoSync;

	private String bucket;

	private String region;

	private Boolean useAccessKey;

	private String accessKeyId;

	private String secretAccesKey;

	public TruststoreConfiguration(boolean autoSync, String bucket, String region, Boolean useAccessKey,
			String accessKeyId, String secretAccesKey) {
		super();
		this.autoSync = autoSync;
		this.bucket = bucket;
		this.region = region;
		this.useAccessKey = useAccessKey;
		this.accessKeyId = accessKeyId;
		this.secretAccesKey = secretAccesKey;
	}

	public TruststoreConfiguration() {
		super();
	}

	public boolean isAutoSync() {
		return autoSync;
	}

	public void setAutoSync(boolean autoSync) {
		this.autoSync = autoSync;
	}

	public String getBucket() {
		return bucket;
	}

	public void setBucket(String bucket) {
		this.bucket = bucket;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public Boolean getUseAccessKey() {
		return useAccessKey;
	}

	public void setUseAccessKey(Boolean useAccessKey) {
		this.useAccessKey = useAccessKey;
	}

	public String getAccessKeyId() {
		return accessKeyId;
	}

	public void setAccessKeyId(String accessKeyId) {
		this.accessKeyId = accessKeyId;
	}

	public String getSecretAccesKey() {
		return secretAccesKey;
	}

	public void setSecretAccesKey(String secretAccesKey) {
		this.secretAccesKey = secretAccesKey;
	}

}
