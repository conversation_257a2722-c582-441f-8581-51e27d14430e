/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.device;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.styl.device.management.persistence.device.Device;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Lam
 *
 */
public class DeviceResponse {

	@Schema(description = "Unique identifier of device", example = "*********")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String deviceUid;

	@Schema(description = "Hardware id of device", example = "866bc626030279199eb")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String hardwareId;

	@Schema(description = "model of device", example = "E700")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String model;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String simId;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String imei;
	
	private boolean isAllowTransport; 

	private boolean isForceRenew;

	private ServicePlatformResponse servicePlatform;

	public DeviceResponse(Device device) {
		if (device != null) {
			this.deviceUid = device.getId();
			this.hardwareId = device.getHardwareId();
			this.model = device.getModel().getModel();
			this.servicePlatform = device.getServicePlatform() == null ? null
					: new ServicePlatformResponse(device.getServicePlatform());
			this.simId = device.getSimId();
			this.imei = device.getImei();
			this.isAllowTransport = device.isAllowTransport();
			this.isForceRenew = device.isForceRenew();
		}
	}

	public DeviceResponse() {

	}

	public String getDeviceUid() {
		return deviceUid;
	}

	public void setDeviceUid(String deviceUid) {
		this.deviceUid = deviceUid;
	}

	public String getHardwareId() {
		return hardwareId;
	}

	public void setHardwareId(String hardwareId) {
		this.hardwareId = hardwareId;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public ServicePlatformResponse getServicePlatform() {
		return servicePlatform;
	}

	public void setServicePlatform(ServicePlatformResponse servicePlatform) {
		this.servicePlatform = servicePlatform;
	}

	public String getSimId() {
		return simId;
	}

	public void setSimId(String simId) {
		this.simId = simId;
	}

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	@JsonProperty("isAllowTransport")
	public boolean isAllowTransport() {
		return isAllowTransport;
	}

	public void setAllowTransport(boolean isAllowTransport) {
		this.isAllowTransport = isAllowTransport;
	}

	@JsonProperty("isForceRenew")
	public boolean isForceRenew() {
		return isForceRenew;
	}

	public void setForceRenew(boolean isForceRenew) {
		this.isForceRenew = isForceRenew;
	}
	
}
