/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.portal.admin.tag;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.styl.device.management.error.ErrorResponse;
import com.styl.device.management.persistence.tag.TagRepository.TagWithCountDevice;
import com.styl.device.management.persistence.tag.TagService;
import com.styl.device.management.utils.Pagination;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

/**
 * <AUTHOR> Lam
 *
 */
@RestController
@RequestMapping("/api/management/tag")
public class TagManagementController {

	@Autowired
	private TagService tagService;

	@RequestMapping(method = RequestMethod.GET, value = "")
	@Operation(description = "Read tag pagination", summary = "List Tag details")
	public ResponseEntity<Pagination<TagWithCountDevice>> getTags(
			@RequestParam(name = "page", required = false, defaultValue = "1") int page,
			@RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
			@RequestParam(name = "name", required = false) String name,
			@RequestParam(name = "description", required = false) String description,
			@RequestParam(name = "sortBy", required = false, defaultValue = "id") String sortBy,
			@RequestParam(name = "order", required = false, defaultValue = "ASC") String order) {
		return ResponseEntity.ok(tagService.getPagination(name, description, sortBy, order, page - 1, pageSize));
	}

	@RequestMapping(method = RequestMethod.GET, value = "/all")
	@Operation(description = "Read tag pagination", summary = "Get All Tag")
	public ResponseEntity<List<TagResponse>> getAllTags() {
		return ResponseEntity.ok(tagService.findAll());
	}

	@RequestMapping(method = RequestMethod.POST, value = "")
	@Operation(description = "Add new tag", summary = "Add Tag")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = TagResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<TagResponse> addTag(@RequestBody TagAddRequest tagAddRequest) {
		return ResponseEntity.ok(tagService.addTag(tagAddRequest));
	}

	@RequestMapping(method = RequestMethod.PUT, value = "")
	@Operation(description = "Update tag", summary = "Update Tag")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = TagResponse.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<TagResponse> updateTag(@RequestBody TagUpdateRequest tagUpdateRequest) {
		return ResponseEntity.ok(tagService.updateTag(tagUpdateRequest));
	}

	@RequestMapping(method = RequestMethod.DELETE, value = "/{id}")
	@Operation(description = "Delete tag", summary = "Delete Tag")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successful", content = @Content(schema = @Schema(implementation = String.class))),
			@ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class))) })
	public ResponseEntity<?> deleteTag(@PathVariable(name = "id") Integer id) {
		return ResponseEntity.ok(tagService.removeTag(id));
	}
}
