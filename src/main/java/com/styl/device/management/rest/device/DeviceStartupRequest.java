/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.device;

import java.util.List;

import jakarta.validation.constraints.NotBlank;

import com.styl.device.management.rest.device.software.DeviceSoftware;

/**
 * <AUTHOR> Lam
 *
 */
public class DeviceStartupRequest {

	@NotBlank(message = "DeviceUid must be madatory")
	private String deviceUid;

	private List<DeviceSoftware> softwares;

	private String simId;

	private String imei;

	public String getDeviceUid() {
		return deviceUid;
	}

	public void setDeviceUid(String deviceUid) {
		this.deviceUid = deviceUid;
	}

	public List<DeviceSoftware> getSoftwares() {
		return softwares;
	}

	public void setSoftwares(List<DeviceSoftware> softwares) {
		this.softwares = softwares;
	}

	public String getSimId() {
		return simId;
	}

	public void setSimId(String simId) {
		this.simId = simId;
	}

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

}
