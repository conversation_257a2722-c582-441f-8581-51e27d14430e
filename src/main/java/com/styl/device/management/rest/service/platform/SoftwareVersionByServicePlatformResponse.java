/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.rest.service.platform;

import java.util.Set;

import com.styl.device.management.persistence.software.version.SoftwareVersion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Yee
 *
 */
public class SoftwareVersionByServicePlatformResponse {

	@Schema(description = "Software version Id")
	private Long id;

	@Schema(description = "Software id")
	private Long softwareId;

	@Schema(description = "Software package Name", example = "com.styl.crb.pos")
	private String softwarePackageName;

	@Schema(description = "Software device model")
	private Set<String> softwareDeviceModels;

	@Schema(description = "Software version", example = "1.0.0")
	private String version;

	@Schema(description = "Update mode, we have 3 type: \n- Startup update (S): update software in next restart \n- Mandatory update (M): update madatory \n- Optional update(O): User can update later ", example = "M")
	private Integer updateMode;

	private Long uploadedTime;

	private String releaseNote;

	public SoftwareVersionByServicePlatformResponse(SoftwareVersion softwareVersion) {
		if (softwareVersion != null) {
			this.id = softwareVersion.getId();
			this.softwareId = softwareVersion.getSoftware().getId();
			this.softwarePackageName = softwareVersion.getSoftware().getPackageName();
			this.softwareDeviceModels = softwareVersion.getSoftware().getListNameDeviceModels();
			this.version = softwareVersion.getVersion();
			this.updateMode = softwareVersion.getUpdateMode().getModeId();
			this.releaseNote = softwareVersion.getReleaseNote();
			this.uploadedTime = softwareVersion.getCreatedTime();
		}
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getSoftwareId() {
		return softwareId;
	}

	public void setSoftwareId(Long softwareId) {
		this.softwareId = softwareId;
	}

	public String getSoftwarePackageName() {
		return softwarePackageName;
	}

	public void setSoftwarePackageName(String softwarePackageName) {
		this.softwarePackageName = softwarePackageName;
	}

	public Set<String> getSoftwareDeviceModels() {
		return softwareDeviceModels;
	}

	public void setSoftwareDeviceModels(Set<String> softwareDeviceModels) {
		this.softwareDeviceModels = softwareDeviceModels;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public Integer getUpdateMode() {
		return updateMode;
	}

	public void setUpdateMode(Integer updateMode) {
		this.updateMode = updateMode;
	}

	public Long getUploadedTime() {
		return uploadedTime;
	}

	public void setUploadedTime(Long uploadedTime) {
		this.uploadedTime = uploadedTime;
	}

	public String getReleaseNote() {
		return releaseNote;
	}

	public void setReleaseNote(String releaseNote) {
		this.releaseNote = releaseNote;
	}

}
