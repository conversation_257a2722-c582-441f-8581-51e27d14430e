/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.rest.service.platform;

import com.styl.device.management.persistence.service.platform.ServicePlatform;

/**
 * <AUTHOR> Lam
 *
 */
public class ServicePlatformDetailResponse {

	private Integer id;

	private String shortName;

	private String name;

	private String url;

	private String emailContact;

	private String nameContact;

	private String phoneContact;

	private Long createdTime;

	private String description;

	public ServicePlatformDetailResponse() {

	}

	public ServicePlatformDetailResponse(ServicePlatform sp) {
		this.id = sp.getId();
		this.shortName = sp.getShortName();
		this.name = sp.getName();
		this.url = sp.getUrl();
		this.emailContact = sp.getContactEmail();
		this.nameContact = sp.getContactName();
		this.phoneContact = sp.getContactPhone();
		this.createdTime = sp.getCreatedTime();
		this.description = sp.getDescription();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getEmailContact() {
		return emailContact;
	}

	public void setEmailContact(String emailContact) {
		this.emailContact = emailContact;
	}

	public String getNameContact() {
		return nameContact;
	}

	public void setNameContact(String nameContact) {
		this.nameContact = nameContact;
	}

	public String getPhoneContact() {
		return phoneContact;
	}

	public void setPhoneContact(String phoneContact) {
		this.phoneContact = phoneContact;
	}

	public Long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(Long createdTime) {
		this.createdTime = createdTime;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
