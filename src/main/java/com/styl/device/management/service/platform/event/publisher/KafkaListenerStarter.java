/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.platform.event.publisher;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;

/**
 * <AUTHOR>
 *
 */
public class KafkaListenerStarter implements Runnable {

	private static final Logger logger = LoggerFactory.getLogger(KafkaListenerStarter.class);

	private KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry;

	private String listenerId;

	public KafkaListenerStarter(KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry, String listenerId) {
		super();
		this.kafkaListenerEndpointRegistry = kafkaListenerEndpointRegistry;
		this.listenerId = listenerId;
	}

	@Override
	public void run() {
		MessageListenerContainer listener = null;
		while (listener == null) {
			logger.info("Getting listener...");
			listener = kafkaListenerEndpointRegistry.getListenerContainer(listenerId);
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				logger.error("InterruptedException", e);
				Thread.currentThread().interrupt();
			}
		}
		listener.start();
	}

}
