/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.vault.pki;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.vault.authentication.ClientAuthentication;
import org.springframework.vault.client.VaultEndpoint;
import org.springframework.vault.client.VaultResponses;
import org.springframework.vault.core.VaultTemplate;
import org.springframework.vault.support.VaultResponseSupport;
import org.springframework.web.client.HttpStatusCodeException;

/**
 * <AUTHOR>
 *
 */
public class CustomVaultTemplate extends VaultTemplate {

	/**
	 * @param vaultEndpoint
	 * @param clientAuthentication
	 */
	public CustomVaultTemplate(VaultEndpoint vaultEndpoint, ClientAuthentication clientAuthentication) {
		super(vaultEndpoint, clientAuthentication);
	}

	@Nullable
	public <T> VaultResponseSupport<T> write(String path, @Nullable Object body, Class<T> responseType) {

		ParameterizedTypeReference<VaultResponseSupport<T>> ref = VaultResponses.getTypeReference(responseType);

		return doWithSession(restOperations -> {

			try {
				ResponseEntity<VaultResponseSupport<T>> exchange = restOperations.exchange(path, HttpMethod.POST,
						new HttpEntity<>(body), ref);

				return exchange.getBody();
			} catch (HttpStatusCodeException e) {

				if (e.getStatusCode() == HttpStatus.NOT_FOUND) {
					return null;
				}

				throw VaultResponses.buildException(e, path);
			}
		});
	}

}
