/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.service.platform.event.publisher.event.data;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import jakarta.validation.constraints.NotNull;

import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.service.platform.event.publisher.event.common.EventVersion;

/**
 * <AUTHOR> Yee
 *
 */
public class SoftwareAddedEventData implements IEventData {

	@NotNull
	private Long softwareId;

	@NotNull
	private String name;

	@NotNull
	private String packageName;

	private String description;

	@NotNull
	private Set<String> deviceModels;

	public SoftwareAddedEventData() {

	}

	public SoftwareAddedEventData(Software software) {
		this.softwareId = software.getId();
		this.name = software.getName();
		this.packageName = software.getPackageName();
		this.description = software.getDescription();
		this.deviceModels = software.getListNameDeviceModels();
	}

	public Long getSoftwareId() {
		return softwareId;
	}

	public void setSoftwareId(Long softwareId) {
		this.softwareId = softwareId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Set<String> getDeviceModels() {
		return deviceModels;
	}

	public void setDeviceModels(Set<String> deviceModels) {
		this.deviceModels = deviceModels;
	}

	public static Set<String> getEventProperties(EventVersion eventVersion) {
		switch (eventVersion) {
		case V1:
		default:
			return new HashSet<String>(
					Arrays.asList("softwareId", "name", "packageName", "description", "deviceModel"));
		}
	}

}
