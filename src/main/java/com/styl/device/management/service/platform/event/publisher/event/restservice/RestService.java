/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.platform.event.publisher.event.restservice;

import java.io.IOException;
import java.util.Map;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 *
 */
@Component
public class RestService {

	private RestTemplate restTemplate;

	private ObjectMapper objectMapper;

	public RestService() {
		this.restTemplate = new RestTemplateBuilder().build();
		this.objectMapper = new ObjectMapper();
	}

	public <T> RestResponse<T> request(RestRequest request, Class<T> responseClass) {
		String requestBody = null;

		if (request.getBody() != null) {
			try {
				requestBody = objectMapper.writeValueAsString(request.getBody());
			} catch (JsonProcessingException e) {
				throw new RestServiceException(e);
			}
		}

		if (!request.getHeaders().containsKey(HttpHeaders.CONTENT_TYPE)) {
			request.addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
		}
		HttpEntity<?> httpEntity = new HttpEntity<>(requestBody, request.getHeaders());

		RestResponse<T> restResponse = new RestResponse<T>();

		try {
			ResponseEntity<byte[]> responseEntity = restTemplate.exchange(
					buildUriString(request.getUrl(), request.getRequestParams()), request.getHttpMethod(), httpEntity,
					byte[].class, request.getRequestParams());
			restResponse.setHttpStatus(responseEntity.getStatusCode());
			restResponse.setResponseHeaders(responseEntity.getHeaders());

			setSuccessResponse(restResponse, responseEntity, responseClass);
		} catch (RestClientResponseException e) {
			setErrorResponse(restResponse, e.getStatusCode().value(), e.getResponseHeaders(),
					e.getResponseBodyAsByteArray(), responseClass);
		}
		return restResponse;
	}

	public <T> RestResponse<T> get(RestRequest request, Class<T> responseClass) {
		request.setHttpMethod(HttpMethod.GET);
		return request(request, responseClass);
	}

	public <T> RestResponse<T> post(RestRequest request, Class<T> responseClass) {
		request.setHttpMethod(HttpMethod.POST);
		return request(request, responseClass);
	}

	public <T> RestResponse<T> put(RestRequest request, Class<T> responseClass) {
		request.setHttpMethod(HttpMethod.PUT);
		return request(request, responseClass);
	}

	public <T> RestResponse<T> delete(RestRequest request, Class<T> responseClass) {
		request.setHttpMethod(HttpMethod.DELETE);
		return request(request, responseClass);
	}

	private String buildUriString(String url, Map<String, Object> params) {
		UriComponentsBuilder urlTemplate = UriComponentsBuilder.fromHttpUrl(url);
		if (params != null) {
			for (String param : params.keySet()) {
				urlTemplate.queryParam(param, "{" + param + "}");
			}
		}

		return urlTemplate.encode().toUriString();
	}

	private <T> void setSuccessResponse(RestResponse<T> restResponse, ResponseEntity<byte[]> responseEntity,
			Class<T> responseClass) {
		restResponse.setHttpStatus(responseEntity.getStatusCode());
		restResponse.setResponseHeaders(responseEntity.getHeaders());
		if (responseClass != null) {
			restResponse.setResponseBody(getResponseBody(responseEntity.getBody(), responseClass));
		}
	}

	private <T> void setErrorResponse(RestResponse<T> restResponse, int statusCode, HttpHeaders headers, byte[] body,
			Class<T> responseClass) {
		restResponse.setHttpStatus(HttpStatus.valueOf(statusCode));
		restResponse.setResponseHeaders(headers);
		restResponse.setErrorResponse(body);
	}

	/**
	 * @param body
	 * @param responseClass
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private <T> T getResponseBody(byte[] body, Class<T> responseClass) {
		if (responseClass.equals(String.class)) {
			return (T) new String(body);
		} else if (responseClass.equals(Integer.class)) {
			return (T) Integer.valueOf(new String(body));
		} else if (responseClass.equals(Long.class)) {
			return (T) Long.valueOf(new String(body));
		} else if (responseClass.equals(Double.class)) {
			return (T) Double.valueOf(new String(body));
		} else if (responseClass.equals(Boolean.class)) {
			return (T) Boolean.valueOf(new String(body));
		} else {
			try {
				return new ObjectMapper().readValue(body, responseClass);
			} catch (StreamReadException e) {
				throw new RestServiceException(e);
			} catch (DatabindException e) {
				throw new RestServiceException(e);
			} catch (IOException e) {
				throw new RestServiceException(e);
			}
		}
	}
}