/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.platform.event.publisher.event.restservice;

import java.io.IOException;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;

import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 *
 */
public class RestResponse<T> {

	private HttpStatusCode httpStatus;

	private HttpHeaders responseHeaders;

	private T responseBody;

	private byte[] errorResponse;

	/**
	 * @return the httpStatus
	 */
	public HttpStatusCode getHttpStatus() {
		return httpStatus;
	}

	/**
	 * @param httpStatus the httpStatus to set
	 */
	public void setHttpStatus(HttpStatusCode httpStatus) {
		this.httpStatus = httpStatus;
	}

	/**
	 * @return the responseHeaders
	 */
	public HttpHeaders getResponseHeaders() {
		return responseHeaders;
	}

	/**
	 * @param responseHeaders the responseHeaders to set
	 */
	public void setResponseHeaders(HttpHeaders responseHeaders) {
		this.responseHeaders = responseHeaders;
	}

	/**
	 * @return the responseBody
	 */
	public T getResponseBody() {
		return responseBody;
	}

	/**
	 * @param responseBody the responseBody to set
	 */
	public void setResponseBody(T responseBody) {
		this.responseBody = responseBody;
	}

	public <E> E getErrorResponse(Class<E> responseType) {
		try {
			return new ObjectMapper().readValue(errorResponse, responseType);
		} catch (StreamReadException e) {
			throw new RestServiceException(e);
		} catch (DatabindException e) {
			throw new RestServiceException(e);
		} catch (IOException e) {
			throw new RestServiceException(e);
		}
	}

	public void setErrorResponse(byte[] errorResponse) {
		this.errorResponse = errorResponse;
	}

	public boolean isSuccess() {
		return httpStatus.is2xxSuccessful();
	}

	public boolean isError() {
		return !isSuccess();
	}

}
