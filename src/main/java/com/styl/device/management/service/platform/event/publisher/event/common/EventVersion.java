/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.platform.event.publisher.event.common;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 *
 */
public enum EventVersion {

	V1("2022-11-29");

	private String version;

	private static Map<String, EventVersion> versions = new HashMap<>();

	static {
		for (EventVersion v : values()) {
			versions.put(v.version, v);
		}
	}

	private EventVersion(String version) {
		this.version = version;
	}

	@JsonValue
	public String toString() {
		return this.version;
	}

	public static EventVersion getLatestVersion() {
		return V1;
	}

	public static EventVersion of(String version) {
		EventVersion ev = versions.get(version);
		if (ev != null) {
			return ev;
		} else {
			return getLatestVersion();
		}
	}

	public static EventVersion get(String version) {
		return versions.get(version);
	}

	public static boolean exist(String version) {
		return versions.get(version) != null ? true : false;
	}

	public static Set<String> getList() {
		return versions.keySet();
	}
}
