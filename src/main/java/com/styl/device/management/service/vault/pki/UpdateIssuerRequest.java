/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.vault.pki;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class UpdateIssuerRequest {

	@JsonProperty("issuer_name")
	private String issuerName;

	@JsonProperty("leaf_not_after_behavior")
	private String leafNotAfterBehavior;

	public UpdateIssuerRequest(String issuerName, String leafNotAfterBehavior) {
		super();
		this.issuerName = issuerName;
		this.leafNotAfterBehavior = leafNotAfterBehavior;
	}

	public String getIssuerName() {
		return issuerName;
	}

	public void setIssuerName(String issuerName) {
		this.issuerName = issuerName;
	}

	public String getLeafNotAfterBehavior() {
		return leafNotAfterBehavior;
	}

	public void setLeafNotAfterBehavior(String leafNotAfterBehavior) {
		this.leafNotAfterBehavior = leafNotAfterBehavior;
	}

}
