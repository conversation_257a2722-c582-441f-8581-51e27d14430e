/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.vault.pki;

/**
 * <AUTHOR>
 *
 */
public class VaultCertificateAuthority implements Comparable<VaultCertificateAuthority> {

	public static final String TYPE_ROOT = "root";

	public static final String TYPE_INTERMEDIATE = "intermediate";

	private String name;

	private String type;

	public VaultCertificateAuthority() {
		super();
	}

	public VaultCertificateAuthority(String name, String type) {
		super();
		this.name = name;
		this.type = type;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Override
	public int compareTo(VaultCertificateAuthority o) {
		if (this.type.equals("root")) {
			return -1;
		}
		return this.name.compareTo(o.name);
	}

}
