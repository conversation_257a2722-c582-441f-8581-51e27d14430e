/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.vault.pki;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class TuneMountRequest {

	@JsonProperty("default_lease_ttl")
	private long defaultLeaseTtl;

	@JsonProperty("max_lease_ttl")
	private long maxLeaseTtl;

	public TuneMountRequest() {
		super();
	}

	public TuneMountRequest(long defaultLeaseTtl, long maxLeaseTtl) {
		super();
		this.defaultLeaseTtl = defaultLeaseTtl;
		this.maxLeaseTtl = maxLeaseTtl;
	}

	public long getDefaultLeaseTtl() {
		return defaultLeaseTtl;
	}

	public void setDefaultLeaseTtl(long defaultLeaseTtl) {
		this.defaultLeaseTtl = defaultLeaseTtl;
	}

	public long getMaxLeaseTtl() {
		return maxLeaseTtl;
	}

	public void setMaxLeaseTtl(long maxLeaseTtl) {
		this.maxLeaseTtl = maxLeaseTtl;
	}

}
