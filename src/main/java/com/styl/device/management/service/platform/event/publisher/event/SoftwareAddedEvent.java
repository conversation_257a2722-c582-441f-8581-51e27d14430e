/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.platform.event.publisher.event;

import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.service.platform.event.publisher.event.common.Event;
import com.styl.device.management.service.platform.event.publisher.event.common.EventType;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwareAddedEventData;

/**
 * <AUTHOR>
 *
 */
public class SoftwareAddedEvent extends Event<SoftwareAddedEventData> {

	public SoftwareAddedEvent(ServicePlatform servicePlatform, SoftwareAddedEventData data) {
		super(EventType.SOFTWARE_ADDED, servicePlatform, data);
	}
}
