/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.platform.event.publisher.event.restservice;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * <AUTHOR>
 *
 */
public class RestRequest {

	private HttpMethod httpMethod;

	private String url;

	private HttpHeaders headers;

	private Map<String, Object> requestParams;

	private Object body;

	/**
	 * @param httpMethod
	 * @param path
	 */
	public RestRequest(String url) {
		super();
		this.url = url;
		this.requestParams = new HashMap<String, Object>();
		this.headers = new HttpHeaders();
	}

	/**
	 * @return the httpMethod
	 */
	public HttpMethod getHttpMethod() {
		return httpMethod;
	}

	/**
	 * @param httpMethod the httpMethod to set
	 */
	public void setHttpMethod(HttpMethod httpMethod) {
		this.httpMethod = httpMethod;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	/**
	 * @return the headers
	 */
	public HttpHeaders getHeaders() {
		return headers;
	}

	/**
	 * @param headers the headers to set
	 */
	public void setHeaders(HttpHeaders headers) {
		this.headers = headers;
	}

	public void addHeader(String name, Object value) {
		if (this.headers == null) {
			this.headers = new HttpHeaders();
		}
		this.headers.add(name, String.valueOf(value));
	}

	/**
	 * @return the requestParams
	 */
	public Map<String, Object> getRequestParams() {
		return requestParams;
	}

	/**
	 * @param requestParams the requestParams to set
	 */
	public void setRequestParams(Map<String, Object> requestParams) {
		this.requestParams = requestParams;
	}

	public void addRequestParam(String name, Object value) {
		if (this.requestParams == null) {
			this.requestParams = new HashMap<String, Object>();
		}
		this.requestParams.put(name, value);
	}

	public Object getBody() {
		return body;
	}

	public void setBody(Object body) {
		this.body = body;
	}

}
