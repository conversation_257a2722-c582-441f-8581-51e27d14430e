/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.vault.pki;

import java.net.URI;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.vault.authentication.TokenAuthentication;
import org.springframework.vault.client.VaultEndpoint;
import org.springframework.vault.support.VaultMount;
import org.springframework.vault.support.VaultResponseSupport;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;

/**
 * <AUTHOR>
 *
 */

@Component
public class VaultPkiService {

	private static final String SECRET_TYPE_PKI = "pki";

	private CustomVaultTemplate vaultTemplate;

	@Value("${com.styl.device.management.vault.endpoint:}")
	private String vaultEndpoint;

	@Value("${com.styl.device.management.vault.token:}")
	private String vaultToken;

	@Value("${com.styl.device.management.vault.pki.ca.prefix:dms_}")
	private String caPrefix;

	@Value("${com.styl.device.management.vault.pki.ca.rootname:root}")
	private String rootCaName;

	@Value("${com.styl.device.management.vault.pki.ca.ttl.default:31556952}")
	private long defaultLeaseTtl;

	@Value("${com.styl.device.management.vault.pki.ca.ttl.max:94670856}")
	private long maxLeaseTtl;

	@PostConstruct
	public void init() {
		if (StringUtils.isNoneBlank(vaultEndpoint, vaultToken)) {
			VaultEndpoint endpoint = VaultEndpoint.from(URI.create(vaultEndpoint));
			TokenAuthentication token = new TokenAuthentication(vaultToken);
			this.vaultTemplate = new CustomVaultTemplate(endpoint, token);
		}
	}

	public Collection<VaultCertificateAuthority> getAuthorities() {
		return getMounts(caPrefix).keySet().stream().map(x -> {
			if (isRootCa(x)) {
				return new VaultCertificateAuthority(x, VaultCertificateAuthority.TYPE_ROOT);
			}
			return new VaultCertificateAuthority(x, VaultCertificateAuthority.TYPE_INTERMEDIATE);
		}).toList();
	}

	public void createRootCertificateAuthority() {
		createCertificateAuthority(getRootCaName());
	}

	public void createIntemediateCertificateAuthority(String name) {
		createCertificateAuthority(caPrefix + name);
	}

	/**
	 * @param issuerName
	 * @param ttl        in second
	 * @return
	 */
	public VaultIssuerResponse addIssuer(String caName, String issuerName, long ttl) {
		if (isRootCa(caName)) {
			return addRootIssuer(caName, issuerName, ttl + "s");
		}

		DefaultIssuerResponse defaultIssuer = getDefaultIssuer(getRootCaName());
		if (StringUtils.isBlank((defaultIssuer.getDefaultIssuer()))) {
			throw new ServiceException(ErrorCode.PKI_ROOT_ISSUER_NOT_FOUND);
		}

		IntermediateCsrResponse addCertResponse = createIntermediateCsr(caName, issuerName, ttl + "s");
		if (addCertResponse == null) {
			throw new ServiceException(ErrorCode.PKI_VAULT_EXCEPTION);
		}

		String csr = String.valueOf(addCertResponse.getCsr());

		SignCertificateRequest signRequest = new SignCertificateRequest(csr, "pem", ttl + "s");

		VaultResponseSupport<VaultIssuerResponse> signResponse = vaultTemplate
				.write(getRootCaName() + "/root/sign-intermediate", signRequest, VaultIssuerResponse.class);
		if (signResponse == null || signResponse.getData() == null) {
			throw new ServiceException(ErrorCode.PKI_VAULT_EXCEPTION);
		}

		VaultIssuerResponse signedCert = signResponse.getData();

		String signedCertificate = signedCert.getCertificate();

		SetSignedRequest setSignedRequest = new SetSignedRequest(signedCertificate);
		VaultResponseSupport<SetSignedResponse> setSignedResponse = vaultTemplate
				.write(caName + "/intermediate/set-signed", setSignedRequest, SetSignedResponse.class);
		if (setSignedResponse == null || setSignedResponse.getData() == null) {
			throw new ServiceException(ErrorCode.PKI_VAULT_EXCEPTION);
		}

		VaultIssuerResponse issuerResponse = getIssuerDetail(caName,
				setSignedResponse.getData().getImportedIssuer().get(0));
		issuerResponse.setSerialNumber(signedCert.getSerialNumber());
		String derCert = convertPemToDerCertificate(signedCertificate);
		String derCaCert = convertPemToDerCertificate(signedCert.getIssuingCaCertificate());
		issuerResponse.setCertificate(derCert);
		issuerResponse.setIssuingCaCertificate(derCaCert);

		UpdateIssuerRequest updateIssuerRequest = new UpdateIssuerRequest(issuerName, "truncate");
		vaultTemplate.write(caName + "/issuer/" + issuerResponse.getIssuerId(), updateIssuerRequest, Void.class);

		return issuerResponse;
	}

	public VaultIssuerResponse getIssuerDetail(String caName, String issuerId) {
		VaultResponseSupport<VaultIssuerResponse> response = vaultTemplate.read(caName + "/issuer/" + issuerId,
				VaultIssuerResponse.class);
		if (response == null) {
			return null;
		}
		return response.getData();
	}

	public void setDefaultIssuer(String caName, String issuerName) {
		Map<String, String> setDefaultIssuerRequest = new HashMap<>();
		setDefaultIssuerRequest.put("default", issuerName);

		vaultTemplate.write(caName + "/config/issuers", setDefaultIssuerRequest);
	}

	public DefaultIssuerResponse getDefaultIssuer(String caName) {
		VaultResponseSupport<DefaultIssuerResponse> response = vaultTemplate.read(caName + "/config/issuers",
				DefaultIssuerResponse.class);
		if (response == null) {
			return null;
		}
		return response.getRequiredData();
	}

	public SignCertificateResponse signCertificate(String commonName, String caName, String csr, long ttl) {
		String path = caName + "/sign/" + getRoleName(caName);
		SignCertificateRequest signRequest = new SignCertificateRequest(commonName, csr, "der", ttl + "s");

		VaultResponseSupport<SignCertificateResponse> response = vaultTemplate.write(path, signRequest,
				SignCertificateResponse.class);
		if (response == null) {
			return null;
		}
		return response.getData();
	}

	public GeneratePrivateResponse generatePrivateKey(String caName) {
		String path = caName + "/intermediate/generate/exported";

		Map<String, Object> requestObject = new HashMap<>();
		requestObject.put("format", "pem");

		VaultResponseSupport<GeneratePrivateResponse> response = vaultTemplate.write(path, requestObject,
				GeneratePrivateResponse.class);
		if (response == null) {
			return null;
		}
		return response.getData();
	}

	public CertificatePemResponse getPemCertificate(String caName, String serialNumber) {
		VaultResponseSupport<CertificatePemResponse> response = vaultTemplate.read(caName + "/cert/" + serialNumber,
				CertificatePemResponse.class);
		if (response == null) {
			return null;
		}
		return response.getRequiredData();
	}

	private VaultIssuerResponse addRootIssuer(String caName, String issuerName, String ttl) {
		String path = caName + "/root/generate/internal";

		AddCACertificate addCaCertRequest = new AddCACertificate(issuerName, issuerName, ttl, "der");

		VaultResponseSupport<VaultIssuerResponse> response = vaultTemplate.write(path, addCaCertRequest,
				VaultIssuerResponse.class);
		if (response == null || response.getData() == null) {
			throw new ServiceException(ErrorCode.PKI_VAULT_EXCEPTION);
		}

		UpdateIssuerRequest updateIssuerRequest = new UpdateIssuerRequest(issuerName, "truncate");

		vaultTemplate.write(caName + "/issuer/" + response.getData().getIssuerId(), updateIssuerRequest, Void.class);
		return response.getData();
	}

	private IntermediateCsrResponse createIntermediateCsr(String caName, String issuerName, String ttl) {
		String path = caName + "/intermediate/generate/internal";

		AddCACertificate addCaCertRequest = new AddCACertificate(issuerName,
				issuerName + "_" + System.currentTimeMillis(), ttl, "pem");

		VaultResponseSupport<IntermediateCsrResponse> response = vaultTemplate.write(path, addCaCertRequest,
				IntermediateCsrResponse.class);
		if (response == null) {
			return null;
		}
		return response.getData();
	}

	private Map<String, VaultMount> getMounts(String prefix) {
		VaultResponseSupport<VaultMountResponse> mounts = vaultTemplate.read("/sys/mounts", VaultMountResponse.class);
		if (mounts == null || mounts.getData() == null) {
			return new HashMap<>();
		}
		return mounts.getData().entrySet().stream()
				.filter(x -> x.getKey().startsWith(prefix) && x.getValue().getType().equalsIgnoreCase(SECRET_TYPE_PKI))
				.collect(Collectors.toMap(x -> x.getKey().replace("/", ""), x -> x.getValue()));
	}

	private void createCertificateAuthority(String caName) {
		if (!getMounts(caPrefix).containsKey(caName)) {
			Map<String, Object> configs = new HashMap<>();
			configs.put("default_lease_ttl", defaultLeaseTtl);
			configs.put("max_lease_ttl", maxLeaseTtl);
			vaultTemplate.opsForSys().mount(caName, VaultMount.builder().type(SECRET_TYPE_PKI).config(configs).build());
		}
		addRoleIfNotExist(caName);
	}

	private void configureCertificateAuthority(String caName, TuneMountRequest request) {
		vaultTemplate.write("/sys/mounts/" + caName + "/tune", request, TuneMountRequest.class);
	}

	private boolean isRootCa(String caName) {
		if (StringUtils.isBlank(caName)) {
			return false;
		}
		return caName.equals(getRootCaName());
	}

	public String getRootCaName() {
		return caPrefix + rootCaName;
	}

	private String getRoleName(String caName) {
		return caName + "_role";
	}

	public RoleResponse getRole(String caName) {
		return addRoleIfNotExist(caName);
	}

	private RoleResponse addRoleIfNotExist(String caName) {
		String roleName = getRoleName(caName);
		RoleResponse role = getRole(caName, roleName);
		if (role != null) {
			return role;
		}
		RoleRequest roleRequest = new RoleRequest();
		roleRequest.setAllowAnyName(true);
		roleRequest.setAllowLocalhost(false);
		roleRequest.setAllowSubdomains(false);
		roleRequest.setMaxTtl(maxLeaseTtl);
		roleRequest.setTtl(defaultLeaseTtl);
		return updateRole(caName, roleRequest);
	}

	public RoleResponse updateRole(String caName, RoleRequest roleRequest) {
		if (!getMounts(caPrefix).containsKey(caName)) {
			throw new ServiceException(ErrorCode.PKI_CA_NOT_FOUND);
		}
		String roleName = getRoleName(caName);
		String path = caName + "/roles/" + roleName;
		vaultTemplate.write(path, roleRequest, RoleResponse.class);
		configureCertificateAuthority(caName, new TuneMountRequest(roleRequest.getTtl(), roleRequest.getMaxTtl()));
		return getRole(caName, roleName);
	}

	private RoleResponse getRole(String caName, String roleName) {
		String path = caName + "/roles/" + roleName;

		VaultResponseSupport<RoleResponse> response = vaultTemplate.read(path, RoleResponse.class);
		if (response == null) {
			return null;
		}
		return response.getData();
	}

	public String convertPemToDerCertificate(String pem) {
		return pem.replaceAll("-----.+?-----|\\n", "");
	}

	public RevokeCertResponse revokeCertificate(RevokeRequest revokeRequest, String caName) {
		VaultResponseSupport<RevokeCertResponse> response = vaultTemplate.write(caName + "/revoke", revokeRequest,
				RevokeCertResponse.class);
		if (response == null) {
			throw new ServiceException(ErrorCode.PKI_REVOKED_CERT_FAIL);
		}
		return response.getData();
	}

	public RevokeIssuerResponse revokeIssuer(String caName, String issuerId) {
		VaultResponseSupport<RevokeIssuerResponse> response = vaultTemplate
				.write(caName + "/issuer/" + issuerId + "/revoke", null, RevokeIssuerResponse.class);
		if (response == null) {
			throw new ServiceException(ErrorCode.PKI_REVOKED_ISSUER_FAIL);
		}
		return response.getData();
	}
}
