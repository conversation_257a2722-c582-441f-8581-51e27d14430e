/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.platform.event.publisher.event.data;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import jakarta.validation.constraints.NotNull;

import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.service.platform.event.publisher.event.common.EventVersion;

/**
 * <AUTHOR>
 *
 */
public class DeviceAssignedEventData implements IEventData {

	@NotNull
	private String deviceUid;

	@NotNull
	private String model;

	@NotNull
	private String hardwareId;

	private String simId;

	private String imei;

	@NotNull
	private Long registrationTime;

	public DeviceAssignedEventData() {
		super();
	}

	public DeviceAssignedEventData(DeviceAssignedEventData data) {
		this.deviceUid = data.getDeviceUid();
		this.model = data.getModel();
		this.hardwareId = data.getHardwareId();
		this.simId = data.getSimId();
		this.imei = data.getImei();
		this.registrationTime = data.getRegistrationTime();
	}

	public DeviceAssignedEventData(Device device) {
		super();
		this.deviceUid = device.getId();
		this.model = device.getModel().getModel();
		this.hardwareId = device.getHardwareId();
		this.simId = device.getSimId();
		this.imei = device.getImei();
		this.registrationTime = device.getFirstRegistrationTime();
	}

	public String getDeviceUid() {
		return deviceUid;
	}

	public void setDeviceUid(String deviceUid) {
		this.deviceUid = deviceUid;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getHardwareId() {
		return hardwareId;
	}

	public void setHardwareId(String hardwareId) {
		this.hardwareId = hardwareId;
	}

	public String getSimId() {
		return simId;
	}

	public void setSimId(String simId) {
		this.simId = simId;
	}

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	public Long getRegistrationTime() {
		return registrationTime;
	}

	public void setRegistrationTime(Long registrationTime) {
		this.registrationTime = registrationTime;
	}

	public static Set<String> getEventProperties(EventVersion eventVersion) {
		switch (eventVersion) {
		case V1:
		default:
			return new HashSet<String>(
					Arrays.asList("deviceUid", "model", "hardwareId", "simId", "imei", "registrationTime"));
		}
	}

	@Override
	public String toString() {
		return "DeviceAssignedEventData [deviceUid=" + deviceUid + ", model=" + model + ", hardwareId=" + hardwareId
				+ ", simId=" + simId + ", imei=" + imei + ", registrationTime=" + registrationTime + "]";
	}

}
