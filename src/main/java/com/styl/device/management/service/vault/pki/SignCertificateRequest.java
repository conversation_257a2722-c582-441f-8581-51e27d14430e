/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.vault.pki;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class SignCertificateRequest {

	@JsonProperty("common_name")
	private String commonName;

	@JsonProperty("csr")
	private String csr;

	@JsonProperty("format")
	private String format;

	@JsonProperty("ttl")
	private String ttl;

	/**
	 * @param csr
	 * @param format
	 * @param ttl
	 */
	public SignCertificateRequest(String csr, String format, String ttl) {
		super();
		this.csr = csr;
		this.format = format;
		this.ttl = ttl;
	}

	public SignCertificateRequest(String commonName, String csr, String format, String ttl) {
		super();
		this.commonName = commonName;
		this.csr = csr;
		this.format = format;
		this.ttl = ttl;
	}

	public String getCommonName() {
		return commonName;
	}

	public void setCommonName(String commonName) {
		this.commonName = commonName;
	}

	/**
	 * @return the csr
	 */
	public String getCsr() {
		return csr;
	}

	/**
	 * @param csr the csr to set
	 */
	public void setCsr(String csr) {
		this.csr = csr;
	}

	/**
	 * @return the format
	 */
	public String getFormat() {
		return format;
	}

	/**
	 * @param format the format to set
	 */
	public void setFormat(String format) {
		this.format = format;
	}

	/**
	 * @return the ttl
	 */
	public String getTtl() {
		return ttl;
	}

	/**
	 * @param ttl the ttl to set
	 */
	public void setTtl(String ttl) {
		this.ttl = ttl;
	}

}
