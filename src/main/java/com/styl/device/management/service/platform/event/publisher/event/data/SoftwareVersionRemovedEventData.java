/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.service.platform.event.publisher.event.data;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import jakarta.validation.constraints.NotNull;

import com.styl.device.management.service.platform.event.publisher.event.common.EventVersion;

/**
 * <AUTHOR> Yee
 *
 */
public class SoftwareVersionRemovedEventData implements IEventData {

	@NotNull
	private Long softwareVersionId;

	public SoftwareVersionRemovedEventData() {

	}

	public SoftwareVersionRemovedEventData(Long softwareVersionId) {
		this.softwareVersionId = softwareVersionId;
	}

	public Long getSoftwareVersionId() {
		return softwareVersionId;
	}

	public void setSoftwareVersionId(Long softwareVersionId) {
		this.softwareVersionId = softwareVersionId;
	}

	public static Set<String> getEventProperties(EventVersion eventVersion) {
		switch (eventVersion) {
		case V1:
		default:
			return new HashSet<String>(Arrays.asList("softwareVersionId"));
		}
	}

}
