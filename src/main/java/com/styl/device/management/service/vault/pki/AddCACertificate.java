/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.vault.pki;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
public class AddCACertificate {

	@JsonProperty("common_name")
	private String commonName;

	@JsonProperty("issuer_name")
	private String issuerName;

	@JsonProperty("ttl")
	private String ttl;

	@JsonProperty("format")
	private String format;

	/**
	 * @param commonName
	 * @param issuerName
	 * @param ttl
	 * @param format
	 */
	public AddCACertificate(String commonName, String issuerName, String ttl, String format) {
		super();
		this.commonName = commonName;
		this.issuerName = issuerName;
		this.ttl = ttl;
		this.format = format;
	}

	/**
	 * @param commonName
	 * @param issuerName
	 * @param ttl
	 * @param format
	 */
	public AddCACertificate(String commonName, String issuerName, String ttl) {
		super();
		this.commonName = commonName;
		this.issuerName = issuerName;
		this.ttl = ttl;
	}

	/**
	 * @return the commonName
	 */
	public String getCommonName() {
		return commonName;
	}

	/**
	 * @param commonName the commonName to set
	 */
	public void setCommonName(String commonName) {
		this.commonName = commonName;
	}

	/**
	 * @return the issuerName
	 */
	public String getIssuerName() {
		return issuerName;
	}

	/**
	 * @param issuerName the issuerName to set
	 */
	public void setIssuerName(String issuerName) {
		this.issuerName = issuerName;
	}

	/**
	 * @return the ttl
	 */
	public String getTtl() {
		return ttl;
	}

	/**
	 * @param ttl the ttl to set
	 */
	public void setTtl(String ttl) {
		this.ttl = ttl;
	}

	public String getFormat() {
		return format;
	}

	public void setFormat(String format) {
		this.format = format;
	}

}
