/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.service.platform.event.publisher.event.common;

import java.util.HashSet;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.FilterProvider;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.styl.device.management.service.platform.event.publisher.event.data.DeviceAssignedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.DeviceUnassignedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.IEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwareAddedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwarePackageUpdatedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwareRemovedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwareUpdatedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwareVersionAddedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwareVersionRemovedEventData;

/**
 * <AUTHOR> Yee
 *
 */
@Component
public class EventHandler {

	public Event<IEventData> resolve(Event<IEventData> event) {
		switch (event.getType()) {
		case DEVICE_ASSIGNED:
			return resolveDeviceAssignEventData(event);
		case DEVICE_UNASSIGNED:
			return resolveDeviceUnassignedEventData(event);
		case SOFTWARE_ADDED:
			return resolveSoftwareAddedEventData(event);
		case SOFTWARE_REMOVED:
			return resolveSoftwareRemovedEventData(event);
		case SOFTWARE_UPDATED:
			return resolveSoftwareUpdatedEventData(event);
		case SOFTWARE_PACKAGE_UPDATED:
			return resolveSoftwarePackageUpdatedEventData(event);
		case SOFTWARE_VERSION_ADDED:
			return resolveSoftwareVersionAddedEventData(event);
		case SOFTWARE_VERSION_REMOVED:
			return resolveSoftwareVersionRemovedEventData(event);
		default:
			return event;
		}
	}

	public static <T> T parse(Object data, Class<T> elementClass) {
		ObjectMapper objectMapper = new ObjectMapper();
		return objectMapper.convertValue(data, elementClass);
	}

	public Event<IEventData> resolveDeviceAssignEventData(Event<IEventData> event) {
		switch (event.getVersion()) {
		case V1:
			event.setData(new DeviceAssignedEventData(parse(event.getData(), DeviceAssignedEventData.class)));
			break;
		default:
			break;
		}
		return event;
	}

	public Event<IEventData> resolveDeviceUnassignedEventData(Event<IEventData> event) {
		return event;

	}

	public Event<IEventData> resolveSoftwareAddedEventData(Event<IEventData> event) {
		return event;

	}

	public Event<IEventData> resolveSoftwareRemovedEventData(Event<IEventData> event) {
		return event;
	}

	public Event<IEventData> resolveSoftwareUpdatedEventData(Event<IEventData> event) {
		return event;

	}

	public Event<IEventData> resolveSoftwarePackageUpdatedEventData(Event<IEventData> event) {
		return event;

	}

	public Event<IEventData> resolveSoftwareVersionAddedEventData(Event<IEventData> event) {
		return event;

	}

	public Event<IEventData> resolveSoftwareVersionRemovedEventData(Event<IEventData> event) {
		return event;

	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Event resolveA(Event event) throws JsonProcessingException {
		SimpleBeanPropertyFilter simpleBeanPropertyFilter = SimpleBeanPropertyFilter
				.filterOutAllExcept(getEventProperties(event));

		FilterProvider filterProvider = new SimpleFilterProvider().addFilter("dynamicFilter", simpleBeanPropertyFilter);

		ObjectMapper mapper = new ObjectMapper();
		mapper.addMixIn(Object.class, DynamicMixIn.class);
		JsonNode data = mapper.setFilterProvider(filterProvider).valueToTree(event.getData());
		event.setData(data);
		return event;
	}

	@SuppressWarnings("rawtypes")
	public Set<String> getEventProperties(Event event) {
		switch (event.getType()) {
		case DEVICE_ASSIGNED:
			return DeviceAssignedEventData.getEventProperties(event.getVersion());
		case DEVICE_UNASSIGNED:
			return DeviceUnassignedEventData.getEventProperties(event.getVersion());
		case SOFTWARE_ADDED:
			return SoftwareAddedEventData.getEventProperties(event.getVersion());
		case SOFTWARE_REMOVED:
			return SoftwareRemovedEventData.getEventProperties(event.getVersion());
		case SOFTWARE_UPDATED:
			return SoftwareUpdatedEventData.getEventProperties(event.getVersion());
		case SOFTWARE_PACKAGE_UPDATED:
			return SoftwarePackageUpdatedEventData.getEventProperties(event.getVersion());
		case SOFTWARE_VERSION_ADDED:
			return SoftwareVersionAddedEventData.getEventProperties(event.getVersion());
		case SOFTWARE_VERSION_REMOVED:
			return SoftwareVersionRemovedEventData.getEventProperties(event.getVersion());
		default:
			return new HashSet<String>();
		}
	}
}
