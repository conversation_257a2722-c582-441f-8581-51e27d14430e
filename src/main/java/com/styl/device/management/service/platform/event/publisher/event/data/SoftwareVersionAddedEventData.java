/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.service.platform.event.publisher.event.data;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import jakarta.validation.constraints.NotNull;

import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.service.platform.event.publisher.event.common.EventVersion;

/**
 * <AUTHOR> Yee
 *
 */
public class SoftwareVersionAddedEventData implements IEventData {

	@NotNull
	private Long softwareVersionId;

	@NotNull
	private Long softwareId;

	@NotNull
	private String softwarePackageName;

	@NotNull
	private Set<String> softwareDeviceModel;

	@NotNull
	private String version;

	@NotNull
	private String updateMode;

	@NotNull
	private Long uploadedTime;

	private String releaseNote;

	public SoftwareVersionAddedEventData() {

	}

	public SoftwareVersionAddedEventData(SoftwareVersion sv) {
		this.softwareVersionId = sv.getId();
		this.softwareId = sv.getSoftware() != null ? sv.getSoftware().getId() : null;
		this.softwarePackageName = sv.getSoftware() != null ? sv.getSoftware().getPackageName() : null;
		this.softwareDeviceModel = sv.getSoftware() != null ? sv.getSoftware().getListNameDeviceModels() : null;
		this.version = sv.getVersion();
		this.updateMode = sv.getUpdateMode() != null ? sv.getUpdateMode().getMode() : null;
		this.uploadedTime = sv.getCreatedTime();
		this.releaseNote = sv.getReleaseNote();
	}

	public Long getSoftwareVersionId() {
		return softwareVersionId;
	}

	public void setSoftwareVersionId(Long softwareVersionId) {
		this.softwareVersionId = softwareVersionId;
	}

	public Long getSoftwareId() {
		return softwareId;
	}

	public void setSoftwareId(Long softwareId) {
		this.softwareId = softwareId;
	}

	public String getSoftwarePackageName() {
		return softwarePackageName;
	}

	public void setSoftwarePackageName(String softwarePackageName) {
		this.softwarePackageName = softwarePackageName;
	}

	public Set<String> getSoftwareDeviceModel() {
		return softwareDeviceModel;
	}

	public void setSoftwareDeviceModel(Set<String> softwareDeviceModel) {
		this.softwareDeviceModel = softwareDeviceModel;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getUpdateMode() {
		return updateMode;
	}

	public void setUpdateMode(String updateMode) {
		this.updateMode = updateMode;
	}

	public Long getUploadedTime() {
		return uploadedTime;
	}

	public void setUploadedTime(Long uploadedTime) {
		this.uploadedTime = uploadedTime;
	}

	public String getReleaseNote() {
		return releaseNote;
	}

	public void setReleaseNote(String releaseNote) {
		this.releaseNote = releaseNote;
	}

	public static Set<String> getEventProperties(EventVersion eventVersion) {
		switch (eventVersion) {
		case V1:
		default:
			return new HashSet<String>(Arrays.asList("softwareVersionId", "softwareId", "softwarePackageName",
					"softwareDeviceModel", "version", "updateMode", "uploadedTime", "releaseNote"));
		}
	}

}
