/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.service.platform.event.publisher;

import jakarta.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.kafka.KafkaTopicService;
import com.styl.device.management.persistence.service.platform.notification.setting.ServicePlatformNotificationSetting;
import com.styl.device.management.persistence.service.platform.notification.setting.ServicePlatformNotificationSettingService;
import com.styl.device.management.service.platform.event.publisher.event.common.Event;
import com.styl.device.management.service.platform.event.publisher.event.common.EventHandler;
import com.styl.device.management.service.platform.event.publisher.event.common.EventVersion;
import com.styl.device.management.service.platform.event.publisher.event.common.EventWebhookHandler;
import com.styl.device.management.service.platform.event.publisher.event.data.IEventData;

/**
 * <AUTHOR> Yee
 *
 */
@Component
public class EventQueueHandler {

	private static final Logger logger = LoggerFactory.getLogger(EventQueueHandler.class);

	public static final String PROCESS_LISTENER_ID = "event_processing_listener_id";
	public static final String WEBHOOKS_LISTENER_ID = "event_webhooks_listener_id";

	@Value("${com.styl.device.management.kafka.serviceplatform.event.topic}")
	private String topicName;

	@Value("${com.styl.device.management.kafka.serviceplatform.event.failed.topic}")
	private String failedTopicName;

	@Value("${com.styl.device.management.kafka.serviceplatform.webhooks.topic}")
	private String webhooksTopicName;

	@Value("${com.styl.device.management.kafka.serviceplatform.webhooks.failed.topic}")
	private String webhooksFailedTopicName;

	@Value("${com.styl.device.management.kafka.listener.enabled}")
	private boolean enabledListener;

	@Autowired
	private KafkaTemplate<String, Event<?>> kafkaTemplate;

	@Autowired
	private KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry;

	@Autowired
	private EventHandler eventHandler;

	@Autowired
	private EventWebhookHandler eventWebhookHandler;

	@Autowired
	private ServicePlatformNotificationSettingService spNotificationSettingService;

	@Autowired
	private KafkaTopicService topicService;

	@Value(value = "${com.styl.device.management.kafka.acl.enabled:true}")
	private boolean aclEnabled;

	@PostConstruct
	public void init() {
		if (enabledListener) {
			new Thread(new KafkaListenerStarter(kafkaListenerEndpointRegistry, PROCESS_LISTENER_ID)).start();
			new Thread(new KafkaListenerStarter(kafkaListenerEndpointRegistry, WEBHOOKS_LISTENER_ID)).start();
			if (aclEnabled) {
				topicService.authorizedForAdminTopicCreation();
				topicService.authorizedForAdminOnGroup(PROCESS_LISTENER_ID);
				topicService.authorizedForAdminOnGroup(WEBHOOKS_LISTENER_ID);
				topicService.authorizedForTopic(topicName);
				topicService.authorizedForTopic(failedTopicName);
				topicService.authorizedForTopic(webhooksTopicName);
				topicService.authorizedForTopic(webhooksFailedTopicName);
			}
		}
	}

	@Async
	public void queue(Event<?> event) {
		logger.info("Event: {} - {}", event.getType(), event.getId());
		kafkaTemplate.send(topicName, event);
	}

	@KafkaListener(id = PROCESS_LISTENER_ID, topics = "${com.styl.device.management.kafka.serviceplatform.event.topic}", containerFactory = "kafkaListenerContainerFactory", autoStartup = "false")
	public void process(Event<IEventData> event) throws JsonProcessingException {
		ServicePlatformNotificationSetting spNotificationSetting = spNotificationSettingService
				.findByServicePlatformShortName(event.getServicePlatform());
		logger.info("Process kafka to service platform: " + event.getServicePlatform());
		if (spNotificationSetting != null && StringUtils.isNotBlank(spNotificationSetting.getEventTopic())
				&& EventVersion.exist(spNotificationSetting.getEventVersion())) {
			event.setVersion(EventVersion.of(spNotificationSetting.getEventVersion()));
			event = eventHandler.resolve(event);
			logger.info("Process kafka to service platform: " + spNotificationSetting.getEventTopic());
			kafkaTemplate.send(spNotificationSetting.getEventTopic(), event);
			boolean enabledWebhooks = spNotificationSetting.isEnabledWebhook();
			if (enabledWebhooks) {
				kafkaTemplate.send(webhooksTopicName, event);
			}
		} else {
			kafkaTemplate.send(failedTopicName, event);
		}
	}

	@KafkaListener(id = WEBHOOKS_LISTENER_ID, topics = "${com.styl.device.management.kafka.serviceplatform.webhooks.topic}", containerFactory = "kafkaListenerContainerFactory", autoStartup = "false")
	public void processWebhooks(Event<IEventData> event) {
		logger.info("Processing Webhooks Event: {}", event.getId());
		try {
			eventWebhookHandler.processEvent(event);
		} catch (ServiceException ex) {
			kafkaTemplate.send(webhooksFailedTopicName, event);
		}
	}

}
