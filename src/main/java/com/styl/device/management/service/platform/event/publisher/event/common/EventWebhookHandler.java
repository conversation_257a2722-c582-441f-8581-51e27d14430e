/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.service.platform.event.publisher.event.common;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.api.key.ApiKey;
import com.styl.device.management.persistence.api.key.ApiKeyService;
import com.styl.device.management.persistence.service.platform.notification.setting.ServicePlatformNotificationSetting;
import com.styl.device.management.persistence.service.platform.notification.setting.ServicePlatformNotificationSettingService;
import com.styl.device.management.service.platform.event.publisher.event.data.IEventData;
import com.styl.device.management.service.platform.event.publisher.event.restservice.RestRequest;
import com.styl.device.management.service.platform.event.publisher.event.restservice.RestService;
import com.styl.device.management.utils.CryptoUtils;
import com.styl.device.management.utils.Utils;

/**
 * <AUTHOR> Yee
 *
 */
@Component
public class EventWebhookHandler {

	private static final Logger logger = LoggerFactory.getLogger(EventWebhookHandler.class);

	private static final String HEADER_API_KEY = "DMS-ApiKey";
	private static final String HEADER_NONCE = "DMS-Nonce";
	private static final String HEADER_SIGNATURE = "DMS-Signature";

	@Autowired
	private RestService restService;

	@Autowired
	private ApiKeyService apiKeyService;

	@Autowired
	private ServicePlatformNotificationSettingService spNotificationSettingService;

	public boolean processEvent(Event<IEventData> event) {
		try {
			ServicePlatformNotificationSetting spNotificationSetting = spNotificationSettingService
					.findByServicePlatformShortName(event.getServicePlatform());
			ApiKey apiKey = apiKeyService.getLatestApiKeyByServicePlatformShortName(event.getServicePlatform());

			String nonce = String.format("%s%s%s", System.currentTimeMillis(), "#", UUID.randomUUID());
			StringBuilder signatureData = new StringBuilder();
			if (event.getData() != null) {
				signatureData.append(Utils.convertObjectToJson(event.getData()));
			}
			signatureData.append(nonce);

			HttpHeaders headers = new HttpHeaders();
			headers.add(HEADER_API_KEY, apiKey.getApiKey());
			headers.add(HEADER_NONCE, nonce);
			headers.add(HEADER_SIGNATURE, CryptoUtils.hmacSHA256(apiKey.getSecretKey(), signatureData.toString()));
			return pushEvent(headers, spNotificationSetting.getWebhookUrl(), event.getData());
		} catch (JsonProcessingException jpe) {
			logger.warn("Failed to get event body", jpe);
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_WEBHOOK_CALL_FAILED);
		} catch (NoSuchAlgorithmException | InvalidKeyException ex) {
			logger.warn("Failed to generate signature", ex);
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_WEBHOOK_CALL_FAILED);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			logger.warn("Unknown exception happened", e);
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_WEBHOOK_CALL_FAILED);
		}
	}

	public boolean pushEvent(HttpHeaders headers, String webhookUrl, Object requestBody) {
		try {
			RestRequest request = new RestRequest(webhookUrl);
			request.setHeaders(headers);
			request.setBody(requestBody);

			restService.post(request, null);
			return true;
		} catch (Exception e) {
			logger.warn("Failed to push event to webhookUrl", e);
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_WEBHOOK_CALL_FAILED);
		}
	}

}
