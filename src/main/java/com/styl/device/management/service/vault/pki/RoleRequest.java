/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.vault.pki;

import java.util.List;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
@JsonInclude(value = Include.NON_NULL)
public class RoleRequest {

	@NotNull
	@JsonProperty("allow_any_name")
	private Boolean allowAnyName;

	@NotNull
	@JsonProperty("allow_subdomains")
	private Boolean allowSubdomains;

	@NotNull
	@JsonProperty("allow_localhost")
	private Boolean allowLocalhost;

	@NotNull
	@JsonProperty("use_csr_common_name")
	private Boolean useCsrCommonName;

	@JsonProperty("allowed_domains")
	private List<String> allowDomains;

	@NotNull
	@JsonProperty("max_ttl")
	private Long maxTtl;

	@NotNull
	private Long ttl;

	public RoleRequest() {
		super();
	}

	/**
	 * @param role
	 */
	public RoleRequest(RoleResponse role) {
		this.allowAnyName = role.getAllowAnyName();
		this.allowSubdomains = role.getAllowSubdomains();
		this.allowLocalhost = role.getAllowLocalhost();
		this.useCsrCommonName = role.getUseCsrCommonName();
		this.allowDomains = role.getAllowDomains();
		this.maxTtl = role.getMaxTtl();
		this.ttl = role.getTtl();
	}

	public Boolean getAllowAnyName() {
		return allowAnyName;
	}

	public void setAllowAnyName(Boolean allowAnyName) {
		this.allowAnyName = allowAnyName;
	}

	public Boolean getAllowSubdomains() {
		return allowSubdomains;
	}

	public void setAllowSubdomains(Boolean allowSubdomains) {
		this.allowSubdomains = allowSubdomains;
	}

	public Boolean getAllowLocalhost() {
		return allowLocalhost;
	}

	public void setAllowLocalhost(Boolean allowLocalhost) {
		this.allowLocalhost = allowLocalhost;
	}

	public List<String> getAllowDomains() {
		return allowDomains;
	}

	public void setAllowDomains(List<String> allowDomains) {
		this.allowDomains = allowDomains;
	}

	public Boolean getUseCsrCommonName() {
		return useCsrCommonName;
	}

	public void setUseCsrCommonName(Boolean useCsrCommonName) {
		this.useCsrCommonName = useCsrCommonName;
	}

	public Long getMaxTtl() {
		return maxTtl;
	}

	public void setMaxTtl(Long maxTtl) {
		this.maxTtl = maxTtl;
	}

	public Long getTtl() {
		return ttl;
	}

	public void setTtl(Long ttl) {
		this.ttl = ttl;
	}

}
