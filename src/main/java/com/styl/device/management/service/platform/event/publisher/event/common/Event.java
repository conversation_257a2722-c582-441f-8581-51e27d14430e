/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.service.platform.event.publisher.event.common;

import java.util.UUID;

import com.styl.device.management.persistence.service.platform.ServicePlatform;

/**
 * <AUTHOR>
 *
 */

public class Event<T> {

	private String id;

	private EventVersion version;

	private Long createdAt;

	private EventType type;

	private String servicePlatform;

	private T data;

	public Event() {
		this.id = UUID.randomUUID().toString();
		this.createdAt = System.currentTimeMillis();
	}

	protected Event(EventType type, ServicePlatform servicePlatform, T data) {
		this();
		this.type = type;
		this.servicePlatform = servicePlatform != null ? servicePlatform.getShortName() : null;
		this.data = data;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public EventVersion getVersion() {
		return version;
	}

	public void setVersion(EventVersion version) {
		this.version = version;
	}

	public Long getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Long createdAt) {
		this.createdAt = createdAt;
	}

	public EventType getType() {
		return type;
	}

	public void setType(EventType type) {
		this.type = type;
	}

	public String getServicePlatform() {
		return servicePlatform;
	}

	public void setServicePlatform(String servicePlatform) {
		this.servicePlatform = servicePlatform;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

}
