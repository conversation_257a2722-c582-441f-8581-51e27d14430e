/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.service.platform.event.publisher.event.data;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import jakarta.validation.constraints.NotNull;

import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.service.platform.event.publisher.event.common.EventVersion;

/**
 * <AUTHOR> Yee
 *
 */
public class SoftwarePackageUpdatedEventData implements IEventData {

	@NotNull
	private Long softwarePackageId;

	@NotNull
	private String deviceUid;

	@NotNull
	private Long softwareId;

	@NotNull
	private String softwareName;

	@NotNull
	private Long softwareVersionId;

	@NotNull
	private String softwareVersion;

	@NotNull
	private Integer state;

	private String assignedBy;

	private Long assignedTime;

	private Integer updateMode;

	public SoftwarePackageUpdatedEventData() {

	}

	public SoftwarePackageUpdatedEventData(SoftwarePackages sp) {
		this.softwarePackageId = sp.getId();
		this.deviceUid = sp.getDevice() != null ? sp.getDevice().getId() : null;
		this.softwareId = sp.getSoftwareId();
		this.softwareName = sp.getSoftware() != null ? sp.getSoftware().getName() : null;
		this.softwareVersionId = sp.getSoftwareVersionId();
		this.softwareVersion = sp.getVersion();
		this.state = sp.getState();
		this.assignedBy = sp.getAssignedBy();
		this.assignedTime = sp.getAssignedTime();
		this.updateMode = sp.getUpdateMode();
	}

	public Long getSoftwarePackageId() {
		return softwarePackageId;
	}

	public void setSoftwarePackageId(Long softwarePackageId) {
		this.softwarePackageId = softwarePackageId;
	}

	public String getDeviceUid() {
		return deviceUid;
	}

	public void setDeviceUid(String deviceUid) {
		this.deviceUid = deviceUid;
	}

	public Long getSoftwareId() {
		return softwareId;
	}

	public void setSoftwareId(Long softwareId) {
		this.softwareId = softwareId;
	}

	public String getSoftwareName() {
		return softwareName;
	}

	public void setSoftwareName(String softwareName) {
		this.softwareName = softwareName;
	}

	public Long getSoftwareVersionId() {
		return softwareVersionId;
	}

	public void setSoftwareVersionId(Long softwareVersionId) {
		this.softwareVersionId = softwareVersionId;
	}

	public String getSoftwareVersion() {
		return softwareVersion;
	}

	public void setSoftwareVersion(String softwareVersion) {
		this.softwareVersion = softwareVersion;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getAssignedBy() {
		return assignedBy;
	}

	public void setAssignedBy(String assignedBy) {
		this.assignedBy = assignedBy;
	}

	public Long getAssignedTime() {
		return assignedTime;
	}

	public void setAssignedTime(Long assignedTime) {
		this.assignedTime = assignedTime;
	}

	public Integer getUpdateMode() {
		return updateMode;
	}

	public void setUpdateMode(Integer updateMode) {
		this.updateMode = updateMode;
	}

	public static Set<String> getEventProperties(EventVersion eventVersion) {
		switch (eventVersion) {
		case V1:
		default:
			return new HashSet<String>(Arrays.asList("softwarePackageId", "deviceUid", "softwareId", "softwareName",
					"softwareVersionId", "softwareVersion", "state", "assignedBy", "assignedTime", "updateMode"));
		}
	}

}
