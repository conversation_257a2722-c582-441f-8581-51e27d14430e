/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.service.platform.event.publisher;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 */
@Component
public class EventPublisherHandler {

	public static final String EVENT_DEVICE_ASSIGNED = "DEVICE_ASSIGNED";
	public static final String EVENT_DEVICE_UNASSIGNED = "DEVICE_UNASSIGNED";
	public static final String EVENT_SOFTWARE_ADDED = "SOFTWARE_ADDED";
	public static final String EVENT_SOFTWARE_REMOVED = "SOFTWARE_REMOVED";
	public static final String EVENT_SOFTWARE_UPDATED = "SOFTWARE_UPDATED";
	public static final String EVENT_SOFTWARE_PACKAGE_UPDATED = "SOFTWARE_PACKAGE_UPDATED";
	public static final String EVENT_SOFTWARE_VERSION_ADDED = "SOFTWARE_VERSION_ADDED";
	public static final String EVENT_SOFTWARE_VERSION_REMOVED = "SOFTWARE_VERSION_REMOVED";

	public static final String VERSION_DATE_TIME_FORMAT = "yyyy-MM-dd";

//	@Autowired
//	private ServicePlatformService servicePlatformService;

	// TODO Update publish event handler
	public boolean publishEvent(Integer servicePlatformId, String event, Object requestBody) {
//		try {
//			String version = Utils.getDateTimeString(System.currentTimeMillis(), VERSION_DATE_TIME_FORMAT);
//			EventObject eventObject = new EventObject(UUID.randomUUID().toString(), version, event, requestBody);
//			
//			//TODO check if require to publish event- check if service platform need this particular event
//			String webhookUrl = servicePlatformService.getWebhookUrlByServicePlatformId(servicePlatformId);
//			if (webhookUrl == null) {
//				throw new ServiceException(ErrorCode.SERVICE_PLATFORM_WEBHOOK_URL_NOT_FOUND);
//			}
//			return publishEvent(new PublishedEventObject(servicePlatformId, webhookUrl, eventObject));
//		} catch (Exception ex) {
//
//		}
		return true;
	}

	// TODO push event to SNS
//	private boolean publishEvent(PublishedEventObject object) {
//
//		return true;
//	}

}
