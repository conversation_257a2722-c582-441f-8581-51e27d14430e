/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.service.platform.event.publisher.event.common;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 *
 */
public enum EventType {

	DEVICE_ASSIGNED("DEVICE_ASSIGNED"),

	DEVICE_UNASSIGNED("DEVICE_UNASSIGNED"),

	SOFTWARE_ADDED("SOFTWARE_ADDED"),

	SOFTWARE_REMOVED("SOFTWARE_REMOVED"),

	SOFTWARE_UPDATED("SOFTWARE_UPDATED"),

	SOFTWARE_PACKAGE_UPDATED("SOFTWARE_PACKAGE_UPDATED"),

	SOFTWARE_VERSION_ADDED("SOFTWARE_VERSION_ADDED"),

	SOFTWARE_VERSION_REMOVED("SOFTWARE_VERSION_REMOVED");

	private String name;

	private EventType(String name) {
		this.name = name;
	}

	@JsonValue
	public String toString() {
		return this.name;
	}

	public EventType of(String name) {
		for (EventType eventVersion : EventType.values()) {
			if (eventVersion.name.equals(name)) {
				return eventVersion;
			}
		}
		throw new IllegalArgumentException("Invalid event type");
	}

}
