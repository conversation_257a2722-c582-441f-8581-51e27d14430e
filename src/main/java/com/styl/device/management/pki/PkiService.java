/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.pki;

import java.util.List;

import com.styl.device.management.persistence.pki.certificate.Certificate;
import com.styl.device.management.persistence.pki.certificate.issuer.CertificateIssuer;
import com.styl.device.management.rest.portal.admin.pki.ca.TruststoreConfiguration;
import com.styl.device.management.rest.portal.admin.pki.certificate.CertificateRequest;
import com.styl.device.management.rest.portal.admin.pki.issuer.UpdateIssuerRequest;
import com.styl.device.management.service.vault.pki.RevokeCertResponse;
import com.styl.device.management.service.vault.pki.RevokeIssuerResponse;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR>
 *
 */
public interface PkiService {

	/**
	 * @param caName
	 * @param issuerName
	 * @param ttl
	 * @return
	 */
	CertificateIssuer addIssuer(String caName, String issuerName, long ttl);

	/**
	 * @return
	 */
	List<CertificateIssuer> list();

	/**
	 * @param page
	 * @param pageSize
	 * @param caName
	 * @return
	 */
	Pagination<CertificateIssuer> listIssuers(int page, int pageSize, String caName);

	/**
	 * @param caName
	 * @param issuerId
	 * @param updateIssuerRequest
	 * @return
	 */
	CertificateIssuer updateIssuer(String caName, String issuerId, UpdateIssuerRequest updateIssuerRequest);

	/**
	 * @param caName
	 * @param request
	 */
	Certificate signCertificate(String caName, CertificateRequest request);

	/**
	 * @param page
	 * @param pageSize
	 * @param caName
	 * @param commonName
	 * @param serialNumber
	 * @param issuerName
	 * @return
	 */
	Pagination<Certificate> listCertificates(int page, int pageSize, String caName, String commonName,
			String serialNumber, String issuerName);

	/**
	 * @param caName
	 * @param issuerId
	 * @return
	 */
	CertificateIssuer findIssuer(String caName, String issuerId);

	/**
	 * @param caName
	 * @param serialNumber
	 * @return
	 */
	Certificate findCertificate(String caName, String serialNumber);

	RevokeCertResponse revokeCertificate(String caName, String serialNumber);

	RevokeIssuerResponse revokeIssuer(String caName, String issuerId);

	Certificate signDeviceCertificate(String caName, DeviceCertificateRequest request);
	
	void uploadIssuer(String caName, String issuerId);
	
	void removeIssuer(String caName, String issuerId);

	TruststoreConfiguration saveTruststoreConfiguration(String caName, TruststoreConfiguration config);

	TruststoreConfiguration findTruststoreConfigurationByCaName(String caName);

}
