/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.pki;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.pki.certificate.Certificate;
import com.styl.device.management.persistence.pki.certificate.CertificateRepository;
import com.styl.device.management.persistence.pki.certificate.CertificateSpecification;
import com.styl.device.management.persistence.pki.certificate.authority.CertificateAuthority;
import com.styl.device.management.persistence.pki.certificate.authority.CertificateAuthorityRepository;
import com.styl.device.management.persistence.pki.certificate.issuer.CertificateIssuer;
import com.styl.device.management.persistence.pki.certificate.issuer.CertificateIssuerRepository;
import com.styl.device.management.rest.portal.admin.pki.ca.TruststoreConfiguration;
import com.styl.device.management.rest.portal.admin.pki.certificate.CertificateRequest;
import com.styl.device.management.rest.portal.admin.pki.issuer.UpdateIssuerRequest;
import com.styl.device.management.service.vault.pki.DefaultIssuerResponse;
import com.styl.device.management.service.vault.pki.RevokeCertResponse;
import com.styl.device.management.service.vault.pki.RevokeIssuerResponse;
import com.styl.device.management.service.vault.pki.RevokeRequest;
import com.styl.device.management.service.vault.pki.RoleResponse;
import com.styl.device.management.service.vault.pki.SignCertificateResponse;
import com.styl.device.management.service.vault.pki.VaultCertificateAuthority;
import com.styl.device.management.service.vault.pki.VaultIssuerResponse;
import com.styl.device.management.service.vault.pki.VaultPkiService;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR>
 *
 */

@Service
public class PkiServiceImpl implements PkiService {

	private static final Logger logger = LoggerFactory.getLogger(PkiServiceImpl.class);

	@Autowired
	private VaultPkiService vaultPkiService;

	@Autowired
	private CertificateIssuerRepository certificateIssuerRepository;

	@Autowired
	private CertificateRepository certificateRepository;

	@Autowired
	private CertificateAuthorityRepository certificateAuthorityRepository;

	@Autowired
	private PkiTruststoreService pkiTruststoreService;

	@Override
	public List<CertificateIssuer> list() {
		return certificateIssuerRepository.findAll();
	}

	@Override
	public Pagination<CertificateIssuer> listIssuers(int page, int pageSize, String caName) {
		Page<CertificateIssuer> pages = certificateIssuerRepository.findByCaName(caName,
				PageRequest.of(page, pageSize));
		List<CertificateIssuer> issuers = pages.getContent();
		if (pages.getTotalElements() > 0) {
			String defaultIssuer = vaultPkiService.getDefaultIssuer(caName).getDefaultIssuer();
			for (CertificateIssuer issuer : issuers) {
				if (issuer.getIssuerId().equals(defaultIssuer)) {
					issuer.setDefault(true);
				}
			}
		}

		return new Pagination<>(pages.getTotalElements(), page + 1, pageSize, issuers);
	}

	@Override
	public CertificateIssuer addIssuer(String caName, String issuerName, long ttl) {
		if (certificateIssuerRepository.findByIssuerName(issuerName) != null) {
			throw new ServiceException(ErrorCode.PKI_ISSUER_EXISTED);
		}
		VaultIssuerResponse vaultIssuer = vaultPkiService.addIssuer(caName, issuerName, ttl);
		CertificateIssuer certificateIssuer = new CertificateIssuer(caName, issuerName, vaultIssuer);

		// upload every single issuer to system's truststore
		pkiTruststoreService.uploadCertificateIssuerAsync(caName, certificateIssuer);

		return certificateIssuerRepository.save(certificateIssuer);
	}

	@Override
	public CertificateIssuer updateIssuer(String caName, String issuerId, UpdateIssuerRequest updateIssuerRequest) {
		CertificateIssuer issuer = certificateIssuerRepository.findByCaNameAndIssuerId(caName, issuerId);
		issuer.setIssuerName(updateIssuerRequest.getIssuerName());
		return certificateIssuerRepository.save(issuer);
	}

	@Override
	public Certificate signCertificate(String caName, CertificateRequest request) {
		SignCertificateResponse signResponse = vaultPkiService.signCertificate(request.getCommonName(), caName,
				request.getCsr(), request.getTtl());
		DefaultIssuerResponse defaultIssuer = vaultPkiService.getDefaultIssuer(caName);

		org.springframework.vault.support.Certificate vaultCert = org.springframework.vault.support.Certificate.of(
				signResponse.getSerialNumber(), signResponse.getCertificate(), signResponse.getIssuingCaCertificate());

		CertificateIssuer issuerDefault = certificateIssuerRepository.findById(defaultIssuer.getDefaultIssuer())
				.orElseThrow(() -> new ServiceException(ErrorCode.PKI_ISSUER_DEFAULT_NOT_STORED_IN_DB));

		Certificate certificate = new Certificate(caName, defaultIssuer.getDefaultIssuer(), vaultCert);
		certificate.setIssuerName(issuerDefault.getIssuerName());
		certificate.setCertificate(signResponse.getCertificate());
		return certificateRepository.save(certificate);
	}

	@Override
	public Certificate signDeviceCertificate(String caName, DeviceCertificateRequest request) {
		DefaultIssuerResponse defaultIssuer = vaultPkiService.getDefaultIssuer(caName);

		CertificateIssuer issuerDefault = certificateIssuerRepository.findById(defaultIssuer.getDefaultIssuer())
				.orElseThrow(() -> new ServiceException(ErrorCode.PKI_ISSUER_DEFAULT_NOT_STORED_IN_DB));

		RoleResponse role = vaultPkiService.getRole(caName);
		long defaultTtl = role.getTtl();
		SignCertificateResponse signResponse = vaultPkiService.signCertificate(request.getCommonName(), caName,
				request.getCsr(), defaultTtl);

		org.springframework.vault.support.Certificate vaultCert = org.springframework.vault.support.Certificate.of(
				signResponse.getSerialNumber(), signResponse.getCertificate(), signResponse.getIssuingCaCertificate());

		Certificate certificate = new Certificate(caName, defaultIssuer.getDefaultIssuer(), vaultCert);

		certificate.setIssuerName(issuerDefault.getIssuerName());
		certificate.setCertificate(signResponse.getCertificate());

		logger.debug("New certificate {} - {} expiration: {}", certificate.getCommonName(),
				certificate.getSerialNumber(), certificate.getExpiryTime());

		return certificateRepository.save(certificate);
	}

	@Override
	public Pagination<Certificate> listCertificates(int page, int pageSize, String caName, String commonName,
			String serialNumber, String issuerName) {

		CertificateSpecification specs = new CertificateSpecification(caName, commonName, serialNumber, issuerName);
		Page<Certificate> pages = certificateRepository.findAll(specs, PageRequest.of(page, pageSize));

		return new Pagination<>(pages.getTotalElements(), page + 1, pageSize, pages.getContent());
	}

	@Override
	public CertificateIssuer findIssuer(String caName, String issuerId) {
		return certificateIssuerRepository.findByCaNameAndIssuerId(caName, issuerId);
	}

	@Override
	public Certificate findCertificate(String caName, String serialNumber) {
		return certificateRepository.findByCaNameAndSerialNumber(caName, serialNumber);
	}

	@Override
	@Transactional
	public RevokeIssuerResponse revokeIssuer(String caName, String issuerId) {
		CertificateIssuer existingIssuer = Optional
				.ofNullable(certificateIssuerRepository.findByCaNameAndIssuerId(caName, issuerId))
				.orElseThrow(() -> new ServiceException(ErrorCode.PKI_ISSUER_NOT_FOUND));

		RevokeIssuerResponse revoke = vaultPkiService.revokeIssuer(caName, issuerId);
		existingIssuer.setRevokedTime(revoke.getRevocationTime());
		return revoke;
	}

	@Override
	@Transactional
	public RevokeCertResponse revokeCertificate(String caName, String serialNumber) {

		logger.debug("revokeCertificate: {}/{}", caName, serialNumber);

		Certificate existingCert = certificateRepository.findById(serialNumber)
				.orElseThrow(() -> new ServiceException(ErrorCode.PKI_CERTIFICATE_NOT_FOUND));

		RevokeCertResponse revoke = vaultPkiService.revokeCertificate(new RevokeRequest(serialNumber), caName);
		existingCert.setRevokedTime(revoke.getRevocationTime());
		return revoke;
	}

	@Override
	@Transactional
	public TruststoreConfiguration saveTruststoreConfiguration(String caName, TruststoreConfiguration config) {
		// check if CA existed in Vault
		Optional<VaultCertificateAuthority> ob = vaultPkiService.getAuthorities().stream()
				.filter(ca -> ca.getName().equals(caName)).findFirst();
		if (ob.isEmpty()) {
			throw new ServiceException(ErrorCode.PKI_CA_NOT_FOUND);
		}

		CertificateAuthority ca = certificateAuthorityRepository.findByCaName(caName).orElse(null);

		if (ca != null) {
			ca.setConfig(config);
		} else {
			ca = new CertificateAuthority(caName, config);
		}

		certificateAuthorityRepository.save(ca);
		return new TruststoreConfiguration(ca.isAutoSync(), ca.getBucket(), ca.getRegion(), ca.isUseAccessKey(),
				ca.getAccessKeyId(), ca.getSecretAccessKey());
	}

	@Override
	@Transactional
	public TruststoreConfiguration findTruststoreConfigurationByCaName(String caName) {
		CertificateAuthority ca = certificateAuthorityRepository.findByCaName(caName).orElse(null);

		if (ca == null) {
			return new TruststoreConfiguration(false, null, null, null, null, null);
		}

		return new TruststoreConfiguration(ca.isAutoSync(), ca.getBucket(), ca.getRegion(), ca.isUseAccessKey(),
				ca.getAccessKeyId(), ca.getSecretAccessKey());
	}

	@Override
	@Transactional
	public void uploadIssuer(String caName, String issuerId) {
		CertificateIssuer certificateIssuer = Optional
				.ofNullable(certificateIssuerRepository.findByCaNameAndIssuerId(caName, issuerId))
				.orElseThrow(() -> new ServiceException(ErrorCode.PKI_ISSUER_NOT_FOUND));
		pkiTruststoreService.uploadCertificateIssuerSync(caName, certificateIssuer);
	}

	@Override
	@Transactional
	public void removeIssuer(String caName, String issuerId) {
		CertificateIssuer certificateIssuer = Optional
				.ofNullable(certificateIssuerRepository.findByCaNameAndIssuerId(caName, issuerId))
				.orElseThrow(() -> new ServiceException(ErrorCode.PKI_ISSUER_NOT_FOUND));
		String fileName = caName + "_" + certificateIssuer.getSerialNumber() + "_ca_cert.crt";
		pkiTruststoreService.deleteCertificateIssuerSync(fileName);
	}

}
