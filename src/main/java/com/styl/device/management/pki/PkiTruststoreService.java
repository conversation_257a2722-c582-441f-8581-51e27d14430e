package com.styl.device.management.pki;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.styl.device.management.config.aws.s3.AwsS3StorageService;
import com.styl.device.management.config.aws.s3.AwsS3StorageServiceImpl;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.pki.certificate.issuer.CertificateIssuer;
import com.styl.device.management.persistence.pki.certificate.issuer.CertificateIssuerRepository;
import com.styl.device.management.service.vault.pki.VaultPkiService;

@Service
public class PkiTruststoreService {

	private static final Logger logger = LoggerFactory.getLogger(PkiTruststoreService.class);

	@Autowired
	private AwsS3StorageService awsS3StorageService;

	@Autowired
	private CertificateIssuerRepository certificateIssuerRepository;

	@Autowired
	private VaultPkiService vaultPkiService;

	public void uploadCertificateIssuerSync(String caName, CertificateIssuer certificateIssuer) {
		byte[] pemCert = vaultPkiService
				.getPemCertificate(vaultPkiService.getRootCaName(), certificateIssuer.getSerialNumber())
				.getCertificate().getBytes();
		String fileName = caName + "_" + certificateIssuer.getSerialNumber() + "_ca_cert.crt";
		String objectKey = AwsS3StorageServiceImpl.TRUSTSTORE + "/" + fileName;

		try {
			awsS3StorageService.storeFile(objectKey, pemCert, "application/x-pem-file");

			certificateIssuer.setObjectKey(objectKey);
			certificateIssuerRepository.save(certificateIssuer);
		} catch (Exception e) {
			logger.info("Exception", e);
			throw new ServiceException(ErrorCode.PKI_TRUSTSTORE_CONNECTION_ERROR);
		}
	}

	@Async
	public void uploadCertificateIssuerAsync(String caName, CertificateIssuer certificateIssuer) {
		uploadCertificateIssuerSync(caName, certificateIssuer);
	}

	public void deleteCertificateIssuerSync(String fileName) {
		String objectKey = AwsS3StorageServiceImpl.TRUSTSTORE + "/" + fileName;
		try {
			awsS3StorageService.deleteFile(objectKey);
			CertificateIssuer certificateIssuer = certificateIssuerRepository.findByObjectKey(objectKey).get();
			certificateIssuer.setObjectKey(null);
			certificateIssuerRepository.save(certificateIssuer);
		} catch (Exception e) {
			logger.info("Exception", e);
			throw new ServiceException(ErrorCode.PKI_TRUSTSTORE_CONNECTION_ERROR);
		}
	}

	@Async
	public void deleteCertificateIssuerAsync(String fileName) {
		deleteCertificateIssuerSync(fileName);
	}
}
