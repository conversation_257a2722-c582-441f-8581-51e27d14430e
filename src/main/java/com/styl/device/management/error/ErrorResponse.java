/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.error;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class ErrorResponse {

    private List<Error> errors;

    public ErrorResponse() {
        super();
    }

    public ErrorResponse(List<Error> errors) {
        super();
        this.errors = errors;
    }

    public ErrorResponse(Error error) {
        super();
        this.errors = Arrays.asList(error);
    }

    public ErrorResponse(ErrorCode error) {
        this(new Error(error.code, error.errorCode, error.errorMessage));
    }

    public ErrorResponse(String value, ErrorCode error) {
        this(new Error(value, error.code,error.errorCode, error.errorMessage));
    }

    public ErrorResponse(int code, String errorCode, String errorMessage) {
        this(new Error(code, errorCode, errorMessage));
    }

    public ErrorResponse(String value, int code, String errorCode, String errorMessage) {
        this(new Error(value, code, errorCode, errorMessage));
    }

    public ErrorResponse(int code, String errorCode) {
        this(new Error(code, errorCode, null));
    }
    
    public ErrorResponse(String errorCode, String errorMessage) {
        this(new Error(errorCode, errorMessage));
    }
    
    public ErrorResponse(String value, int errorCode, String errorMessage) {
        this(new Error(value, errorCode, errorMessage));
    }

    public ErrorResponse(int errorCode) {
        this(new Error(errorCode, null));
    }

    public List<Error> getErrors() {
        return errors;
    }

    public void setErrors(List<Error> errors) {
        this.errors = errors;
    }

    public static class Error {

        private String value;
        
        private int code;

        private String errorCode;

        private String errorMessage;

        public Error(int code, String errorCode, String errorMessage) {
            super();
            this.code = code;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public Error(int errorCode, String errorMessage) {
            super();
            this.errorCode = String.valueOf(errorCode);
            this.errorMessage = errorMessage;
        }
        
        public Error(String errorCode, String errorMessage) {
            super();
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public Error(String value, int code, String errorCode, String errorMessage) {
            this(code, errorCode, errorMessage);
            this.value = value;
        }

        public Error(String value, int errorCode, String errorMessage) {
            this(errorCode, errorMessage);
            this.value = value;
        }
        
        public int getCode() {
			return code;
		}

		public void setCode(int code) {
			this.code = code;
		}

		public String getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

    }

}
