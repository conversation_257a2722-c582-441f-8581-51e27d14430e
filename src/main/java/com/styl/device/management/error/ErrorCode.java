/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.error;

/**
 * <AUTHOR> Lam
 *
 */
public enum ErrorCode {

	// System error 9xxxxx

	UNKNOWN("error.unknown", 999999),

	ERROR_GENERAL("error.general", 900000),

	INVALID_FORMAT("error.invalid.format", 900001),

	INVALID_FIELD("error.invalid.field", 900002),

	INVALID_PARAM("error.invalid.param", 900003),

	// Software error 10000 -> 10999

	SOFTWARE_GENERATE_URL_ERROR("software.generate.url", 10000),

	SOFTWARE_NOT_FOUND("software.not.found", 10001),

	SOFTWARE_EXISTED("software.existed", 10002),

	SOFTWARE_NAME_EXISTED("software.name.existed", 10003),

	SOFTWARE_CAN_NOT_REMOVE("software.can.not.remove", 10004),

	SOFTWARE_VERSION_FILE_NOT_FOUND("software.version.file.not.found", 10005),

	SOFTWARE_VERSION_EXISTED("software.version.existed", 10006),

	SOFTWARE_VERSION_NOT_FOUND("software.version.not.found", 10007),

	SOFTWARE_VERSION_CAN_NOT_ADD("software.version.can.not.add", 10008),

	SOFTWARE_VERSION_CAN_NOT_REMOVE("software.version.can.not.remove", 10009),

	SOFTWARE_VERSION_INVALID_CHECKSUM("software.version.invalid.checksum", 10010),

	SOFTWARE_CAN_NOT_DOWNLOAD("software.version.can.not.download", 10011),

	SOFTWARE_PACKAGE_NOT_FOUND("software.package.not.found", 10012),

	UPDATE_MODE_NOT_FOUND("software.update.mode.not.found", 10013),

	SOFTWARE_VERSION_NOT_ASSIGNABLE("software.version.not.assignable", 10014),

	SOFTWARE_PACKAGE_NOT_CANCELABLE("software.package.not.cancelable", 10015),

	// Service Platform Error 12001 -> 12999

	SERVICE_PLATFORM_NOT_FOUND("service.platform.not.found", 12001),

	SERVICE_PLATFORM_EXISTED("service.platform.existed", 12002),

	SERVICE_PLATFORM_NAME_EXISTED("service.platform.name.existed", 12003),

	SERVICE_PLATFORM_SHORTNAME_EXISTED("service.platform.shortname.existed", 12004),

	SERVICE_PLATFORM_CAN_NOT_REMOVE("service.platform.can.not.remove", 12005),

	SERVICE_PLATFORM_MISS_CA_NAME("service.platform.miss.ca.name", 12006),

	SERVICE_PLATFORM_NOTIFICATION_SETTING_NOT_FOUND("service.platform.notification.setting.not.found", 12007),

	SERVICE_PLATFORM_NOTIFICATION_SETTING_EVENT_VERSION_NOT_FOUND(
			"service.platform.notification.setting.event.version.not.found", 12008),

	SERVICE_PLATFORM_TOPIC_CREATE_FAILED("service.platform.topic.create.failed", 12009),

	SERVICE_PLATFORM_TOPIC_READ_FAILED("service.platform.topic.read.failed", 12010),

	SERVICE_PLATFORM_WEBHOOK_CALL_FAILED("service.platform.webhook.call.failed", 12011),

	// Device Error 13000 -> 13999

	DEVICE_NOT_FOUND("device.not.found", 13000),

	DEVICE_PENDING_REGISTER_NOT_FOUND("device.pending.register.not.found", 13001),

	INVALID_ACTIVATION_CODE("device.invalid.activation.code", 13002),

	DEVICE_ALREADY_EXISTED("device.already.existed", 13003),

	DEVICE_CHALLENGE_NOT_FOUND("device.challenge.not.found", 13004),

	DEVICE_MODEL_NOT_FOUND("device.model.not.found", 13005),

	DEVICE_HAS_BEEN_ASSIGNED_SP("device.has.been.assign.sp", 13006),

	DEVICE_NEED_ASSIGN_SP("device.need.assign.sp", 13007),

	DEVICE_NOT_ALLOW_TRANSPORT("device.not.allow.transport", 13008),

	// PKI error
	PKI_VAULT_EXCEPTION("pki.vault.exception", 13500),

	PKI_ISSUER_EXISTED("pki.issuer.existed", 13501),

	PKI_CA_NOT_FOUND("pki.ca.not.found", 13502),

	PKI_ROOT_ISSUER_NOT_FOUND("pki.root.issuer.not.found", 13503),

	PKI_ISSUER_NOT_FOUND("pki.issuer.not.found", 13504),

	PKI_CERTIFICATE_NOT_FOUND("pki.certificate.not.found", 13505),

	PKI_CSR_NOT_FOUND("pki.csr.not.found", 13506),

	PKI_ISSUER_DEFAULT_NOT_STORED_IN_DB("pki.issuer.default.not.stored.in.db", 13507),

	PKI_REVOKED_CERT_FAIL("pki.revoked.cert.failed", 13508),

	PKI_REVOKED_ISSUER_FAIL("pki.revoked.issuer.failed", 13509),

	PKI_CERTIFICATE_INVALID("pki.certificate.invalid", 13510),

	PKI_TRUSTSTORE_BUCKET_NOT_FOUND("pki.truststore.bucket.not.found", 13600),

	PKI_TRUSTSTORE_CREDENTIALS_INVALID("pki.truststore.invalid.credential", 13601),

	PKI_TRUSTSTORE_CONNECTION_ERROR("pki.truststore.bucket.not.found", 13602),

	// Service platform external 14000 -> 14999

	SERVICE_PLATFORM_AUTH_API_KEY_EMPTY("service.platform.auth.api.key.empty", 14000),

	SERVICE_PLATFORM_AUTH_API_EXPIRED("service.platform.auth.api.key.expired", 14001),

	SERVICE_PLATFORM_AUTH_API_KEY_INVALID("service.platform.auth.api.key.invalid", 14002),

	SERVICE_PLATFORM_AUTH_SIGNATURE_EMPTY("service.platform.auth.signature.empty", 14003),

	SERVICE_PLATFORM_AUTH_SIGNATURE_NOT_MATCH("service.platform.auth.signature.not.match", 14004),

	SERVICE_PLATFORM_AUTH_NONCE_EMPTY("service.platform.auth.nonce.empty", 14004),

	SERVICE_PLATFORM_AUTH_NONCE_TIMEOUT("service.platform.auth.nonce.timeout", 14005),

	SERVICE_PLATFORM_AUTH_NONCE_RANDOM_LEN_INVALID("service.platform.auth.nonce.random.length.invalid", 14006),

	SERVICE_PLATFORM_AUTH_NONCE_INVALID("service.platform.auth.nonce.invalid", 14007),

	SERVICE_PLATFORM_AUTH_FAILED("service.platform.auth.failed", 14008),

	API_KEY_NOT_FOUND("api.key.not.found", 14009),

	API_KEY_EXPIRED("api.key.expired", 14010),

	API_KEY_CANNOT_BE_GENERATE("api.key.cannot.be.generated", 14011),

	SERVICE_PLATFORM_WEBHOOK_URL_NOT_FOUND("errors.service.platform.webhook.url.not.found", 14012),

	// Tag management 15000 -> 15999
	TAG_ALREADY_EXISTED("tag.already.existed", 15000),

	TAG_NOT_FOUND("tag.not.found", 15001),

	TAG_CAN_NOT_REMOVE("tag.can.not.remove", 15002),

	TAG_NAME_ALREADY_EXISTED("tag.name.already.existed", 15003);

	int code;
	String errorCode;
	String errorMessage;

	/**
	 * @param errorCode
	 */
	private ErrorCode(String errorCode, int code) {
		this.errorCode = errorCode;
		this.errorMessage = getErrorMessage(errorCode);
		this.code = code;
	}

	public int getCode() {
		return code;
	}

	public String getErrorCode() {
		return this.errorCode;
	}

	public String getErrorMessage() {
		return this.errorMessage;
	}

	public static String getErrorMessage(String errorCode) {
		String errorMessage = ErrorProperties.errors.get(errorCode);
		if (errorMessage != null)
			return errorMessage;
		return "Error message is not defined";
	}
}
