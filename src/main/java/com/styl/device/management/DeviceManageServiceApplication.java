/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;

import com.styl.device.management.config.aws.secret.manager.AfterSecretManagerConfigururedEvent;

/**
 * <AUTHOR> Lam
 *
 */

@EnableAsync
@SpringBootApplication
@EnableJpaRepositories(basePackages = "com.styl.device.management")
public class DeviceManageServiceApplication {

	public static void main(String[] args) {
		SpringApplication springApplication = new SpringApplication(DeviceManageServiceApplication.class);
		springApplication.addListeners(new AfterSecretManagerConfigururedEvent());
		springApplication.run(args);
	}	
}
