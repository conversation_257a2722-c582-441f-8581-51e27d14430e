/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.model;

import java.io.Serializable;
import java.util.Objects;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

/**
 * <AUTHOR> Lam
 *
 */
@Embeddable
public class SoftwareDeviceModelId implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6226350111507370498L;

	@Column(name = "software_id")
	private String softwareId;

	@Column(name = "device_model")
	private int deviceModel;

	@Override
	public int hashCode() {
		return softwareId.hashCode() * 31 + deviceModel;
	}

	@Override
	public boolean equals(Object obj) {
		if (!(obj instanceof SoftwareDeviceModelId)) {
			return false;
		}
		SoftwareDeviceModelId anotherId = (SoftwareDeviceModelId) obj;
		return Objects.equals(this.softwareId, anotherId.softwareId)
				&& Objects.equals(this.deviceModel, anotherId.deviceModel);
	}

	public String getSoftwareId() {
		return softwareId;
	}

	public void setSoftwareId(String softwareId) {
		this.softwareId = softwareId;
	}

	public int getDeviceModel() {
		return deviceModel;
	}

	public void setDeviceModel(int deviceModel) {
		this.deviceModel = deviceModel;
	}

}
