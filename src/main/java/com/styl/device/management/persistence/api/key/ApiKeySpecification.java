/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.persistence.api.key;

import java.util.ArrayList;
import java.util.List;

import com.styl.device.management.utils.CustomSpecification;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

/**
 * <AUTHOR> Yee
 *
 */
public class ApiKeySpecification extends CustomSpecification<ApiKey> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5042978734087931834L;

	private Long id;

	private Integer servicePlatformId;

	private String apiKey;

	private Long createdFromTime;

	private Long createdToTime;

	private Long expiredFromTime;

	private Long expiredToTime;

	private Boolean enabled;

	public ApiKeySpecification(Long id, Integer servicePlatformId, String apiKey, Long createdFromTime,
			Long createdToTime, Long expiredFromTime, Long expiredToTime) {
		this.id = id;
		this.servicePlatformId = servicePlatformId;
		this.apiKey = apiKey;
		this.createdFromTime = createdFromTime;
		this.createdToTime = createdToTime;
		this.expiredFromTime = expiredFromTime;
		this.expiredToTime = expiredToTime;
	}

	public ApiKeySpecification(Integer servicePlatformId) {
		this.servicePlatformId = servicePlatformId;
	}

	@Override
	public Predicate toPredicate(Root<ApiKey> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
		List<Predicate> predicates = new ArrayList<>();
		if (isNotNull(id)) {
			predicates.add(equals(cb, root.get("id"), id));
		}

		if (isNotNull(servicePlatformId)) {
			predicates.add(equals(cb, root.get("servicePlatformId"), servicePlatformId));
		}

		if (isNotBlank(apiKey)) {
			predicates.add(like(cb, root.get("apiKey"), apiKey));
		}

		if (isNotNull(createdFromTime)) {
			predicates.add(greaterThanOrEqualTo(cb, root.get("createdTime"), createdFromTime));
		}

		if (isNotNull(createdToTime)) {
			predicates.add(lessThanOrEqualTo(cb, root.get("createdTime"), createdToTime));
		}

		if (isNotNull(expiredFromTime)) {
			predicates.add(greaterThanOrEqualTo(cb, root.get("expiredTime"), expiredFromTime));
		}

		if (isNotNull(expiredToTime)) {
			predicates.add(lessThanOrEqualTo(cb, root.get("expiredTime"), expiredToTime));
		}

		if (isNotNull(enabled)) {
			predicates.add(equals(cb, root.get("enabled"), enabled));
		}

		return cb.and(predicates.toArray(new Predicate[predicates.size()]));
	}

}
