/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.device.model.DeviceModelRepository;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.service.platform.ServicePlatformService;
import com.styl.device.management.rest.portal.admin.software.SoftwareAddRequest;
import com.styl.device.management.rest.portal.admin.software.SoftwareResponse;
import com.styl.device.management.rest.portal.admin.software.SoftwareUpdateRequest;
import com.styl.device.management.service.platform.event.publisher.EventQueueHandler;
import com.styl.device.management.service.platform.event.publisher.event.SoftwareAddedEvent;
import com.styl.device.management.service.platform.event.publisher.event.SoftwareRemovedEvent;
import com.styl.device.management.service.platform.event.publisher.event.SoftwareUpdatedEvent;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwareAddedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwareRemovedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwareUpdatedEventData;
import com.styl.device.management.utils.Pagination;
import com.styl.device.management.utils.Utils;

/**
 * <AUTHOR> Lam
 *
 */
@Service
public class SoftwareServiceImpl implements SoftwareService {

	private static final Logger logger = LoggerFactory.getLogger(SoftwareServiceImpl.class);

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private SoftwareRepository softwareRepository;

	@Autowired
	private DeviceModelRepository deviceModelRepository;

	@Autowired
	private ServicePlatformService spService;

	/**
	 * @param packageId
	 * @return Software throw error if not existed
	 */
	@Override
	public Software findByPackageId(String packageId) {
		return softwareRepository.findByPackageName(packageId)
				.orElseThrow(() -> new ServiceException(ErrorCode.SOFTWARE_NOT_FOUND));
	}

	@Override
	@Transactional
	public Software addSoftware(SoftwareAddRequest softwareAddRequest) {
		List<Software> listSoftware = softwareRepository.findByPackageNameOrName(softwareAddRequest.getPackageName(),
				softwareAddRequest.getName());
		if (listSoftware.size() > 0) {
			throw new ServiceException(ErrorCode.SOFTWARE_EXISTED);
		}

		Software newSoftware = new Software(softwareAddRequest);
		Set<String> models = softwareAddRequest.getDeviceModels();
		newSoftware.setDeviceModels(null);
		if (models != null && !models.isEmpty()) {
			Set<DeviceModel> addDeviceModels = new HashSet<DeviceModel>();
			for (String model : models) {
				DeviceModel deviceModel = deviceModelRepository.findById(model).orElse(null);
				// if model not exist in database
				if (deviceModel == null) {
					deviceModel = new DeviceModel(model, "");
					entityManager.persist(deviceModel);
					entityManager.flush();
				}
				addDeviceModels.add(deviceModel);
			}
			newSoftware.setDeviceModels(addDeviceModels);
		}

		newSoftware.setServicePlatform(null);

		Integer spId = softwareAddRequest.getServicePlatformId();
		ServicePlatform servicePlatform = spService.findByServicePlatformId(spId);
		newSoftware.setServicePlatform(servicePlatform);

		logger.debug("Software: {}", softwareAddRequest.getPackageName());
		entityManager.persist(newSoftware);

		eventQueue.queue(new SoftwareAddedEvent(servicePlatform, new SoftwareAddedEventData(newSoftware)));
		return newSoftware;
	}

	@Override
	@Transactional
	public Software updateSoftware(SoftwareUpdateRequest updateRequest) {
		Software existingSoftware = softwareRepository.findById(updateRequest.getId())
				.orElseThrow(() -> new ServiceException(ErrorCode.SOFTWARE_NOT_FOUND));

		if (updateRequest.getServicePlatformId() != null) {
			ServicePlatform sp = spService.findByServicePlatformId(updateRequest.getServicePlatformId());
			existingSoftware.setServicePlatform(sp);
		} else {
			if (existingSoftware.getServicePlatformId() != null) {
				existingSoftware.setServicePlatform(null);
			}
		}

		if (!StringUtils.equals(updateRequest.getName(), existingSoftware.getName())) {
			softwareRepository.findByName(updateRequest.getName()).ifPresent(sof -> {
				throw new ServiceException(ErrorCode.SOFTWARE_NAME_EXISTED);
			});
		}

		Set<DeviceModel> addDeviceModels = new HashSet<DeviceModel>();

		if (updateRequest.getDeviceModels() != null) {
			for (String model : updateRequest.getDeviceModels()) {
				DeviceModel deviceModel = deviceModelRepository.findById(model).orElse(null);
				// if model not exist in database
				if (deviceModel == null) {
					deviceModel = new DeviceModel(model, "");
					entityManager.persist(deviceModel);
					entityManager.flush();
				}
				addDeviceModels.add(deviceModel);
			}
		}

		existingSoftware.setDeviceModels(addDeviceModels);
		existingSoftware.setName(updateRequest.getName());
		existingSoftware.setDescription(updateRequest.getDescription());

		eventQueue.queue(new SoftwareUpdatedEvent(existingSoftware.getServicePlatform(),
				new SoftwareUpdatedEventData(existingSoftware)));

		return existingSoftware;
	}

	@Override
	public Pagination<SoftwareResponse> listSoftware(String packageName, String name, Set<String> deviceModels,
			Integer servicePlatformId, String sortBy, String order, int page, int pageSize) {
		Utils.validatePagination(page, pageSize);
		Pageable paging = PageRequest.of(page, pageSize, Sort
				.by(StringUtils.equalsIgnoreCase(order, "DESC") ? Sort.Direction.DESC : Sort.Direction.ASC, sortBy));

		SoftwareSpecification specs = new SoftwareSpecification(null, servicePlatformId, name, null, packageName,
				deviceModels);
		Page<Software> listSoftware = softwareRepository.findAll(specs, paging);
		Long totalItems = listSoftware.getTotalElements();

		// Transfer DTO
		List<SoftwareResponse> listResult = listSoftware.stream().map(s -> {
			return new SoftwareResponse(s);
		}).collect(Collectors.toList());
		return new Pagination<SoftwareResponse>(totalItems, page + 1, pageSize, listResult);
	}

	@Override
	public boolean removeSoftware(Long id) {
		Software existingSoftware = softwareRepository.findById(id)
				.orElseThrow(() -> new ServiceException(ErrorCode.SOFTWARE_NOT_FOUND));
		softwareRepository.delete(existingSoftware);

		eventQueue.queue(
				new SoftwareRemovedEvent(existingSoftware.getServicePlatform(), new SoftwareRemovedEventData(id)));

		return true;
	}

}
