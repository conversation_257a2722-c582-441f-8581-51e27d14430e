/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.pki.certificate;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

import com.styl.device.management.utils.CustomSpecification;

/**
 * <AUTHOR>
 *
 */
public class CertificateSpecification extends CustomSpecification<Certificate> {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String caName;

	private String commonName;

	private String serialNumber;

	private String issuerName;

	public CertificateSpecification(String caName, String commonName, String serialNumber, String issuerName) {
		super();
		this.caName = caName;
		this.commonName = commonName;
		this.serialNumber = serialNumber;
		this.issuerName = issuerName;
	}

	@Override
	public Predicate toPredicate(Root<Certificate> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotBlank(caName)) {
			predicates.add(equals(cb, root.get("caName"), caName));
		}

		if (isNotBlank(commonName)) {
			predicates.add(like(cb, root.get("commonName"), commonName));
		}

		if (isNotBlank(serialNumber)) {
			predicates.add(like(cb, root.get("serialNumber"), serialNumber));
		}

		if (isNotBlank(issuerName)) {
			predicates.add(like(cb, root.get("issuerName"), issuerName));
		}

		return cb.and(predicates.toArray(new Predicate[predicates.size()]));
	}

}
