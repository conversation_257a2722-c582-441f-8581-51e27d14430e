/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Lam
 *
 */
@Repository
public interface DeviceRepository extends JpaRepository<Device, String>, JpaSpecificationExecutor<Device> {

	public Optional<Device> findByHardwareId(String hardwareId);

	/**
	 * @param model
	 * @param servicePlatform
	 * @return list device by model and service platform with current OTA
	 */
	@Query(value = "SELECT d FROM Device d WHERE (d.model.model IN :model OR :model is null) "
			+ "AND (d.servicePlatform.id = :servicePlatformId OR :servicePlatformId is null)")
	public List<Device> findByModelAndSPAndCurrentOTA(@Param("model") Set<String> model, Integer servicePlatformId);

	@Query("SELECT d FROM Device d WHERE d.id IN :ids and d.servicePlatform.id=:servicePlatformId")
	List<Device> findByServicePlatformIdAndIds(Integer servicePlatformId, List<String> ids);

	/**
	 * @param deviceUids
	 * @return
	 */
	public List<Device> findByIdIn(List<String> deviceUids);
}
