/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device.model;

import java.util.HashSet;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.software.Software;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

/**
 * <AUTHOR> Lam
 *
 */
@Entity
@Table(name = "tbl_device_model")
public class DeviceModel {

	@Id
	@Column(name = "model", unique = true, nullable = false, length = 25)
	private String model;

	@Column(name = "description", length = 100)
	private String description;

	@JsonIgnore
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "model")
	private Set<Device> devices = new HashSet<Device>(0);

	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "tbl_software_device_model", joinColumns = {
			@JoinColumn(name = "model", nullable = false, updatable = false, insertable = false) }, inverseJoinColumns = {
					@JoinColumn(name = "software_id", nullable = false, updatable = false, insertable = false) })
	private Set<Software> softwares = new HashSet<Software>(0);

	/**
	 * @param model
	 * @param description
	 */
	public DeviceModel(String model, String description) {
		this.model = model;
		this.description = description;
	}

	/**
	 * 
	 */
	public DeviceModel() {

	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Set<Software> getSoftwares() {
		return softwares;
	}

	public void setSoftwares(Set<Software> softwares) {
		this.softwares = softwares;
	}

	public Set<Device> getDevices() {
		return devices;
	}

	public void setDevices(Set<Device> devices) {
		this.devices = devices;
	}

}
