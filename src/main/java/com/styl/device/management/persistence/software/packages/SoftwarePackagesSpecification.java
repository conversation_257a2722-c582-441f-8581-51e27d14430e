/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.persistence.software.packages;

import java.util.ArrayList;
import java.util.List;

import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.tag.Tag;
import com.styl.device.management.utils.CustomSpecification;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

/**
 * <AUTHOR> Yee
 *
 */
public class SoftwarePackagesSpecification extends CustomSpecification<SoftwarePackages> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3088355314805862478L;

	String deviceUId;

	String deviceModel;

	Integer servicePlatformId;

	Long softwareId;

	String softwareName;

	String softwareVersion;

	Integer state;

	List<Integer> otaStates;

	List<Integer> tags;

	Integer updateMode;

	String originalVersion;

	String assignedBy;

	String sortBy;

	String orderBy;

	public SoftwarePackagesSpecification(Integer servicePlatformId, String deviceUId, Long softwareId,
			String softwareName, String softwareVersion, Integer state, Integer updateMode) {
		this.servicePlatformId = servicePlatformId;
		this.deviceUId = deviceUId;
		this.softwareId = softwareId;
		this.softwareName = softwareName;
		this.softwareVersion = softwareVersion;
		this.state = state;
		this.updateMode = updateMode;
	}

	public SoftwarePackagesSpecification(String deviceUId, String deviceModel, Integer servicePlatformId,
			Long softwareId, List<Integer> otaStates, List<Integer> tags, String originalVersion,
			String assignedVersion, String assignedBy, String sortBy, String orderBy) {
		this.deviceUId = deviceUId;
		this.deviceModel = deviceModel;
		this.servicePlatformId = servicePlatformId;
		this.softwareId = softwareId;
		this.otaStates = otaStates;
		this.tags = tags;
		this.originalVersion = originalVersion;
		this.softwareVersion = assignedVersion;
		this.assignedBy = assignedBy;
		this.sortBy = sortBy;
		this.orderBy = orderBy;
	}

	@Override
	public Predicate toPredicate(Root<SoftwarePackages> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotBlank(deviceUId)) {
			predicates.add(like(cb, root.get("device").get("id"), deviceUId));
		}

		if (isNotBlank(deviceModel)) {
			predicates.add(like(cb, root.get("device").get("model").get("model"), this.deviceModel));
		}

		if (isNotNull(servicePlatformId)) {
			predicates.add(equals(cb, root.get("software").get("servicePlatform").get("id"), this.servicePlatformId));
		}

		if (isNotNull(softwareId)) {
			predicates.add(equals(cb, root.get("software").get("id"), softwareId));

		}
		if (isNotBlank(softwareName)) {
			predicates.add(like(cb, root.get("software").get("name"), softwareName));
		}
		if (isNotBlank(softwareVersion)) {
			predicates.add(equals(cb, root.get("softwareVersion").get("version"), this.softwareVersion));
		}

		if (isNotNull(state)) {
			predicates.add(equals(cb, root.get("state"), state));
		}

		if (isNotNull(otaStates) && !otaStates.isEmpty()) {
			predicates.add(inIntegerList(root.get("state"), this.otaStates));
		}

		if (isNotNull(tags) && !tags.isEmpty()) {
			query.distinct(true);
			Join<SoftwarePackages, Device> device = root.join("device");
			Join<Device, Tag> tag = device.join("tags");
			predicates.add(cb.in(tag.get("id")).value(tags));
		}

		if (isNotNull(updateMode)) {
			predicates.add(equals(cb, root.get("updateMode"), updateMode));
		}

		if (isNotBlank(originalVersion)) {
			predicates.add(equals(cb, root.get("appVersion"), this.originalVersion));
		}

		if (isNotBlank(assignedBy)) {
			predicates.add(equals(cb, root.get("assignedBy"), this.assignedBy));
		}
		sortBy(sortBy, orderBy, root, query, cb);
		return cb.and(predicates.toArray(new Predicate[predicates.size()]));
	}

	@SuppressWarnings("unchecked")
	private void sortBy(String sortBy, String direction, Root<SoftwarePackages> root, CriteriaQuery<?> query,
			CriteriaBuilder cb) {
		switch (sortBy) {
		case "deviceId":
			query.orderBy(orderBy(direction, cb, root.get("device").get("id")));
			break;
		case "deviceModel":
			query.orderBy(orderBy(direction, cb, root.get("device").get("model").get("model")));
			break;
		case "softwareName":
			Fetch<SoftwarePackages, Software> fetchedSoftware = root.fetch("software");
			query.orderBy(orderBy(direction, cb, ((Join<SoftwarePackages, Software>) fetchedSoftware).get("name")));
			break;
		case "servicePlatformName":
			Fetch<Software, ServicePlatform> fetchedServicePlatform = root.fetch("software").fetch("servicePlatform");
			query.orderBy(
					orderBy(direction, cb, ((Join<Software, ServicePlatform>) fetchedServicePlatform).get("name")));
			break;
		case "assignedVersion":
			query.orderBy(orderBy(direction, cb, root.get("softwareVersion").get("version")));
			break;
		case "originalVersion":
			query.orderBy(orderBy(direction, cb, root.get("appVersion")));
			break;
		case "assignedBy":
			query.orderBy(orderBy(direction, cb, root.get("assignedBy")));
			break;
		case "assignedTime":
			query.orderBy(orderBy(direction, cb, root.get("assignedTime")));
			break;
		case "otaState":
			query.orderBy(orderBy(direction, cb, root.get("state")));
			break;
		default:
			query.orderBy(orderBy(direction, cb, root.get("lastUpdated")));
			break;
		}
	}
}
