/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software;

import java.util.Set;

import com.styl.device.management.rest.portal.admin.software.SoftwareAddRequest;
import com.styl.device.management.rest.portal.admin.software.SoftwareResponse;
import com.styl.device.management.rest.portal.admin.software.SoftwareUpdateRequest;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
public interface SoftwareService {

	/**
	 * @param softwareAddRequest
	 * @return
	 */
	Software addSoftware(SoftwareAddRequest softwareAddRequest);

	/**
	 * @param updateRequest
	 * @return
	 */
	Software updateSoftware(SoftwareUpdateRequest updateRequest);

	/**
	 * @param packageId
	 * @return Software throw error if not existed
	 */
	Software findByPackageId(String packageId);

	/**
	 * @param packageName
	 * @param name
	 * @param deviceModels
	 * @param servicePlatformId
	 * @param sortBy
	 * @param order
	 * @param page
	 * @param pageSize
	 * @return
	 */
	Pagination<SoftwareResponse> listSoftware(String packageName, String name, Set<String> deviceModels,
			Integer servicePlatformId, String sortBy, String order, int page, int pageSize);

	/**
	 * @param id
	 * @return
	 */
	boolean removeSoftware(Long id);

}
