/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;
import org.hibernate.id.OptimizableGenerator;

import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.device.registraion.DeviceRegistration;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.tag.Tag;
import com.styl.device.management.rest.device.software.DeviceSoftware;
import com.styl.device.management.utils.JsonToMapConverter;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

/**
 * <AUTHOR> Lam
 *
 */
@Entity
@Table(name = "tbl_device")
public class Device {

	public static final int AVAILABLE = 1;
	public static final int ASSIGNED = 2;
	public static final int RETIRED = 3;

	@Id
	@GeneratedValue(generator = "device-id-generator")
	@GenericGenerator(name = "device-id-generator", type = DeviceIdGenerator.class, parameters = {
			@Parameter(name = "sequence_name", value = "tbl_seq_device"),
			@Parameter(name = OptimizableGenerator.INCREMENT_PARAM, value = "1") })
	private String id;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "model", nullable = false)
	private DeviceModel model;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "service_platform_id", nullable = true)
	private ServicePlatform servicePlatform;

	@Column(name = "hardware_id", unique = true, nullable = false, length = 256)
	private String hardwareId;

	@Column(name = "state", nullable = false)
	private Integer state;

	@Column(name = "sim_id", length = 64)
	private String simId;

	@Column(name = "imei", length = 64)
	private String imei;

	@Column(name = "first_registration_time", nullable = false)
	private Long firstRegistrationTime;

	@Column(name = "last_registration_time")
	private Long lastRegistrationTime;

	@Column(name = "created_time")
	private Long createdTime;

	@Column(name = "created_by", length = 45)
	private String createdBy;

	@Column(name = "current_softwares", length = 1048)
	@Convert(converter = JsonToMapConverter.class)
	private List<DeviceSoftware> currentSoftwares;

	@Column(name = "allow_transport")
	private boolean allowTransport;

	@Column(name = "force_renew")
	private boolean forceRenew;

	@Column(name = "csr")
	private String csr;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "device")
	private Set<SoftwarePackages> softwarePackages = new HashSet<>(0);

	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = "tbl_tag_assign", joinColumns = {
			@JoinColumn(name = "device_id", nullable = false, updatable = false, insertable = false) }, inverseJoinColumns = {
					@JoinColumn(name = "tag_id", nullable = false, updatable = false, insertable = false) })
	private Set<Tag> tags = new HashSet<>(0);

	public Device() {

	}

	public Device(DeviceModel model, ServicePlatform servicePlatform, String hardwareId, Integer state, String simId,
			String imei, Long firstRegistrationTime, Long lastRegistrationTime) {
		this.model = model;
		this.servicePlatform = servicePlatform;
		this.hardwareId = hardwareId;
		this.state = state;
		this.simId = simId;
		this.imei = imei;
		this.firstRegistrationTime = firstRegistrationTime;
		this.lastRegistrationTime = lastRegistrationTime;
	}

	public Device(DeviceRegistration deviceRegis, DeviceModel model) {
		this.model = model;
		this.hardwareId = deviceRegis.getHardwareId();
		this.simId = deviceRegis.getSimId();
		this.imei = deviceRegis.getImei();
		this.firstRegistrationTime = System.currentTimeMillis();
		this.lastRegistrationTime = System.currentTimeMillis();
		this.createdTime = System.currentTimeMillis();
		this.createdBy = "";
	}

	public Device(String id, DeviceModel model, ServicePlatform servicePlatform, String hardwareId, boolean isActive,
			String simId, String imei, Long firstRegistrationTime, Long lastRegistrationTime, Long createdTime,
			String createdBy) {
		this.id = id;
		this.model = model;
		this.servicePlatform = servicePlatform;
		this.hardwareId = hardwareId;
		this.simId = simId;
		this.imei = imei;
		this.firstRegistrationTime = firstRegistrationTime;
		this.lastRegistrationTime = lastRegistrationTime;
		this.createdTime = createdTime;
		this.createdBy = createdBy;
	}

	public static String getStateString(int state) {
		switch (state) {
		case ASSIGNED:
			return "ASSIGNED";
		case AVAILABLE:
			return "AVAILABLE";
		case RETIRED:
			return "RETIRED";
		default:
			return String.valueOf(state);
		}
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public DeviceModel getModel() {
		return model;
	}

	public void setModel(DeviceModel model) {
		this.model = model;
	}

	public String getHardwareId() {
		return hardwareId;
	}

	public void setHardwareId(String hardwareId) {
		this.hardwareId = hardwareId;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getSimId() {
		return simId;
	}

	public void setSimId(String simId) {
		this.simId = simId;
	}

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	public long getFirstRegistrationTime() {
		return firstRegistrationTime;
	}

	public void setFirstRegistrationTime(Long firstRegistrationTime) {
		this.firstRegistrationTime = firstRegistrationTime;
	}

	public Long getLastRegistrationTime() {
		return lastRegistrationTime;
	}

	public void setLastRegistrationTime(Long lastRegistrationTime) {
		this.lastRegistrationTime = lastRegistrationTime;
	}

	public Long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(Long createdTime) {
		this.createdTime = createdTime;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Set<SoftwarePackages> getSoftwarePackages() {
		return softwarePackages;
	}

	public void setSoftwarePackages(Set<SoftwarePackages> softwarePackages) {
		this.softwarePackages = softwarePackages;
	}

	public Set<Tag> getTags() {
		return tags;
	}

	public void setTags(Set<Tag> tags) {
		this.tags = tags;
	}

	public ServicePlatform getServicePlatform() {
		return servicePlatform;
	}

	public void setServicePlatform(ServicePlatform servicePlatform) {
		this.servicePlatform = servicePlatform;
	}

	public String getUrlPlatform() {
		if (servicePlatform != null) {
			return servicePlatform.getUrl();
		}
		return null;
	}

	public List<DeviceSoftware> getCurrentSoftwares() {
		return currentSoftwares;
	}

	public void setCurrentSoftwares(List<DeviceSoftware> currentSoftwares) {
		this.currentSoftwares = currentSoftwares;
	}

	public boolean isAllowTransport() {
		return allowTransport;
	}

	public void setAllowTransport(boolean allowTransport) {
		this.allowTransport = allowTransport;
	}

	public boolean isForceRenew() {
		return forceRenew;
	}

	public void setForceRenew(boolean forceRenew) {
		this.forceRenew = forceRenew;
	}

	public String getCsr() {
		return csr;
	}

	public void setCsr(String csr) {
		this.csr = csr;
	}
}
