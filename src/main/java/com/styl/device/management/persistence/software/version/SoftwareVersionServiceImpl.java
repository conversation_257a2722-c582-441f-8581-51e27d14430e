/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.version;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.config.aws.s3.AwsS3StorageService;
import com.styl.device.management.config.aws.s3.AwsS3StorageServiceImpl;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.DeviceRepository;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.SoftwareService;
import com.styl.device.management.persistence.software.SoftwareServiceImpl;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesRepository;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesService;
import com.styl.device.management.persistence.software.update.mode.UpdateMode;
import com.styl.device.management.persistence.software.update.mode.UpdateModeRepository;
import com.styl.device.management.rest.device.software.DeviceSoftware;
import com.styl.device.management.rest.device.software.SoftwareInformation;
import com.styl.device.management.rest.device.software.SoftwareResponse;
import com.styl.device.management.rest.device.software.StateOTARequest;
import com.styl.device.management.rest.device.software.UpgradeRequest;
import com.styl.device.management.rest.portal.admin.device.DevicePortalResponse;
import com.styl.device.management.rest.portal.admin.software.version.AssignSoftware;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareUrlResponse;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareVersionAddRequest;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareVersionResponse;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareVersionUpdateRequest;
import com.styl.device.management.service.platform.event.publisher.EventQueueHandler;
import com.styl.device.management.service.platform.event.publisher.event.SoftwarePackageUpdatedEvent;
import com.styl.device.management.service.platform.event.publisher.event.SoftwareVersionAddedEvent;
import com.styl.device.management.service.platform.event.publisher.event.SoftwareVersionRemovedEvent;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwarePackageUpdatedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwareVersionAddedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwareVersionRemovedEventData;
import com.styl.device.management.utils.Pagination;
import com.styl.device.management.utils.Utils;

/**
 * <AUTHOR> Lam
 *
 */
@Service
public class SoftwareVersionServiceImpl implements SoftwareVersionService {

	private static final Logger logger = LoggerFactory.getLogger(SoftwareServiceImpl.class);

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private SoftwareService softwareService;

	@Autowired
	private SoftwareVersionRepository softwareVersionRepository;

	@Autowired
	private AwsS3StorageService awsS3StorageService;

	@Autowired
	private UpdateModeRepository updateModeRepository;

	@Autowired
	private SoftwarePackagesRepository softwarePackagesRepository;

	@Autowired
	private SoftwarePackagesService softwarePackagesService;

	@Autowired
	private DeviceRepository deviceRepository;

	@Value("${com.styl.device.management.model.software.version.timeout:3600000}")
	private Long timeout;

	@Override
	public SoftwareVersion findByVersionAndPackageId(String version, String packageId) {
		return softwareVersionRepository.findByVerionAndPackageName(version, packageId)
				.orElseThrow(() -> new ServiceException(ErrorCode.SOFTWARE_VERSION_NOT_FOUND));
	}

	@Override
	public SoftwareUrlResponse generateURLUploadSoftware(String md5checksum) {
		try {
			String filePath = AwsS3StorageServiceImpl.SOFTWARE_FOLDER + "/" + String.valueOf(System.currentTimeMillis())
					+ Utils.generateToken(16);
			logger.debug("File path upload software {}", filePath);
			logger.debug("md5checksum {}", md5checksum);
			Long timeExpired = System.currentTimeMillis() + timeout;
			URL url = awsS3StorageService.requestUploadUrl(filePath, timeout, md5checksum);
			return new SoftwareUrlResponse(url.toString(), timeExpired, filePath);
		} catch (Exception e) {
			logger.debug("Error for generate url upload software: " + e.getMessage());
		}
		throw new ServiceException(ErrorCode.SOFTWARE_GENERATE_URL_ERROR);
	}

	@Override
	@Transactional
	public SoftwareVersionResponse addSoftwareVersion(SoftwareVersionAddRequest newSoftware) {
		SoftwareVersion currentSoftwareVersion = softwareVersionRepository
				.findByVerionAndPackageName(newSoftware.getVersion(), newSoftware.getPackageName()).orElse(null);
		if (currentSoftwareVersion != null) {
			throw new ServiceException(ErrorCode.SOFTWARE_VERSION_EXISTED);
		}

		String filePath = newSoftware.getFilePath();
		if (StringUtils.isBlank(filePath)) {
			try {
				filePath = AwsS3StorageServiceImpl.SOFTWARE_FOLDER + "/" + String.valueOf(System.currentTimeMillis())
						+ Utils.generateToken(16);
				validateChecksum(newSoftware.getSoftware().getInputStream(), newSoftware.getChecksum());
				logger.debug("Pass validate checksum");
				logger.debug("file name: " + newSoftware.getSoftware().getOriginalFilename());
				logger.debug("content lenghth: " + newSoftware.getSoftware().getSize());
				logger.debug("contentType: " + newSoftware.getSoftware().getContentType());
				awsS3StorageService.storeFile(filePath, new ByteArrayInputStream(newSoftware.getSoftware().getBytes()),
						newSoftware.getSoftware().getSize(), newSoftware.getSoftware().getContentType());
			} catch (IOException e) {
				logger.error("Upload file to s3 failure: " + e.getMessage());
				throw new ServiceException(ErrorCode.SOFTWARE_VERSION_CAN_NOT_ADD);
			}
		}

		if (!awsS3StorageService.fileExist(filePath)) {
			logger.error("File is not existed in S3");
			throw new ServiceException(ErrorCode.SOFTWARE_VERSION_CAN_NOT_ADD);
		}

		Software software = softwareService.findByPackageId(newSoftware.getPackageName());
		UpdateMode updateMode = updateModeRepository.findById(newSoftware.getUpdateModeId())
				.orElseThrow(() -> new ServiceException(ErrorCode.UPDATE_MODE_NOT_FOUND));

		SoftwareVersion newSoftwareVersion = new SoftwareVersion(newSoftware, software, updateMode);
		newSoftwareVersion.setFilePath(filePath);

		entityManager.persist(newSoftwareVersion);

		Map<String, String> metadata = new HashMap<>();
		metadata.put("software_name", software.getName());
		metadata.put("software_package_name", software.getPackageName());
		metadata.put("software_id", software.getId() + "");
		metadata.put("version", newSoftwareVersion.getVersion());
		metadata.put("version_id", newSoftwareVersion.getId() + "");
		metadata.put("checksum", newSoftwareVersion.getChecksum());

		awsS3StorageService.updateMetadata(filePath, metadata);

		// push event
		eventQueue.queue(new SoftwareVersionAddedEvent(software.getServicePlatform(),
				new SoftwareVersionAddedEventData(newSoftwareVersion)));

		return new SoftwareVersionResponse(newSoftwareVersion);

	}

	private void validateChecksum(InputStream is, String targetChecksum) {
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");

			byte[] digest = checksum(is, md);
			if (!Arrays.equals(digest, Base64.getDecoder().decode(targetChecksum))) {
				throw new ServiceException(ErrorCode.SOFTWARE_VERSION_INVALID_CHECKSUM);
			}
		} catch (IOException | NoSuchAlgorithmException e) {
			logger.error("Checksum error", e);
			throw new ServiceException(ErrorCode.UNKNOWN);
		}
	}

	private byte[] checksum(InputStream is, MessageDigest md) throws IOException {
		try (InputStream fis = is) {
			byte[] buffer = new byte[1024];
			int nread;
			while ((nread = fis.read(buffer)) != -1) {
				md.update(buffer, 0, nread);
			}
		}

		return md.digest();
	}

	@Override
	@Transactional(readOnly = true)
	public Pagination<SoftwareVersionResponse> findSoftwareVersionByPagination(Long softwareId, String packageName,
			String version, Integer updateModeId, String releaseNote, Long fromTime, Long toTime, String sortBy,
			String order, int page, int pageSize) {
		Utils.validatePagination(page, pageSize);
		Pageable paging = PageRequest.of(page, pageSize,
				Sort.by(order.equalsIgnoreCase("DESC") ? Sort.Direction.DESC : Sort.Direction.ASC, sortBy));

		SoftwareVersionSpecification specs = new SoftwareVersionSpecification(softwareId, packageName, version,
				updateModeId, releaseNote, fromTime, toTime);

		Page<SoftwareVersion> listSoftwareVersion = softwareVersionRepository.findAll(specs, paging);
		Long totalItems = listSoftwareVersion.getTotalElements();

		// Transfer to DTO
		List<SoftwareVersionResponse> listResult = listSoftwareVersion.stream().map(sv -> {
			return new SoftwareVersionResponse(sv);
		}).collect(Collectors.toList());

		return new Pagination<SoftwareVersionResponse>(totalItems, page + 1, pageSize, listResult);
	}

	@Override
	@Transactional(readOnly = true)
	public Set<DevicePortalResponse> listDeviceBySoftwareVersion(Long softwareVersionId) {
		List<Device> listDevice = softwarePackagesRepository.findBySoftwareVersion(softwareVersionId);
		return listDevice.stream().map(d -> {
			return new DevicePortalResponse(d);
		}).collect(Collectors.toSet());
	}

	@Override
	@Transactional(readOnly = true)
	public Set<DevicePortalResponse> listDeviceCanBeAssignedBySoftwareVersion(Long softwareVersionId) {
		SoftwareVersion existingSV = softwareVersionRepository.findById(softwareVersionId)
				.orElseThrow(() -> new ServiceException(ErrorCode.SOFTWARE_VERSION_NOT_FOUND));

		Set<String> models = existingSV.getSoftware().getListNameDeviceModels();
		Integer sp = existingSV.getServicePlatformId();
		logger.debug("Model: {}  --- ServicePlatform: {}", models.size(), sp);
		Set<DevicePortalResponse> listDevices = deviceRepository.findByModelAndSPAndCurrentOTA(models, sp).stream()
				.map(i -> {
					return new DevicePortalResponse(i);
				}).collect(Collectors.toSet());
		return listDevices;
	}

	@Override
	@Transactional(readOnly = true)
	public SoftwareInformation downloadSoftware(String deviceUid, Long softwarePackageId) {
		// Download software
		SoftwareVersion softwareVersion = softwarePackagesRepository.findById(softwarePackageId)
				.orElseThrow(() -> new ServiceException(ErrorCode.SOFTWARE_PACKAGE_NOT_FOUND)).getSoftwareVersion();
		String filePath = softwareVersion.getFilePath();
		try {
			Long timeExpired = System.currentTimeMillis() + timeout;
			URL urlDownload = awsS3StorageService.requestDownloadUrl(filePath, timeout);
			return new SoftwareInformation(softwareVersion, urlDownload.toString(), timeExpired);

		} catch (IOException e) {
			logger.debug("Error for download software: " + e.getMessage());
		}
		throw new ServiceException(ErrorCode.SOFTWARE_CAN_NOT_DOWNLOAD);
	}

	@Override
	public void assignSoftware(AssignSoftware assignSoftware, String assignBy) {
		SoftwareVersion softwareAssign = softwareVersionRepository.findById(assignSoftware.getSoftwareVersionId())
				.orElseThrow(() -> new ServiceException(ErrorCode.SOFTWARE_VERSION_NOT_FOUND));
		if (!softwareAssign.isAssignable()) {
			throw new ServiceException(ErrorCode.SOFTWARE_VERSION_NOT_ASSIGNABLE);
		}
		softwarePackagesService.assignBySoftwareVersion(softwareAssign, assignSoftware.getDeviceUids(), assignBy);
	}

	/**
	 * @param upgradeRequest
	 * @return list software version need update
	 */
	@Override
	public List<SoftwareResponse> ota(UpgradeRequest upgradeRequest) {
		Device existingDevice = deviceRepository.findById(upgradeRequest.getDeviceUid())
				.orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_NOT_FOUND));

		List<SoftwarePackages> versionControls = softwarePackagesRepository
				.findOTAPackagesByDeviceUid(existingDevice.getId());

		// transfer to DTO
		return versionControls.stream().filter(sofp -> sofp.isOTA()).map(vc -> {
			return new SoftwareResponse(vc);
		}).collect(Collectors.toList());
	}

	@Override
	@Transactional
	public SoftwarePackages submitStateOTA(StateOTARequest submitStateOTA) {
		SoftwarePackages existingSPackagesOTA = softwarePackagesRepository
				.findById(submitStateOTA.getSoftwarePackageId())
				.orElseThrow(() -> new ServiceException(ErrorCode.SOFTWARE_PACKAGE_NOT_FOUND));

		existingSPackagesOTA.setState(submitStateOTA.getState());
		existingSPackagesOTA.setRemarks(submitStateOTA.getRemarks());

		if (submitStateOTA.getState() == SoftwarePackages.STATE_8_FAILED) {
			existingSPackagesOTA.setRetryFail(existingSPackagesOTA.getRetryFail() + 1);
			if (existingSPackagesOTA.getRetryFail() >= SoftwarePackages.MAX_FAILED_RETRY) {
				existingSPackagesOTA.setState(SoftwarePackages.STATE_7_CANCELLED);
			}
		} else if (submitStateOTA.getState() == SoftwarePackages.STATE_6_UPDATED
				&& StringUtils.isBlank(submitStateOTA.getRemarks())) {
			existingSPackagesOTA.setRemarks("OTA is successful");

			List<DeviceSoftware> currentSoftwares = new ArrayList<>();
			DeviceSoftware updatedSoftware = new DeviceSoftware();
			updatedSoftware.setPackageName(existingSPackagesOTA.getPackageName());
			updatedSoftware.setSoftwareName(existingSPackagesOTA.getSoftware().getName());
			updatedSoftware.setVersion(existingSPackagesOTA.getVersion());
			currentSoftwares.add(updatedSoftware);

			if (existingSPackagesOTA.getDevice().getCurrentSoftwares() != null) {
				for (DeviceSoftware software : existingSPackagesOTA.getDevice().getCurrentSoftwares()) {
					if (!Objects.equals(software.getPackageName(), updatedSoftware.getPackageName())) {
						currentSoftwares.add(software);
					}
				}
			}
			existingSPackagesOTA.getDevice().setCurrentSoftwares(currentSoftwares);
			deviceRepository.save(existingSPackagesOTA.getDevice());

		}

		eventQueue.queue(new SoftwarePackageUpdatedEvent(existingSPackagesOTA.getDevice().getServicePlatform(),
				new SoftwarePackageUpdatedEventData(existingSPackagesOTA)));
		return existingSPackagesOTA;
	}

	@Override
	public boolean removeSoftwareVersion(Long id) {
		SoftwareVersion existingSofV = softwareVersionRepository.findById(id)
				.orElseThrow(() -> new ServiceException(ErrorCode.SOFTWARE_VERSION_NOT_FOUND));

		Long assignedPackagesCount = softwarePackagesRepository.countBySoftwareVersion(existingSofV);
		if (assignedPackagesCount > 0) {
			existingSofV.setAssignable(false);
			softwareVersionRepository.save(existingSofV);
		} else {
			softwareVersionRepository.delete(existingSofV);
//			try {
//				awsS3StorageService.deleteFile(existingSofV.getFilePath());
//			} catch (IOException e) {
//				logger.error("File path: {}", existingSofV.getFilePath());
//				logger.error("Delete file failure: " + e.getMessage());
//				throw new ServiceException(ErrorCode.SOFTWARE_VERSION_CAN_NOT_REMOVE);
//			}
		}

		eventQueue.queue(new SoftwareVersionRemovedEvent(existingSofV.getServicePlatform(),
				new SoftwareVersionRemovedEventData(id)));
		return true;
	}

	@Override
	public List<SoftwareVersion> listByServicePlatformAndIds(Integer servicePlatformId, List<Long> ids) {
		return softwareVersionRepository.findByServicePlatformIdAndIds(servicePlatformId, ids);
	}

	@Override
	@Transactional
	public SoftwareVersionResponse updateSoftwareVersion(SoftwareVersionUpdateRequest updateRequest) {
		SoftwareVersion existingSofV = softwareVersionRepository.findById(updateRequest.getSoftwareVersionId())
				.orElseThrow(() -> new ServiceException(ErrorCode.SOFTWARE_VERSION_NOT_FOUND));

		existingSofV.setReleaseNote(updateRequest.getReleaseNote());
		return new SoftwareVersionResponse(existingSofV);
	}

}
