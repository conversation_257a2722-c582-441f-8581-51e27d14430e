/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.packages;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.device.DeviceRepository;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.rest.portal.admin.software.ota.history.OTAHistoryResponse;
import com.styl.device.management.rest.portal.admin.software.ota.history.ServicePlatformOtaResponse;
import com.styl.device.management.service.platform.event.publisher.EventQueueHandler;
import com.styl.device.management.service.platform.event.publisher.event.SoftwarePackageUpdatedEvent;
import com.styl.device.management.service.platform.event.publisher.event.data.SoftwarePackageUpdatedEventData;
import com.styl.device.management.utils.Pagination;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * <AUTHOR> Lam
 *
 */
@Service
public class SoftwarePackagesServiceImpl implements SoftwarePackagesService {

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private DeviceRepository deviceRepository;

	@Autowired
	private SoftwarePackagesRepository softwarePackagesRepository;

	@Override
	@Transactional
	public void assignBySoftwareVersion(SoftwareVersion softwareVersion, List<String> deviceUids, String assignBy) {
		// Should use batch in query, check later
		for (String deviceUid : deviceUids) {
			Device device = deviceRepository.findById(deviceUid)
					.orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_NOT_FOUND));
			addSoftwarePackages(device, softwareVersion, SoftwarePackages.STATE_1_ASSIGNED, assignBy);
		}
	}

	public SoftwarePackages addSoftwarePackages(Device device, SoftwareVersion softwareVersion, Integer state,
			String assignBy) {
		if (device.getServicePlatform() == null) {
			throw new ServiceException(ErrorCode.DEVICE_NEED_ASSIGN_SP);
		}
		if (!softwareVersion.getSoftware().getServicePlatform().getId().equals(device.getServicePlatform().getId())) {
			throw new ServiceException(ErrorCode.DEVICE_NEED_ASSIGN_SP);
		}

		// update softwarePackage by deviceUId and softwareId to cancel without software
		Long softwareId = softwareVersion.getSoftware().getId();
		String deviceUid = device.getId();

		String cancellationReason = "Cancelled by assigning new version:" + softwareVersion.getVersion();
		softwarePackagesRepository.updateSoftwarePackageState(deviceUid, softwareId, SoftwarePackages.STATE_7_CANCELLED,
				Arrays.asList(SoftwarePackages.STATE_7_CANCELLED, SoftwarePackages.STATE_6_UPDATED),
				cancellationReason);

		SoftwarePackages softwarePackages = new SoftwarePackages();
		softwarePackages.setDevice(device);
		softwarePackages.setSoftware(softwareVersion.getSoftware());
		softwarePackages.setSoftwareVersion(softwareVersion);
		softwarePackages.setState(state);
		softwarePackages.setAssignedTime(System.currentTimeMillis());
		softwarePackages.setAssignedBy(Optional.ofNullable(assignBy).orElse(""));
		softwarePackages.setUpdateMode(softwareVersion.getUpdateMode().getModeId());
		if (device.getCurrentSoftwares() != null) {
			String appVersion = device.getCurrentSoftwares().stream()
					.filter(d -> d.getPackageName().equals(softwareVersion.getPackageId())).map(i -> i.getVersion())
					.findFirst().orElse(null);
			softwarePackages.setAppVersion(appVersion);
		}

		entityManager.persist(softwarePackages);

		if (device.getServicePlatform() != null) {
			eventQueue.queue(new SoftwarePackageUpdatedEvent(device.getServicePlatform(),
					new SoftwarePackageUpdatedEventData(softwarePackages)));
		}

		return softwarePackages;
	}

	@Override
	public void cancel(String deviceUid, Long softwarePackageId, String cancelledBy) {
		Device device = deviceRepository.findById(deviceUid).orElse(null);
		if (device == null) {
			throw new ServiceException(ErrorCode.DEVICE_NOT_FOUND);
		}

		SoftwarePackages softwarePakage = softwarePackagesRepository.findByIdAndDeviceUId(softwarePackageId, deviceUid)
				.orElse(null);
		if (softwarePakage == null) {
			throw new ServiceException(ErrorCode.SOFTWARE_PACKAGE_NOT_FOUND);
		}
		if (!softwarePakage.getState().equals(SoftwarePackages.STATE_1_ASSIGNED)) {
			throw new ServiceException(ErrorCode.SOFTWARE_PACKAGE_NOT_CANCELABLE);
		}

		softwarePakage.setState(SoftwarePackages.STATE_7_CANCELLED);
		if (StringUtils.isNotBlank(cancelledBy)) {
			softwarePakage.setRemarks("Cancelled by " + cancelledBy);
		}
		softwarePackagesRepository.save(softwarePakage);
	}

	@Override
	public Page<SoftwarePackages> getSoftwarePackages(int page, int pageSize, String sortBy, String orderBy,
			String deviceUId, String deviceModel, Integer servicePlatformId, Long softwareId, List<Integer> otaStates,
			List<Integer> tags, String originalVersion, String assignedVersion, String assignedBy) {

		Specification<SoftwarePackages> spec = new SoftwarePackagesSpecification(deviceUId, deviceModel,
				servicePlatformId, softwareId, otaStates, tags, originalVersion, assignedVersion, assignedBy, sortBy,
				orderBy);
		page = page < 1 ? 1 : page;
		pageSize = pageSize < 1 || pageSize > 100 ? 10 : pageSize;
		Pageable pageable = PageRequest.of(page - 1, pageSize);

		Page<SoftwarePackages> softwarePackages = softwarePackagesRepository.findAll(spec, pageable);
		return softwarePackages;
	}

	@Override
	public Pagination<OTAHistoryResponse> getOtaHistory(String deviceId, String deviceModel, Integer servicePlatformId,
			Long softwareId, List<Integer> otaStates, List<Integer> tags, String originalVersion,
			String assignedVersion, String assignedBy, int page, int pageSize, String sortBy, String orderBy) {

		Page<SoftwarePackages> softwarePackages = getSoftwarePackages(page, pageSize, sortBy, orderBy, deviceId,
				deviceModel, servicePlatformId, softwareId, otaStates, tags, originalVersion, assignedVersion,
				assignedBy);

		List<OTAHistoryResponse> data = softwarePackages.stream().map(softwarePackage -> {
			OTAHistoryResponse otaHistoryResponse = new OTAHistoryResponse();
			ServicePlatform servicePlatform = softwarePackage.getSoftware().getServicePlatform();

			otaHistoryResponse.setId(softwarePackage.getId());
			otaHistoryResponse.setDeviceId(softwarePackage.getDevice().getId());
			otaHistoryResponse.setDeviceModel(softwarePackage.getDevice().getModel().getModel());
			otaHistoryResponse.setServicePlatform(new ServicePlatformOtaResponse(servicePlatform));
			otaHistoryResponse.setSoftwareName(softwarePackage.getSoftware().getName());
			otaHistoryResponse.setOriginalVersion(softwarePackage.getAppVersion());
			otaHistoryResponse.setAssignedVersion(softwarePackage.getVersion());
			otaHistoryResponse.setAssignedBy(softwarePackage.getAssignedBy());
			otaHistoryResponse.setAssignedTime(softwarePackage.getAssignedTime());
			otaHistoryResponse.setOtaState(SoftwarePackages.convertToStateSoftwarePackages(softwarePackage.getState()));
			otaHistoryResponse.setOtaRemarks(softwarePackage.getRemarks());
			otaHistoryResponse.setLastUpdated(softwarePackage.getLastUpdated());
			otaHistoryResponse.setTags(softwarePackage.getDevice().getTags().stream().map(tag -> tag.getName())
					.collect(Collectors.toSet()));

			return otaHistoryResponse;
		}).toList();

		return new Pagination<OTAHistoryResponse>(softwarePackages.getTotalElements(), page, pageSize, data);
	}

}
