/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.tag.assign;

import java.io.Serializable;
import java.util.Objects;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

/**
 * <AUTHOR> Lam
 *
 */
@Embeddable
public class DeviceTagId implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6569973593017173052L;

	@Column(name = "device_id")
	private String deviceId;

	@Column(name = "tag_id")
	private int tagId;

	@Override
	public int hashCode() {
		return deviceId.hashCode() * 31 + tagId;
	}

	@Override
	public boolean equals(Object obj) {
		if (!(obj instanceof DeviceTagId)) {
			return false;
		}
		DeviceTagId anotherId = (DeviceTagId) obj;
		return Objects.equals(this.deviceId, anotherId.deviceId) && Objects.equals(this.tagId, anotherId.tagId);
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public int getTagId() {
		return tagId;
	}

	public void setTagId(int tagId) {
		this.tagId = tagId;
	}

}
