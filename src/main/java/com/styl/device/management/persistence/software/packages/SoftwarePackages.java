/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.packages;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;

import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.version.SoftwareVersion;

/**
 * <AUTHOR> Lam
 *
 *         SoftwarePackages ( include multiple OTA package)
 */
@Entity
@Table(name = "tbl_software_packages")
public class SoftwarePackages {

	// State of OTA: ASSIGNED -> DOWNLOADING -> DOWNLOADED -> VERIFIED -> UPDATING
	// -> UPDATED -> CANCELLED
	// Please read DMS- Device api documentation to understand flow
	public static final int STATE_1_ASSIGNED = 1;
	public static final int STATE_2_DOWNLOADING = 2;
	public static final int STATE_3_DOWNLOADED = 3;
	public static final int STATE_4_VERIFIED = 4;
	public static final int STATE_5_UPDATING = 5;
	public static final int STATE_6_UPDATED = 6;
	public static final int STATE_7_CANCELLED = 7;
	public static final int STATE_8_FAILED = 8;

	public static final int MAX_FAILED_RETRY = 3;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	private Long id;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "device_id", nullable = false)
	private Device device;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "software_id", nullable = false)
	private Software software;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "software_version_id", nullable = false)
	private SoftwareVersion softwareVersion;

	@Column(name = "state", nullable = false)
	private Integer state;

	@Column(name = "assigned_by", nullable = false)
	private String assignedBy;

	@Column(name = "assigned_time", nullable = false)
	private Long assignedTime;

	@Column(name = "update_mode", nullable = true)
	private Integer updateMode;

	@Column(name = "retry_fail")
	private Integer retryFail;

	@Column(name = "remarks")
	private String remarks;

	@Column(name = "app_version")
	private String appVersion;

	@Column(name = "last_updated")
	private Long lastUpdated;

	public SoftwarePackages() {
		super();
	}

	public SoftwarePackages(Device device, Software software, SoftwareVersion softwareVersion, Integer state,
			Integer updateMode) {
		super();
		this.device = device;
		this.software = software;
		this.softwareVersion = softwareVersion;
		this.state = state;
		this.updateMode = updateMode;
	}

	@PreUpdate
	@PrePersist
	public void preUpdatePackage() {
		this.lastUpdated = System.currentTimeMillis();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Device getDevice() {
		return device;
	}

	public void setDevice(Device device) {
		this.device = device;
	}

	public Software getSoftware() {
		return software;
	}

	public void setSoftware(Software software) {
		this.software = software;
	}

	public SoftwareVersion getSoftwareVersion() {
		return softwareVersion;
	}

	public void setSoftwareVersion(SoftwareVersion softwareVersion) {
		this.softwareVersion = softwareVersion;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getAssignedBy() {
		return assignedBy;
	}

	public void setAssignedBy(String assignedBy) {
		this.assignedBy = assignedBy;
	}

	public Long getAssignedTime() {
		return assignedTime;
	}

	public void setAssignedTime(Long assignedTime) {
		this.assignedTime = assignedTime;
	}

	public Integer getUpdateMode() {
		return updateMode;
	}

	public void setUpdateMode(Integer updateMode) {
		this.updateMode = updateMode;
	}

	public Integer getRetryFail() {
		return retryFail == null ? 0 : retryFail;
	}

	public void setRetryFail(Integer retryFail) {
		this.retryFail = retryFail;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public Long getSoftwareVersionId() {
		if (softwareVersion != null) {
			return softwareVersion.getId();
		}
		return null;
	}

	public String getVersion() {
		if (softwareVersion != null) {
			return softwareVersion.getVersion();
		}
		return null;
	}

	public String getPackageName() {
		if (software != null) {
			return software.getPackageName();
		}
		return null;
	}

	public Long getSoftwareId() {
		if (software != null) {
			return software.getId();
		}
		return null;
	}

	public String getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	public Long getLastUpdated() {
		return lastUpdated;
	}

	public void setLastUpdated(Long lastUpdated) {
		this.lastUpdated = lastUpdated;
	}

	/**
	 * OTA when state is assigned and failed ( retry need < 3)
	 * 
	 * @return
	 */
	public boolean isOTA() {
		if (state.intValue() == STATE_1_ASSIGNED || (state.intValue() == STATE_8_FAILED)) {
			return true;
		}
		return false;
	}

	public static String convertToStateSoftwarePackages(int stateId) {
		switch (stateId) {
		case STATE_1_ASSIGNED: {
			return "ASSIGNED";
		}
		case STATE_2_DOWNLOADING: {
			return "DOWNLOADING";
		}
		case STATE_3_DOWNLOADED: {
			return "DOWNLOADED";
		}
		case STATE_4_VERIFIED: {
			return "VERIFIED";
		}
		case STATE_5_UPDATING: {
			return "UPDATING";
		}
		case STATE_6_UPDATED: {
			return "UPDATED";
		}
		case STATE_7_CANCELLED: {
			return "CANCELLED";
		}
		case STATE_8_FAILED: {
			return "FAILED";
		}
		default:
			throw new IllegalArgumentException("Unexpected value: " + stateId);
		}
	}

}
