/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.service.platform;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

import com.styl.device.management.utils.CustomSpecification;

/**
 * <AUTHOR> Lam
 *
 */
public class ServicePlatformSpecification extends CustomSpecification<ServicePlatform> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3088355314805862478L;

	private String name;

	private String shortName;

	private String url;

	private String contactEmail;

	private String contactName;

	private String contactPhone;

	private Long fromTime;
	
	private Long toTime;

	private String caName;
	
	public ServicePlatformSpecification(String name, String shortName, String url, String contactEmail,
			String contactName, String contactPhone, Long fromTime, Long toTime, String caName) {
		super();
		this.name = name;
		this.shortName = shortName;
		this.url = url;
		this.contactEmail = contactEmail;
		this.contactName = contactName;
		this.contactPhone = contactPhone;
		this.fromTime = fromTime;
		this.toTime = toTime;
		this.caName = caName;
	}

	@Override
	public Predicate toPredicate(Root<ServicePlatform> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotBlank(name)) {
			predicates.add(like(cb, root.get("name"), name));
		}

		if (isNotBlank(shortName)) {
			predicates.add(like(cb, root.get("shortName"), shortName));
		}

		if (isNotBlank(url)) {
			predicates.add(like(cb, root.get("url"), url));
		}

		if (isNotBlank(contactName)) {
			predicates.add(like(cb, root.get("contactName"), contactName));
		}

		if (isNotBlank(contactEmail)) {
			predicates.add(like(cb, root.get("contactEmail"), contactEmail));
		}

		if (isNotNull(contactPhone)) {
			predicates.add(like(cb, root.get("contactPhone"), contactPhone));
		}
		
		if (isNotNull(fromTime)) {
			predicates.add(greaterThanOrEqualTo(cb, root.get("createdTime"), fromTime));
		}
		
		if (isNotNull(toTime)) {
			predicates.add(lessThanOrEqualTo(cb, root.get("createdTime"), toTime));
		}

		if (isNotBlank(caName)) {
			predicates.add(equals(cb, root.get("caName"), caName));
		}
		return cb.and(predicates.toArray(new Predicate[predicates.size()]));

	}

}
