/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.tag;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Lam
 *
 */
@Repository
public interface TagRepository extends JpaRepository<Tag, Integer>, JpaSpecificationExecutor<Tag> {

	public Optional<Tag> findByName(String name);

	@Query(value = "SELECT COUNT(*) FROM tbl_tag_assign WHERE tag_id = ?1", nativeQuery = true)
	public Long countTagAssign(int tagId);

	@Query(value = "SELECT t.id as id, t.name as name, t.description as description, COUNT(d) as totalDevices "
			+ "FROM Tag t " + "LEFT JOIN t.devices d "
			+ "WHERE (t.name LIKE '%'||:name||'%' OR :name IS NULL OR :name = '') AND "
			+ "(t.description LIKE '%'||:description||'%' OR :description IS NULL OR :description = '') "
			+ "GROUP BY t.id, t.name, t.description")
	public Page<TagWithCountDevice> getListTagWithCountDevice(String name, String description, Pageable pageable);

	public static interface TagWithCountDevice {

		Integer getId();

		String getName();

		String getDescription();

		Long getTotalDevices();

	}

	@Modifying
	@Query(value = "DELETE FROM tbl_tag_assign t WHERE t.tag_id = ?1", nativeQuery = true)
	public void removeTagAssignToDevice(Integer tagId);

}
