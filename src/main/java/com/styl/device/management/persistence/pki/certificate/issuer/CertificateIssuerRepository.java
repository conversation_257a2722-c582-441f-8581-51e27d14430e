/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.pki.certificate.issuer;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface CertificateIssuerRepository extends JpaRepository<CertificateIssuer, String> {

	/**
	 * @param caName
	 * @param issuerName
	 */
	CertificateIssuer findByCaNameAndIssuerName(String caName, String issuerName);

	/**
	 * @param caName
	 * @param issuerName
	 */
	CertificateIssuer findByIssuerName(String issuerName);

	/**
	 * @param caName
	 * @param issuerId
	 */
	CertificateIssuer findByCaNameAndIssuerId(String caName, String issuerId);

	/**
	 * @param caName
	 * @param of
	 * @return
	 */

	Page<CertificateIssuer> findByCaName(String caName, PageRequest page);
	/**
	 * @param serialNumber
	 * @return
	 */
	Optional<CertificateIssuer> findBySerialNumber(String serialNumber);

	/**
	 * @param objectKey
	 * @return
	 */
	Optional<CertificateIssuer> findByObjectKey(String objectKey);

}
