/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.pki.certificate;

import java.security.cert.X509Certificate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "tbl_certificate")
public class Certificate {

	@Id
	@Column(name = "serial_number", unique = true, nullable = false, length = 60)
	private String serialNumber;

	@Column(name = "common_name", length = 200)
	private String commonName;

	@Column(name = "ca_name", length = 100)
	private String caName;

	@Column(name = "issued_time", nullable = false)
	private long issuedTime;

	@Column(name = "expiry_time", nullable = false)
	private long expiryTime;

	@Column(name = "issuer_id", length = 36)
	private String issuerId;

	@Column(name = "issuer_name", length = 200)
	private String issuerName;

	// predecessor of the certificate
	@Column(name = "predecessor", length = 60)
	private String predecessor;

	@Column(name = "revoked_time")
	private Long revokedTime;

	@JsonIgnore
	@Column(name = "certificate", length = 2048)
	private String certificate;

	public Certificate() {
		super();
	}

	/**
	 * @param vaultCert
	 */
	public Certificate(String caName, String issuerId, org.springframework.vault.support.Certificate vaultCert) {
		this(caName, issuerId, null, vaultCert);
	}

	/**
	 * @param vaultCert
	 */
	public Certificate(String caName, String issuerId, String predecessor,
			org.springframework.vault.support.Certificate vaultCert) {
		X509Certificate x509Cert = vaultCert.getX509Certificate();

		String commonName = x509Cert.getSubjectX500Principal().getName();
		Pattern commonNamePattern = Pattern.compile("^CN\\=(.+)$");
		Matcher commonNameMatcher = commonNamePattern.matcher(commonName);
		if (commonNameMatcher.find()) {
			commonName = commonNameMatcher.group(1);
		}
		this.serialNumber = vaultCert.getSerialNumber();
		this.commonName = commonName;
		this.caName = caName;
		this.issuedTime = x509Cert.getNotBefore().getTime();
		this.expiryTime = x509Cert.getNotAfter().getTime();
		this.issuerId = issuerId;
		this.predecessor = predecessor;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getCommonName() {
		return commonName;
	}

	public void setCommonName(String commonName) {
		this.commonName = commonName;
	}

	public String getCaName() {
		return caName;
	}

	public void setCaName(String caName) {
		this.caName = caName;
	}

	public long getIssuedTime() {
		return issuedTime;
	}

	public void setIssuedTime(long issuedTime) {
		this.issuedTime = issuedTime;
	}

	public long getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(long expiryTime) {
		this.expiryTime = expiryTime;
	}

	public String getIssuerId() {
		return issuerId;
	}

	public void setIssuerId(String issuerId) {
		this.issuerId = issuerId;
	}

	public String getPredecessor() {
		return predecessor;
	}

	public void setPredecessor(String predecessor) {
		this.predecessor = predecessor;
	}

	public Long getRevokedTime() {
		return revokedTime;
	}

	public void setRevokedTime(Long revokedTime) {
		this.revokedTime = revokedTime;
	}

	public boolean isRevoked() {
		return this.revokedTime != null;
	}

	public String getIssuerName() {
		return issuerName;
	}

	public void setIssuerName(String issuerName) {
		this.issuerName = issuerName;
	}

	public String getCertificate() {
		return certificate;
	}

	public void setCertificate(String certificate) {
		this.certificate = certificate;
	}

}