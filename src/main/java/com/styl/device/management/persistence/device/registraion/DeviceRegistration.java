/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device.registraion;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import com.styl.device.management.rest.device.DeviceRegister;

/**
 * <AUTHOR> Lam
 *
 */
@Entity
@Table(name = "tbl_registration_device", uniqueConstraints = @UniqueConstraint(columnNames = "challenge"))
public class DeviceRegistration {

	public static final int NO_CHALLENGE_REQUIRED = 0;
	public static final int ACTIVATION_CODE_CHALLENGE = 1;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	private Long id;

	@Column(name = "hardware_id", nullable = false, length = 256)
	private String hardwareId;

	@Column(name = "model", nullable = true, length = 45)
	private String model;

	@Column(name = "simId", nullable = true, length = 64)
	private String simId;

	@Column(name = "imei", nullable = true, length = 64)
	private String imei;

	@Column(name = "challenge_code", nullable = false)
	private Integer challengeCode;

	@Column(name = "challenge", unique = true, nullable = false, length = 8)
	private String challenge;

	@Column(name = "expiry_time")
	private Long expiryTime;

	@Column(name = "updated_time")
	private Long updatedTime;

	@Column(name = "registration_time")
	private Long registrationTime;

	@Column(name = "service_platform_id")
	private Integer servicePlatformId;

	public DeviceRegistration(Long id, String hardwareId, String model, String simId, String imei, String challenge,
			Long expiryTime, Long updatedTime) {
		this.id = id;
		this.hardwareId = hardwareId;
		this.model = model;
		this.simId = simId;
		this.imei = imei;
		this.challenge = challenge;
		this.expiryTime = expiryTime;
		this.updatedTime = updatedTime;
	}

	public DeviceRegistration(DeviceRegister deviceRegister, String challenge, Long expiryTime, Integer challengeCode) {
		this.hardwareId = deviceRegister.getHardwareId();
		this.model = deviceRegister.getModel();
		this.simId = deviceRegister.getSimId();
		this.imei = deviceRegister.getImei();
		this.registrationTime = System.currentTimeMillis();
		this.challenge = challenge;
		this.expiryTime = expiryTime;
		this.challengeCode = challengeCode;
	}

	public DeviceRegistration(String hardwareId, Integer spId) {
		this.hardwareId = hardwareId;
		this.servicePlatformId = spId;
		this.registrationTime = System.currentTimeMillis();
		this.challengeCode = DeviceRegistration.NO_CHALLENGE_REQUIRED;
	}

	public DeviceRegistration() {

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getHardwareId() {
		return hardwareId;
	}

	public void setHardwareId(String hardwareId) {
		this.hardwareId = hardwareId;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getChallenge() {
		return challenge;
	}

	public void setChallenge(String challenge) {
		this.challenge = challenge;
	}

	public Long getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(Long expiryTime) {
		this.expiryTime = expiryTime;
	}

	public String getSimId() {
		return simId;
	}

	public void setSimId(String simId) {
		this.simId = simId;
	}

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	public Long getUpdatedTime() {
		return updatedTime;
	}

	public void setUpdatedTime(Long updatedTime) {
		this.updatedTime = updatedTime;
	}

	public Integer getChallengeCode() {
		return challengeCode;
	}

	public void setChallengeCode(Integer challenge) {
		this.challengeCode = challenge;
	}

	public Long getRegistrationTime() {
		return registrationTime;
	}

	public void setRegistrationTime(Long registrationTime) {
		this.registrationTime = registrationTime;
	}

	public boolean isPassedChallenge() {
		if (this.challengeCode != null) {
			return this.challengeCode.intValue() == NO_CHALLENGE_REQUIRED;
		}
		return false;
	}

	public Integer getServicePlatformId() {
		return servicePlatformId;
	}

	public void setServicePlatformId(Integer servicePlatformId) {
		this.servicePlatformId = servicePlatformId;
	}
}
