/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Lam
 *
 */
@Repository
public interface SoftwareRepository extends JpaRepository<Software, Long>, JpaSpecificationExecutor<Software> {

	@Query("SELECT s FROM Software s WHERE s.id = :id AND s.deleted = false")
	Optional<Software> findById(long id);

	/**
	 * Find packageName
	 * 
	 * @param packageName
	 * @return Optional<Software>
	 */
	@Query("SELECT s FROM Software s WHERE s.packageName = :packageName AND s.deleted = false")
	public Optional<Software> findByPackageName(String packageName);

	/**
	 * @param name
	 * @return
	 */
	@Query("SELECT s FROM Software s WHERE s.name = :name AND s.deleted = false")
	public Optional<Software> findByName(String name);

	/**
	 * @param packageName
	 * @param name
	 * @return
	 */
	@Query("SELECT s FROM Software s WHERE (s.packageName = :packageName OR s.name = :name) AND s.deleted = false")
	List<Software> findByPackageNameOrName(String packageName, String name);

	/**
	 * @param packageName
	 * @param name
	 * @param servicePlatformId
	 * @param pageable
	 * @return
	 */
	@Query("SELECT s FROM Software s LEFT JOIN s.servicePlatform sp WHERE "
			+ "(s.packageName LIKE '%'||:packageName||'%' OR :packageName IS NULL) "
			+ "AND (s.name LIKE '%'||:name||'%' OR :name IS NULL) "
			+ "AND (sp.id = :servicePlatformId OR :servicePlatformId IS NULL)")
	public Page<Software> findList(@Param("packageName") String packageName, @Param("name") String name,
			@Param("servicePlatformId") Integer servicePlatformId, Pageable pageable);

}
