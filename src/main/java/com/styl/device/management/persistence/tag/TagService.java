/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.tag;

import java.util.List;

import com.styl.device.management.persistence.tag.TagRepository.TagWithCountDevice;
import com.styl.device.management.rest.portal.admin.tag.TagAddRequest;
import com.styl.device.management.rest.portal.admin.tag.TagResponse;
import com.styl.device.management.rest.portal.admin.tag.TagUpdateRequest;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
public interface TagService {

	/**
	 * @param tagAdd
	 * @return
	 */
	TagResponse addTag(TagAddRequest tagAdd);

	/**
	 * @param id
	 * @param name
	 * @param description
	 * @param sortBy
	 * @param order
	 * @param page
	 * @param pageSize
	 * @return
	 */
	Pagination<TagWithCountDevice> getPagination(String name, String description, String sortBy, String order, int page,
			int pageSize);

	/**
	 * @param tagUpdate
	 * @return
	 */
	TagResponse updateTag(TagUpdateRequest tagUpdate);

	/**
	 * @param tagId
	 * @return
	 */
	boolean removeTag(int tagId);

	List<TagResponse> findAll();
}
