/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.service.platform;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.service.platform.notification.setting.ServicePlatformNotificationSettingRepository;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformAddRequest;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformPortalResponse;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformUpdateRequest;
import com.styl.device.management.utils.Pagination;
import com.styl.device.management.utils.Utils;

/**
 * <AUTHOR> Lam
 *
 */
@Service
public class ServicePlatformServiceImpl implements ServicePlatformService {

	private static final Logger logger = LoggerFactory.getLogger(ServicePlatformServiceImpl.class);

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private ServicePlatformRepository spRepository;

	@Autowired
	private ServicePlatformNotificationSettingRepository spNotificationSettingRepository;

	@Override
	public ServicePlatform findByServicePlatformId(Integer id) {
		return spRepository.findById(id).orElseThrow(() -> new ServiceException(ErrorCode.SERVICE_PLATFORM_NOT_FOUND));
	}

	@Override
	@Transactional
	public ServicePlatform addServicePlatform(ServicePlatformAddRequest addRequest) {
		List<ServicePlatform> listSP = spRepository.findByNameOrShortName(addRequest.getName(),
				addRequest.getShortName());
		if (listSP.size() > 0) {
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_EXISTED);
		}
		ServicePlatform newSP = new ServicePlatform(addRequest);
		entityManager.persist(newSP);
		return newSP;
	}

	@Override
	@Transactional
	public ServicePlatform updateServicePlatform(ServicePlatformUpdateRequest updateRequest) {
		ServicePlatform existingSP = spRepository.findById(updateRequest.getId())
				.orElseThrow(() -> new ServiceException(ErrorCode.SERVICE_PLATFORM_NOT_FOUND));

		if (!StringUtils.equals(updateRequest.getName(), existingSP.getName())) {
			spRepository.findByName(updateRequest.getName()).ifPresent(sp -> {
				throw new ServiceException(ErrorCode.SERVICE_PLATFORM_NAME_EXISTED);
			});
		}

		if (!StringUtils.equals(updateRequest.getShortName(), existingSP.getShortName())) {
			spRepository.findByShortName(updateRequest.getShortName()).ifPresent(sp -> {
				throw new ServiceException(ErrorCode.SERVICE_PLATFORM_SHORTNAME_EXISTED);
			});
		}

		existingSP.setUrl(updateRequest.getUrl());
		existingSP.setName(updateRequest.getName());
		existingSP.setShortName(updateRequest.getShortName());
		existingSP.setContactName(updateRequest.getNameContact());
		existingSP.setContactEmail(updateRequest.getEmailContact());
		existingSP.setContactPhone(updateRequest.getPhoneContact());
		existingSP.setCaName(updateRequest.getCaName());
		existingSP.setDescription(updateRequest.getDescription());
		existingSP.setUpdatedBy("");
		existingSP.setUpdatedTime(System.currentTimeMillis());
		return existingSP;
	}

	@Override
	public Pagination<ServicePlatformPortalResponse> listServicePlatform(String shortName, String name, String url,
			String contactName, String contactEmail, String contactPhone, Long fromTime, Long toTime, String caName,
			String sortBy, String order, int page, int pageSize) {
		Utils.validatePagination(page, pageSize);
		Pageable paging = PageRequest.of(page, pageSize, Sort
				.by(StringUtils.equalsIgnoreCase(order, "DESC") ? Sort.Direction.DESC : Sort.Direction.ASC, sortBy));

		ServicePlatformSpecification specs = new ServicePlatformSpecification(name, shortName, url, contactEmail,
				contactName, contactPhone, fromTime, toTime, caName);

		Page<ServicePlatform> listSp = spRepository.findAll(specs, paging);
		Long totalItems = listSp.getTotalElements();

		List<ServicePlatformPortalResponse> listResult = listSp.stream().map(sp -> {
			return new ServicePlatformPortalResponse(sp);
		}).collect(Collectors.toList());
		return new Pagination<ServicePlatformPortalResponse>(totalItems, page + 1, pageSize, listResult);
	}

	@Override
	public boolean removeServicePlatform(Integer id) {
		ServicePlatform existingSP = spRepository.findById(id)
				.orElseThrow(() -> new ServiceException(ErrorCode.SERVICE_PLATFORM_NOT_FOUND));
		try {
			if (spNotificationSettingRepository.existsById(id)) {
				spNotificationSettingRepository.deleteById(id);
			}
			spRepository.delete(existingSP);
		} catch (Exception e) {
			logger.error("Remove ServicePlatform: {}", e.getMessage());
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_CAN_NOT_REMOVE);
		}
		return true;
	}

	@Override
	public List<ServicePlatformPortalResponse> listAllSP() {
		return spRepository.findAll().stream().map(sp -> {
			return new ServicePlatformPortalResponse(sp);
		}).collect(Collectors.toList());
	}

	@Override
	public String getWebhookUrlByServicePlatformId(Integer servicePlatformId) {
		return findByServicePlatformId(servicePlatformId).getUrl();
	}

	@Override
	public ServicePlatform findByShortName(String shortName) {
		return spRepository.findByShortName(shortName).orElse(null);
	}
}
