/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.version;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.styl.device.management.persistence.software.Software;

/**
 * <AUTHOR> Lam
 *
 */
@Repository
public interface SoftwareVersionRepository
		extends JpaRepository<SoftwareVersion, Long>, JpaSpecificationExecutor<SoftwareVersion> {

	@Query("Select sv From SoftwareVersion sv JOIN sv.software s WHERE sv.version = ?1 AND s.packageName = ?2")
	public Optional<SoftwareVersion> findByVerionAndPackageName(String version, String packageName);

	@Query("Select sv From SoftwareVersion sv JOIN sv.software s LEFT JOIN s.servicePlatform sp WHERE "
			+ "(s.packageName = ?1 OR ?1 is null) " + "AND (sv.version LIKE '%'||?2||'%'OR sv.version is null) "
			+ "AND (?3 is null OR sp.id = ?3) ")
	public Page<SoftwareVersion> findByPackageIdAndVersionAndServicePlatform(String packageName, String version,
			Integer servicePlatform, Pageable pageable);

	@Query("Select count(sv.id) From SoftwareVersion sv JOIN sv.software s WHERE s.packageName = ?1")
	public Long countByPackageName(String packageName);

	public Long countBySoftware(Software software);

	@Query("SELECT sv From SoftwareVersion sv JOIN sv.software s WHERE sv.id IN :ids and s.servicePlatform.id=:servicePlatformId")
	List<SoftwareVersion> findByServicePlatformIdAndIds(Integer servicePlatformId, List<Long> ids);

}
