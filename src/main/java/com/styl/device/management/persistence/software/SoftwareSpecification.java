/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.persistence.software;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;

import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.software.model.SoftwareModel;
import com.styl.device.management.utils.CustomSpecification;

/**
 * <AUTHOR> Yee
 *
 */
public class SoftwareSpecification extends CustomSpecification<Software> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5042978734087931834L;

	private Long id;
	private Integer servicePlatformId;
	private String name;
	private String description;
	private String packageName;
	private Set<String> deviceModels;

	public SoftwareSpecification(Long id, Integer servicePlatformId, String name, String description,
			String packageName, Set<String> deviceModels) {
		this.id = id;
		this.servicePlatformId = servicePlatformId;
		this.name = name;
		this.description = description;
		this.packageName = packageName;
		this.deviceModels = deviceModels;
	}

	@Override
	public Predicate toPredicate(Root<Software> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
		List<Predicate> predicates = new ArrayList<>();
		if (isNotNull(id)) {
			predicates.add(equals(cb, root.get("id"), id));
		}

		if (isNotNull(servicePlatformId)) {
			Join<Device, ServicePlatform> servicePlatform = root.join("servicePlatform");
			predicates.add(equals(cb, servicePlatform.get("id"), servicePlatformId));
		}

		if (isNotBlank(name)) {
			predicates.add(like(cb, root.get("name"), name));
		}

		if (isNotBlank(description)) {
			predicates.add(like(cb, root.get("description"), description));
		}

		if (isNotBlank(packageName)) {
			predicates.add(like(cb, root.get("packageName"), packageName));
		}

		if (isNotNull(deviceModels) && !deviceModels.isEmpty()) {
			Subquery<Long> subqueryFindSoftwareId = query.subquery(Long.class);
			Root<SoftwareModel> softwareAssignDeviceModel = subqueryFindSoftwareId.from(SoftwareModel.class);
			subqueryFindSoftwareId.select(softwareAssignDeviceModel.get("software").get("id"));
			subqueryFindSoftwareId.where(softwareAssignDeviceModel.get("deviceModel").get("model").in(deviceModels));
			subqueryFindSoftwareId.groupBy(softwareAssignDeviceModel.get("software"));
			predicates.add(cb.in(root.get("id")).value(subqueryFindSoftwareId));
		}

		predicates.add(equals(cb, root.get("deleted"), false));

		return cb.and(predicates.toArray(new Predicate[predicates.size()]));
	}
}
