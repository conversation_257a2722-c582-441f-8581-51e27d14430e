/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.pki.certificate;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR>
 *
 */
public interface CertificateRepository
		extends JpaRepository<Certificate, String>, JpaSpecificationExecutor<Certificate> {

	/**
	 * @param caName
	 * @param of
	 * @return
	 */
	Page<Certificate> findByCaName(String caName, PageRequest page);

	/**
	 * @param caName
	 * @param serialNumber
	 * @return
	 */
	Certificate findByCaNameAndSerialNumber(String caName, String serialNumber);

}
