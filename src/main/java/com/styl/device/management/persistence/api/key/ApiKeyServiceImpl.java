/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.persistence.api.key;

import java.security.NoSuchAlgorithmException;
import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.utils.CryptoUtils;

/**
 * <AUTHOR> Yee
 *
 */
@Service
public class ApiKeyServiceImpl implements ApiKeyService {

	private static final Logger logger = LoggerFactory.getLogger(ApiKeyServiceImpl.class);

	@Autowired
	private ApiKeyRepository apiKeyRepository;

	@PersistenceContext
	private EntityManager entityManager;

	private static final int GENERATE_ATTEMPT = 10;

	@Override
	public ApiKey getByApiKey(String apiKey) {
		return apiKeyRepository.findKeyByApiKey(apiKey);
	}

	@Override
	@Transactional
	public ApiKey addApiKey(Integer servicePlatformId, Long expiredTime, String userId) {
		if (expiredTime == null || (expiredTime >= 0 && expiredTime <= System.currentTimeMillis())) {
			throw new ServiceException(ErrorCode.API_KEY_EXPIRED);
		}
		String randomIdForApiKey = null;
		String randomIdForSecretKey = null;
		int apiKeyGenCount = 0;
		int secretKeyGenCount = 0;
		try {
			randomIdForApiKey = CryptoUtils.generateKey(64);
			while (apiKeyRepository.findKeyByApiKey(randomIdForApiKey) != null && apiKeyGenCount < GENERATE_ATTEMPT) {
				apiKeyGenCount++;
				randomIdForApiKey = CryptoUtils.generateKey(64);
			}
			randomIdForSecretKey = CryptoUtils.generateKey(128);
			while (apiKeyRepository.findKeyBySecretKey(randomIdForSecretKey) != null
					&& secretKeyGenCount < GENERATE_ATTEMPT) {
				secretKeyGenCount++;
				randomIdForSecretKey = CryptoUtils.generateKey(128);
			}
		} catch (NoSuchAlgorithmException ex) {
			logger.error("Failed to generate API Key", ex);
			throw new ServiceException(ErrorCode.API_KEY_CANNOT_BE_GENERATE);
		}
		if (randomIdForApiKey == null || randomIdForSecretKey == null) {
			logger.error("Failed to generate API Key");
			throw new ServiceException(ErrorCode.API_KEY_CANNOT_BE_GENERATE);
		}
		ApiKey apiKey = new ApiKey(servicePlatformId, randomIdForApiKey, randomIdForSecretKey, expiredTime, userId);
		entityManager.persist(apiKey);
		entityManager.flush();
		return apiKey;
	}

	@Override
	@Transactional
	public void deleteApiKey(Integer servicePlatformId, Long id) {
		ApiKey apiKey = apiKeyRepository.findKeyByServicePlatformIdAndId(servicePlatformId, id);
		if (apiKey == null) {
			throw new ServiceException(ErrorCode.API_KEY_NOT_FOUND);
		}
		entityManager.remove(apiKey);
	}

	@Override
	public ApiKey getLatestApiKeyByServicePlatformShortName(String spShortName) {
		List<ApiKey> apiKeys = apiKeyRepository.findKeyByServicePlatformShortName(spShortName);
		if (apiKeys != null && !apiKeys.isEmpty() && apiKeys.get(0).getExpiredTime() != null
				&& apiKeys.get(0).getExpiredTime() > System.currentTimeMillis()) {
			return apiKeys.get(0);
		} else {
			throw new ServiceException(ErrorCode.API_KEY_NOT_FOUND);
		}
	}

}
