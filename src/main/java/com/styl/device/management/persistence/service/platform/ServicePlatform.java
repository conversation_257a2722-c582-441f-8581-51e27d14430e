/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.service.platform;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;

import com.styl.device.management.persistence.api.key.ApiKey;
import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformAddRequest;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformUpdateRequest;

/**
 * <AUTHOR> Lam
 *
 */
@Entity
@Table(name = "tbl_service_platform", uniqueConstraints = { @UniqueConstraint(columnNames = "name"),
		@UniqueConstraint(columnNames = "short_name") })
public class ServicePlatform {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	private Integer id;

	@Column(name = "short_name", unique = true, nullable = false, length = 20)
	private String shortName;

	@Column(name = "name", unique = true, nullable = false, length = 256)
	private String name;

	@Column(name = "url", nullable = false, length = 2083)
	private String url;

	@Column(name = "contact_email", length = 256)
	private String contactEmail;

	@Column(name = "contact_name", length = 45)
	private String contactName;

	@Column(name = "contact_phone", length = 20)
	private String contactPhone;

	@Column(name = "created_time", nullable = false)
	private long createdTime;

	@Column(name = "created_by", nullable = false, length = 45)
	private String createdBy;

	@Column(name = "updated_time")
	private Long updatedTime;

	@Column(name = "updated_by", length = 45)
	private String updatedBy;

	@Column(name = "description", length = 256)
	private String description;

	@Column(name = "ca_name", length = 100)
	private String caName;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "servicePlatform")
	private Set<Software> softwares = new HashSet<Software>(0);

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "servicePlatform")
	private Set<Device> devices = new HashSet<Device>(0);

	@Transient
	private ApiKey apiKey;

	public ServicePlatform() {

	}

	public ServicePlatform(ServicePlatformAddRequest spAdd) {
		this.shortName = spAdd.getShortName();
		this.name = spAdd.getName();
		this.url = spAdd.getUrl();
		this.contactEmail = spAdd.getEmailContact();
		this.contactName = spAdd.getNameContact();
		this.contactPhone = spAdd.getPhoneContact();
		this.description = spAdd.getDescription();
		this.caName = spAdd.getCaName();
		this.createdTime = System.currentTimeMillis();
		this.createdBy = "";
	}

	public ServicePlatform(ServicePlatformUpdateRequest spUpdate) {
		this.id = spUpdate.getId();
		this.shortName = spUpdate.getShortName();
		this.name = spUpdate.getName();
		this.url = spUpdate.getUrl();
		this.contactEmail = spUpdate.getEmailContact();
		this.contactName = spUpdate.getNameContact();
		this.contactPhone = spUpdate.getPhoneContact();
		this.caName = spUpdate.getCaName();
		this.updatedTime = System.currentTimeMillis();
		this.updatedBy = "";
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getContactEmail() {
		return contactEmail;
	}

	public void setContactEmail(String contactEmail) {
		this.contactEmail = contactEmail;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactPhone() {
		return contactPhone;
	}

	public void setContactPhone(String contactPhone) {
		this.contactPhone = contactPhone;
	}

	public long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Long getUpdatedTime() {
		return updatedTime;
	}

	public void setUpdatedTime(Long updatedTime) {
		this.updatedTime = updatedTime;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Set<Software> getSoftwares() {
		return softwares;
	}

	public void setSoftwares(Set<Software> softwares) {
		this.softwares = softwares;
	}

	public Set<Device> getDevices() {
		return devices;
	}

	public void setDevices(Set<Device> devices) {
		this.devices = devices;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public ApiKey getApiKey() {
		return apiKey;
	}

	public void setApiKey(ApiKey apiKey) {
		this.apiKey = apiKey;
	}

	public String getCaName() {
		return caName;
	}

	public void setCaName(String caName) {
		this.caName = caName;
	}

}
