/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.service.platform;

import java.util.List;

import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformAddRequest;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformPortalResponse;
import com.styl.device.management.rest.portal.admin.service.platform.ServicePlatformUpdateRequest;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
public interface ServicePlatformService {

	/**
	 * @param id
	 * @return
	 */
	ServicePlatform findByServicePlatformId(Integer id);

	/**
	 * @param addRequest
	 * @return
	 */
	ServicePlatform addServicePlatform(ServicePlatformAddRequest addRequest);

	/**
	 * @param updateRequest
	 * @return
	 */
	ServicePlatform updateServicePlatform(ServicePlatformUpdateRequest updateRequest);

	/**
	 * @param id
	 * @return
	 */
	boolean removeServicePlatform(Integer id);

	/**
	 * @param shortName
	 * @param name
	 * @param url
	 * @param contactName
	 * @param contactEmail
	 * @param contactPhone
	 * @param sortBy
	 * @param order
	 * @param page
	 * @param pageSize
	 * @return
	 */
	Pagination<ServicePlatformPortalResponse> listServicePlatform(String shortName, String name, String url,
			String contactName, String contactEmail, String contactPhone, Long fromTime, Long toTime, String caName,
			String sortBy, String order, int page, int pageSize);

	List<ServicePlatformPortalResponse> listAllSP();

	String getWebhookUrlByServicePlatformId(Integer servicePlatformId);
	
	ServicePlatform findByShortName(String shortName);

}
