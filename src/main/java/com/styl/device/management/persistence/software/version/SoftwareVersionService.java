/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.version;

import java.util.List;
import java.util.Set;

import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.rest.device.software.SoftwareInformation;
import com.styl.device.management.rest.device.software.SoftwareResponse;
import com.styl.device.management.rest.device.software.StateOTARequest;
import com.styl.device.management.rest.device.software.UpgradeRequest;
import com.styl.device.management.rest.portal.admin.device.DevicePortalResponse;
import com.styl.device.management.rest.portal.admin.software.version.AssignSoftware;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareUrlResponse;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareVersionAddRequest;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareVersionResponse;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareVersionUpdateRequest;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
public interface SoftwareVersionService {

	/**
	 * @param md5checksum
	 * @return URl to upload software
	 */
	SoftwareUrlResponse generateURLUploadSoftware(String md5checksum);

	/**
	 * @param version
	 * @param packageId
	 * @return SoftwareVersion
	 */
	SoftwareVersion findByVersionAndPackageId(String version, String packageId);

	/**
	 * @param deviceUid
	 * @param packageId
	 * @param version
	 * @return
	 */
	SoftwareInformation downloadSoftware(String deviceUid, Long softwarePackageId);

	/**
	 * @param assignSoftware
	 * @param assignBy
	 */
	void assignSoftware(AssignSoftware assignSoftware, String assignBy);

	/**
	 * @param upgradeRequest
	 * @return list software version need update
	 */
	List<SoftwareResponse> ota(UpgradeRequest upgradeRequest);

	/**
	 * @param submitStateOTA
	 * @return
	 */
	SoftwarePackages submitStateOTA(StateOTARequest submitStateOTA);

	/**
	 * @param softwareVersionId
	 * @return
	 */
	Set<DevicePortalResponse> listDeviceBySoftwareVersion(Long softwareVersionId);

	/**
	 * @param softwareId
	 * @param packageId
	 * @param version
	 * @param updateModeId
	 * @param releaseNote
	 * @param fromTime
	 * @param toTime
	 * @param sortBy
	 * @param order
	 * @param page
	 * @param pageSize
	 * @return
	 */
	Pagination<SoftwareVersionResponse> findSoftwareVersionByPagination(Long softwareId, String packageId,
			String version, Integer updateModeId, String releaseNote, Long fromTime, Long toTime, String sortBy,
			String order, int page, int pageSize);

	boolean removeSoftwareVersion(Long id);

	Set<DevicePortalResponse> listDeviceCanBeAssignedBySoftwareVersion(Long softwareVersionId);

	List<SoftwareVersion> listByServicePlatformAndIds(Integer servicePlatformId, List<Long> ids);

	SoftwareVersionResponse addSoftwareVersion(SoftwareVersionAddRequest newSoftware);

	SoftwareVersionResponse updateSoftwareVersion(SoftwareVersionUpdateRequest updateRequest);

}
