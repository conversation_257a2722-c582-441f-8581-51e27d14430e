/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.packages;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.version.SoftwareVersion;

/**
 * <AUTHOR> Lam
 *
 */
@Repository
public interface SoftwarePackagesRepository
		extends JpaRepository<SoftwarePackages, Long>, JpaSpecificationExecutor<SoftwarePackages> {

	public Long countBySoftware(Software software);

	public Long countBySoftwareVersion(SoftwareVersion softwareVerison);

	@Query(value = "SELECT * FROM tbl_software_packages v WHERE v.id = ?1 AND v.device_id = ?2", nativeQuery = true)
	public Optional<SoftwarePackages> findByIdAndDeviceUId(Long id, String deviceUid);

	@Query("SELECT d FROM SoftwarePackages v JOIN v.device d JOIN v.softwareVersion sv WHERE sv.id = ?1 AND v.state < "
			+ SoftwarePackages.STATE_7_CANCELLED)
	public List<Device> findBySoftwareVersion(Long softwareVersionId);

	@Query("SELECT sp FROM SoftwarePackages sp JOIN sp.device d WHERE d.id = :deviceUid ORDER BY sp.assignedTime DESC")
	public Page<SoftwarePackages> findByDeviceUid(String deviceUid, Pageable pageable);

	/**
	 * <p>
	 * Use to find last software assigned or failed ( with device OTA api) <br>
	 * Not return OTA for device when state > assigned or < cancel <br>
	 * We will be find list softwarePackages by state ASSINGED and state FAILED (
	 * retry need < 3) <br>
	 * <p>
	 * 
	 * @param deviceId
	 * @return
	 */
	@Query("SELECT spk FROM SoftwarePackages spk JOIN spk.device d WHERE d.id = :deviceUid AND (spk.state = "
			+ SoftwarePackages.STATE_1_ASSIGNED + " OR spk.state = " + SoftwarePackages.STATE_8_FAILED
			+ ") ORDER BY spk.assignedTime ASC")
	public List<SoftwarePackages> findOTAPackagesByDeviceUid(String deviceUid);

	@Modifying
	@Query(value = "UPDATE SoftwarePackages swpk SET swpk.remarks = :remarks ,swpk.state = :targetState"
			+ " WHERE swpk.device.id = :deviceUid AND swpk.software IN (SELECT sw FROM Software sw WHERE sw.id = :softwareId) AND swpk.state NOT IN :exceptStates")
	public int updateSoftwarePackageState(String deviceUid, Long softwareId, int targetState,
			List<Integer> exceptStates, String remarks);

	@Query(value = "SELECT swpk FROM SoftwarePackages swpk "
			+ "WHERE swpk.device.id = :deviceUid and swpk.software.id = :softwareId "
			+ "AND swpk.softwareVersion.version = :version AND swpk.state IN :states")
	List<SoftwarePackages> findSoftwarePackage(String deviceUid, Long softwareId, String version, List<Integer> states);

}
