/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.net.URL;
import java.security.SecureRandom;
import java.security.spec.AlgorithmParameterSpec;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.crypto.spec.IvParameterSpec;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.config.aws.s3.AwsS3StorageService;
import com.styl.device.management.config.aws.s3.AwsS3StorageServiceImpl;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.device.model.DeviceModelRepository;
import com.styl.device.management.persistence.device.registraion.DeviceRegistration;
import com.styl.device.management.persistence.device.registraion.DeviceRegistrationRepository;
import com.styl.device.management.persistence.pki.certificate.Certificate;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.service.platform.ServicePlatformService;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.SoftwareService;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.software.packages.SoftwarePackagesRepository;
import com.styl.device.management.persistence.tag.Tag;
import com.styl.device.management.persistence.tag.TagRepository;
import com.styl.device.management.pki.DeviceCertificateRequest;
import com.styl.device.management.pki.PkiService;
import com.styl.device.management.rest.device.DeviceActivationStateResponse;
import com.styl.device.management.rest.device.DeviceCertRequest;
import com.styl.device.management.rest.device.DeviceCertResponse;
import com.styl.device.management.rest.device.DeviceRegister;
import com.styl.device.management.rest.device.DeviceRegisterResponse;
import com.styl.device.management.rest.device.DeviceResponse;
import com.styl.device.management.rest.device.DeviceStartupRequest;
import com.styl.device.management.rest.device.RenewCertificateRequest;
import com.styl.device.management.rest.device.RenewCertificateRespone;
import com.styl.device.management.rest.device.SignCertificateRequest;
import com.styl.device.management.rest.device.SignCertificateResponse;
import com.styl.device.management.rest.device.software.DeviceSoftware;
import com.styl.device.management.rest.portal.admin.device.AllowTransportRequest;
import com.styl.device.management.rest.portal.admin.device.DeviceAssignRequest;
import com.styl.device.management.rest.portal.admin.device.DeviceAssignTagRequest;
import com.styl.device.management.rest.portal.admin.device.DeviceImportRecord;
import com.styl.device.management.rest.portal.admin.device.DevicePendingRegisterResponse;
import com.styl.device.management.rest.portal.admin.device.DevicePortalResponse;
import com.styl.device.management.rest.portal.admin.device.DeviceUnassignRequest;
import com.styl.device.management.rest.portal.admin.device.ExportDeviceResponse;
import com.styl.device.management.rest.portal.admin.device.ForceRenewRequest;
import com.styl.device.management.rest.portal.admin.device.OtaDetailResponse;
import com.styl.device.management.service.platform.event.publisher.EventQueueHandler;
import com.styl.device.management.service.platform.event.publisher.event.DeviceAssignedEvent;
import com.styl.device.management.service.platform.event.publisher.event.DeviceUnassignedEvent;
import com.styl.device.management.service.platform.event.publisher.event.data.DeviceAssignedEventData;
import com.styl.device.management.service.platform.event.publisher.event.data.DeviceUnassignedEventData;
import com.styl.device.management.service.vault.pki.GeneratePrivateResponse;
import com.styl.device.management.service.vault.pki.VaultPkiService;
import com.styl.device.management.utils.AESUtils;
import com.styl.device.management.utils.Pagination;
import com.styl.device.management.utils.RSAUtils;
import com.styl.device.management.utils.Utils;
import com.styl.device.management.utils.excel.ExcelExporter;
import com.styl.device.management.utils.file.FileReader;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * <AUTHOR> Lam
 *
 */
@Service
public class DeviceServiceImpl implements DeviceService {

	private static final Logger logger = LoggerFactory.getLogger(DeviceServiceImpl.class);

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private DeviceRepository deviceRepository;

	@Autowired
	private DeviceModelRepository deviceModelRepository;

	@Autowired
	private SoftwareService softwareService;

	@Autowired
	private SoftwarePackagesRepository sofpackagesRepository;

	@Autowired
	private DeviceRegistrationRepository deviceResReposiroty;

	@Autowired
	private ServicePlatformService spService;

	@Autowired
	private VaultPkiService vaultPkiService;

	@Autowired
	private PkiService pkiService;

	@Autowired
	private TagRepository tagRepository;

	/**
	 * Router of portal to activation device
	 */
	@Value("${com.styl.device.management.persistence.device.portal.url:http://oceans-dev.styl.solutions/management/device/}")
	private String portalUrl;

	@Value("${com.styl.device.management.persistence.device.activation.timeout:86400000}")
	private Long timeExpired;

	@Autowired
	private FileReader fileReader;

	@Autowired
	private AwsS3StorageService awsS3StorageService;

	private SecureRandom random = new SecureRandom();

	@Override
	public DevicePortalResponse findDevice(String deviceUid) {
		Device device = deviceRepository.findById(deviceUid)
				.orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_NOT_FOUND));

		return new DevicePortalResponse(device);
	}

	@Override
	public Pagination<DevicePortalResponse> listDevice(String deviceUid, String hardwareId, String model,
			Integer servicePlatformId, String simId, String imei, Long fromTime, Long toTime, List<Integer> states,
			List<Integer> otaStates, List<Integer> tags, String sortBy, String order, int page, int pageSize) {
		Utils.validatePagination(page, pageSize);
		Pageable paging = PageRequest.of(page, pageSize, Sort
				.by(StringUtils.equalsIgnoreCase(order, "DESC") ? Sort.Direction.DESC : Sort.Direction.ASC, sortBy));

		DeviceSpecification specs = new DeviceSpecification(deviceUid, model, servicePlatformId, hardwareId, simId,
				imei, fromTime, toTime, states, otaStates, tags);
		Page<Device> listDevices = deviceRepository.findAll(specs, paging);

		Long totalItems = listDevices.getTotalElements();

		// transfer to DTO
		List<DevicePortalResponse> listResult = listDevices.stream().map(d -> new DevicePortalResponse(d)).toList();

		return new Pagination<>(totalItems, page + 1, pageSize, listResult);
	}

	/**
	 * Can see detail flow in Device Provisioning documentation
	 */
	@Override
	@Transactional
	public DeviceRegisterResponse selfRegisterDevice(DeviceRegister deviceRegister) {
		Device existingDevice = deviceRepository.findByHardwareId(deviceRegister.getHardwareId()).orElse(null);
		// If device is already registered previously, it will not challenge
		if (existingDevice != null) {
			existingDevice.setSimId(deviceRegister.getSimId());
			existingDevice.setImei(deviceRegister.getImei());
			return new DeviceRegisterResponse(DeviceRegistration.NO_CHALLENGE_REQUIRED);
		}

		DeviceRegistration existingDeviceRegis = deviceResReposiroty.findByHardwareId(deviceRegister.getHardwareId())
				.orElse(null);

		String url = portalUrl;
		logger.debug("Url {}", url);

		if (existingDeviceRegis != null) {
			return responseWithChallenge(existingDeviceRegis.getChallengeCode(), existingDeviceRegis, deviceRegister);
		}

		existingDeviceRegis = new DeviceRegistration(deviceRegister, Utils.generateChallenge(6),
				System.currentTimeMillis() + timeExpired, DeviceRegistration.ACTIVATION_CODE_CHALLENGE);
		entityManager.persist(existingDeviceRegis);

		return new DeviceRegisterResponse(existingDeviceRegis, url, url + existingDeviceRegis.getChallenge());
	}

	/**
	 * <p>
	 * Challenge: <br>
	 * - 0 is not have challenge. <br>
	 * - 1 is activation code challenge. (Need return activation code value in DTO)
	 * <br>
	 * This function will generate response with challenge code (with existing
	 * device registration).
	 * <p>
	 * 
	 * @param challange
	 * @param existingDeviceRegis
	 * @param deviceRegister
	 * @return
	 */
	private DeviceRegisterResponse responseWithChallenge(int challange, DeviceRegistration existingDeviceRegis,
			DeviceRegister deviceRegister) {
		switch (Integer.valueOf(challange)) {
		case 0:
			existingDeviceRegis.setModel(deviceRegister.getModel());
			existingDeviceRegis.setSimId(deviceRegister.getSimId());
			existingDeviceRegis.setImei(deviceRegister.getImei());
			return new DeviceRegisterResponse(DeviceRegistration.NO_CHALLENGE_REQUIRED);
		case 1:
			existingDeviceRegis.setChallenge(Utils.generateChallenge(6));
			existingDeviceRegis.setExpiryTime(System.currentTimeMillis() + timeExpired);
			existingDeviceRegis.setImei(deviceRegister.getImei());
			existingDeviceRegis.setModel(deviceRegister.getModel());
			existingDeviceRegis.setSimId(deviceRegister.getSimId());
			existingDeviceRegis.setUpdatedTime(System.currentTimeMillis());
			existingDeviceRegis.setChallengeCode(DeviceRegistration.ACTIVATION_CODE_CHALLENGE);
			return new DeviceRegisterResponse(existingDeviceRegis, portalUrl,
					portalUrl + existingDeviceRegis.getChallenge());
		default:
			break;
		}
		throw new ServiceException(ErrorCode.DEVICE_CHALLENGE_NOT_FOUND);
	}

	@Override
	@Transactional
	public Device activateDevice(DeviceRegistration existingDeviceRegis) {
		String existingDeviceModel = existingDeviceRegis.getModel();

		if (existingDeviceModel == null) {
			throw new ServiceException(ErrorCode.DEVICE_MODEL_NOT_FOUND);
		}
		DeviceModel deviceModel = deviceModelRepository.findById(existingDeviceModel).orElse(null);
		if (deviceModel == null) {
			deviceModel = new DeviceModel(existingDeviceModel, "");
			entityManager.persist(deviceModel);
		}

		Device newDevice = new Device(existingDeviceRegis, deviceModel);
		newDevice.setState(Device.AVAILABLE);
		entityManager.persist(newDevice);

		if (existingDeviceRegis.getServicePlatformId() != null) {
			assignServicePlatform(newDevice.getId(), existingDeviceRegis.getServicePlatformId());
		}

		entityManager.remove(existingDeviceRegis);

		return newDevice;
	}

	@Override
	@Transactional
	public Device activateDeviceWithChallenge(String activationCode) {
		DeviceRegistration existingRegis = deviceResReposiroty
				.findByChallenge(activationCode, System.currentTimeMillis())
				.orElseThrow(() -> new ServiceException(ErrorCode.INVALID_ACTIVATION_CODE));

		return activateDevice(existingRegis);
	}

	/**
	 * @param hardwareId
	 * @return Device activation Information
	 */
	@Override
	@Transactional
	public DeviceActivationStateResponse deviceActivation(String hardwareId) {
		Device existingDevice = deviceRepository.findByHardwareId(hardwareId).orElse(null);
		if (existingDevice != null) {
			return new DeviceActivationStateResponse(existingDevice, true);
		}
		DeviceRegistration deviceRegistration = deviceResReposiroty.findByHardwareId(hardwareId).orElse(null);

		if (deviceRegistration != null && deviceRegistration.isPassedChallenge()) {
			Device newDevice = activateDevice(deviceRegistration);
			return new DeviceActivationStateResponse(newDevice, true);
		}
		return new DeviceActivationStateResponse(false);
	}

	@Override
	@Transactional
	public DeviceResponse startup(DeviceStartupRequest startupRequest) {
		Device existingDevice = deviceRepository.findById(startupRequest.getDeviceUid())
				.orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_NOT_FOUND));

		if (StringUtils.isNotBlank(startupRequest.getSimId())) {
			existingDevice.setSimId(startupRequest.getSimId());
		}

		if (StringUtils.isNotBlank(startupRequest.getImei())) {
			existingDevice.setImei(startupRequest.getImei());
		}

		List<Integer> pendingStates = Arrays.asList(SoftwarePackages.STATE_1_ASSIGNED,
				SoftwarePackages.STATE_2_DOWNLOADING, SoftwarePackages.STATE_3_DOWNLOADED,
				SoftwarePackages.STATE_4_VERIFIED, SoftwarePackages.STATE_5_UPDATING, SoftwarePackages.STATE_8_FAILED);

		if (startupRequest.getSoftwares() != null) {
			for (DeviceSoftware curentSoftware : startupRequest.getSoftwares()) {
				if (StringUtils.isNotBlank(curentSoftware.getPackageName())) {
					Software software = softwareService.findByPackageId(curentSoftware.getPackageName());
					if (software != null) {
						curentSoftware.setSoftwareName(software.getName());
						List<SoftwarePackages> softwarePackages = sofpackagesRepository.findSoftwarePackage(
								existingDevice.getId(), software.getId(), curentSoftware.getVersion(), pendingStates);
						for (SoftwarePackages softwarePackage : softwarePackages) {
							softwarePackage.setState(SoftwarePackages.STATE_6_UPDATED);
							softwarePackage.setRemarks("Device started-up with this version");
						}
					}
				}
			}
		}

		existingDevice.setCurrentSoftwares(startupRequest.getSoftwares());
		return new DeviceResponse(existingDevice);
	}

	@Override
	@Transactional
	public DevicePendingRegisterResponse registerOnPortal(String hardwareId) {
		Device existingDevice = deviceRepository.findByHardwareId(hardwareId).orElse(null);
		if (existingDevice != null) {
			throw new ServiceException(ErrorCode.DEVICE_ALREADY_EXISTED);
		}
		DeviceRegistration existingDeviceRegis = deviceResReposiroty.findByHardwareId(hardwareId).orElse(null);
		if (existingDeviceRegis != null) {
			existingDeviceRegis.setChallengeCode(DeviceRegistration.NO_CHALLENGE_REQUIRED);
			existingDeviceRegis.setRegistrationTime(System.currentTimeMillis());
			return new DevicePendingRegisterResponse(existingDeviceRegis);
		}
		existingDeviceRegis = new DeviceRegistration();
		existingDeviceRegis.setHardwareId(hardwareId);
		existingDeviceRegis.setChallengeCode(DeviceRegistration.NO_CHALLENGE_REQUIRED);
		existingDeviceRegis.setRegistrationTime(System.currentTimeMillis());
		entityManager.persist(existingDeviceRegis);
		return new DevicePendingRegisterResponse(existingDeviceRegis);
	}

	@Override
	public List<Device> listByServicePlatformAndIds(Integer servicePlatformId, List<String> deviceIds) {
		return deviceRepository.findByServicePlatformIdAndIds(servicePlatformId, deviceIds);
	}

	private void assignServicePlatform(String deviceUid, int servicePlatformId) {
		DeviceAssignRequest assignRequest = new DeviceAssignRequest();
		assignRequest.setServicePlatformId(servicePlatformId);
		assignRequest.setDeviceUids(Arrays.asList(deviceUid));
		assignServicePlatform(assignRequest);
	}

	@Override
	@Transactional
	public void assignServicePlatform(DeviceAssignRequest assignSP) {
		List<String> deviceUids = assignSP.getDeviceUids();
		List<Device> listDeviceUpdate = new ArrayList<>();

		ServicePlatform sp = spService.findByServicePlatformId(assignSP.getServicePlatformId());
		for (String deviceUid : deviceUids) {
			Device existingDevice = deviceRepository.findById(deviceUid)
					.orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_NOT_FOUND));

			if (existingDevice.getServicePlatform() != null) {
				throw new ServiceException(ErrorCode.DEVICE_HAS_BEEN_ASSIGNED_SP);
			}
			existingDevice.setServicePlatform(sp);
			existingDevice.setState(Device.ASSIGNED);
			existingDevice.setAllowTransport(true);
			listDeviceUpdate.add(existingDevice);
			eventQueue.queue(new DeviceAssignedEvent(sp, new DeviceAssignedEventData(existingDevice)));
		}
		deviceRepository.saveAll(listDeviceUpdate);
	}

	@Override
	@Transactional
	public Device unassignServicePlatform(DeviceUnassignRequest request) {
		Device existingDevice = deviceRepository.findById(request.getDeviceUid())
				.orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_NOT_FOUND));
		ServicePlatform servicePlatform = existingDevice.getServicePlatform();
		existingDevice.setServicePlatform(null);
		existingDevice.setState(Device.AVAILABLE);
		existingDevice.setAllowTransport(false);

		eventQueue.queue(
				new DeviceUnassignedEvent(servicePlatform, new DeviceUnassignedEventData(existingDevice.getId())));
		return existingDevice;
	}

	@Override
	@Transactional
	public SignCertificateResponse signCertificate(SignCertificateRequest signCertRequest) {
		Device existingDevice = deviceRepository.findById(signCertRequest.getDeviceUid())
				.orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_NOT_FOUND));

		// validate allow transport
		if (existingDevice.isAllowTransport() == false) {
			throw new ServiceException(ErrorCode.DEVICE_NOT_ALLOW_TRANSPORT);
		}

		ServicePlatform servicePlatform = existingDevice.getServicePlatform();
		if (servicePlatform == null) {
			throw new ServiceException(ErrorCode.DEVICE_NEED_ASSIGN_SP);
		}

		String caName = servicePlatform.getCaName();
		if (StringUtils.isEmpty(caName)) {
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_MISS_CA_NAME);
		}
		// sign certificate
		Certificate response = signNewCert(signCertRequest.getCsr(), signCertRequest.getDeviceUid(), caName);

		String pemCert = vaultPkiService.getPemCertificate(caName, response.getSerialNumber()).getCertificate();
		existingDevice.setAllowTransport(false);
		return new SignCertificateResponse(vaultPkiService.convertPemToDerCertificate(pemCert),
				response.getSerialNumber(), response.getExpiryTime());
	}

	@Override
	public RenewCertificateRespone renewCert(RenewCertificateRequest renewRequest) {
		Device existingDevice = deviceRepository.findById(renewRequest.getDeviceUid())
				.orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_NOT_FOUND));

		// validate existing cert with service-platform
		ServicePlatform servicePlatform = existingDevice.getServicePlatform();
		if (servicePlatform == null) {
			throw new ServiceException(ErrorCode.DEVICE_NEED_ASSIGN_SP);
		}

		String caName = servicePlatform.getCaName();
		if (caName == null) {
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_MISS_CA_NAME);
		}

		Certificate existingCertificate = pkiService.findCertificate(caName, renewRequest.getSerialNumber());
		if (existingCertificate == null) {
			throw new ServiceException(ErrorCode.PKI_CERTIFICATE_NOT_FOUND);
		}

		if (existingCertificate.isRevoked() || existingCertificate.getExpiryTime() < System.currentTimeMillis()) {
			throw new ServiceException(ErrorCode.PKI_CERTIFICATE_INVALID);
		}
		if (!existingCertificate.getCertificate().equalsIgnoreCase(renewRequest.getExistingCertificate())) {
			throw new ServiceException(ErrorCode.PKI_CERTIFICATE_INVALID);
		}

		String csr = renewRequest.getCsr();
		String deviceUid = renewRequest.getDeviceUid();

		if (csr == null) {
			if (existingDevice.getCsr() != null) {
				csr = existingDevice.getCsr();
			}

			if (existingDevice.getCsr() == null) {
				// currently not create csr. Need check
				throw new ServiceException(ErrorCode.PKI_CSR_NOT_FOUND);
			}
		}

		// sign certificate
		Certificate cert = signNewCert(csr, deviceUid, caName);
		String pemCert = vaultPkiService.getPemCertificate(caName, cert.getSerialNumber()).getCertificate();

		// revoke old cert
		pkiService.revokeCertificate(caName, renewRequest.getSerialNumber());

		// Flag-down Force renew
		if (existingDevice.isForceRenew()) {
			existingDevice.setForceRenew(false);
			deviceRepository.save(existingDevice);
		}

		return new RenewCertificateRespone(vaultPkiService.convertPemToDerCertificate(pemCert), cert.getSerialNumber(),
				cert.getExpiryTime());
	}

	private Certificate signNewCert(String csr, String deviceUid, String caName) {
		DeviceCertificateRequest certRequest = new DeviceCertificateRequest();
		certRequest.setCsr(csr);
		certRequest.setCommonName(deviceUid);
		return pkiService.signDeviceCertificate(caName, certRequest);
	}

	@Override
	@Transactional
	public DeviceCertResponse getDeviceCert(DeviceCertRequest request) {
		Device existingDevice = deviceRepository.findById(request.getDeviceUid())
				.orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_NOT_FOUND));
		// validate allow transport
		if (existingDevice.isAllowTransport() == false) {
			throw new ServiceException(ErrorCode.DEVICE_NOT_ALLOW_TRANSPORT);
		}

		ServicePlatform servicePlatform = existingDevice.getServicePlatform();
		if (servicePlatform == null) {
			throw new ServiceException(ErrorCode.DEVICE_NEED_ASSIGN_SP);
		}

		String caName = servicePlatform.getCaName();
		if (caName == null) {
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_MISS_CA_NAME);
		}

		GeneratePrivateResponse generatePrivateKey = vaultPkiService.generatePrivateKey(caName);

		// create new cert
		Certificate cert = signNewCert(generatePrivateKey.getCsr(), existingDevice.getId(), caName);

		String pemCert = vaultPkiService.getPemCertificate(caName, cert.getSerialNumber()).getCertificate();
		String certificateDER = vaultPkiService.convertPemToDerCertificate(pemCert);
		existingDevice.setAllowTransport(false);
		existingDevice.setCsr(certificateDER);

		byte[] bytesIV = new byte[16];
		random.nextBytes(bytesIV); // Random initialization vector
		AlgorithmParameterSpec ivParameterSpec = new IvParameterSpec(bytesIV);
		byte[] aesKey = AESUtils.generateSymmetricKey();

		logger.debug("IV-Para random: {}", Arrays.toString(bytesIV));
		logger.debug("AES key (bytes): {}", aesKey);
		logger.debug("AES key encode: {}", Base64.getEncoder().encodeToString(aesKey));

		String certificateKey = AESUtils.encrypt(generatePrivateKey.getPrivateKey(), aesKey, AESUtils.AES_PKCS5PADDING,
				ivParameterSpec);
		logger.debug("AES encrypt: {}", certificateKey);
		byte[] exchangeKey = new byte[aesKey.length + bytesIV.length];
		System.arraycopy(aesKey, 0, exchangeKey, 0, aesKey.length);
		System.arraycopy(bytesIV, 0, exchangeKey, aesKey.length, bytesIV.length);

		// 32 byte for key - 16 byte for iv
		byte[] exKeyBytes = Base64.getEncoder().encode(exchangeKey);
		String plainText = new String(exKeyBytes);
		String exchangedKey = RSAUtils.encrypt(request.getPublicKey(), plainText, RSAUtils.RSA_OAEPWITHSHA256);
		logger.debug("Exchange Key (RSA): {}", exchangedKey);

		DeviceCertResponse response = new DeviceCertResponse();
		response.setCertificate(certificateDER);
		response.setSerialNumber(cert.getSerialNumber());
		response.setExpiryTime(cert.getExpiryTime());
		response.setCertificateKey(pemCert);
		response.setExchangedKey(exchangedKey);
		response.setCertificateKey(certificateKey);
		return response;
	}

	@Override
	@Transactional
	public boolean assignTag(DeviceAssignTagRequest assignTag) {
		Set<Tag> tags = new HashSet<Tag>();
		for (String tagName : assignTag.getTags()) {
			Tag tag = tagRepository.findByName(tagName).orElse(null);
			if (tag == null) {
				tag = new Tag(tagName, null);
				entityManager.persist(tag);
			}
			tags.add(tag);
		}

		for (String deviceId : assignTag.getDeviceUids()) {
			Device existingDevice = deviceRepository.findById(deviceId).orElse(null);
			if (existingDevice != null) {
				existingDevice.setTags(tags);
			}
		}

		return true;
	}

	@Override
	@Transactional
	public Device forceRenew(ForceRenewRequest request) {
		Device existingDevice = deviceRepository.findById(request.getDeviceUid())
				.orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_NOT_FOUND));
		existingDevice.setForceRenew(request.isForceRenew());
		return existingDevice;
	}

	@Override
	@Transactional
	public Device allowTransport(AllowTransportRequest request) {
		Device existingDevice = deviceRepository.findById(request.getDeviceUid())
				.orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_NOT_FOUND));
		existingDevice.setAllowTransport(request.isAllowTransport());
		return existingDevice;
	}

	@Override
	@Transactional
	public List<DeviceImportRecord> importWhiteListHardwareId(InputStream is, Integer spId)
			throws InstantiationException, IllegalAccessException, IllegalArgumentException, InvocationTargetException,
			NoSuchMethodException, SecurityException, NoSuchFieldException, IOException {

		try {
			List<DeviceImportRecord> devices = fileReader.read(is, DeviceImportRecord.class);
			List<DeviceImportRecord> response = new ArrayList<DeviceImportRecord>();

			for (DeviceImportRecord device : devices) {
				String hardwareId = device.getHardwareId();
				if (Utils.isSpecialCharacterFree(hardwareId)) {
					// If device is already registered previously, it will not challenge, can
					// ignore, because when device check activation, will pass and return existed
					// deviceId
					Device existingDevice = deviceRepository.findByHardwareId(hardwareId).orElse(null);
					if (existingDevice == null) {
						DeviceRegistration existingDeviceRegis = deviceResReposiroty.findByHardwareId(hardwareId)
								.orElse(null);
						if (existingDeviceRegis != null) {
							existingDeviceRegis.setRegistrationTime(System.currentTimeMillis());
							entityManager.merge(existingDeviceRegis);
							response.add(new DeviceImportRecord(existingDeviceRegis.getHardwareId()));
							continue;
						}
						existingDeviceRegis = new DeviceRegistration(hardwareId, spId);
						entityManager.persist(existingDeviceRegis);
						response.add(new DeviceImportRecord(existingDeviceRegis.getHardwareId()));
						continue;
					}

					response.add(new DeviceImportRecord(existingDevice.getHardwareId()));
					continue;
				}

			}

			return response;

		} catch (InstantiationException | IllegalAccessException | IllegalArgumentException | InvocationTargetException
				| NoSuchMethodException | SecurityException | NoSuchFieldException | IOException e) {
			logger.error("Read file error: {}", e.getMessage());
		} finally {
			is.close();
		}
		return new ArrayList<DeviceImportRecord>();
	}

	@Override
	public Pagination<OtaDetailResponse> getOTAHistoryOfDevice(String deviceUid, int page, int pageSize) {
		Page<SoftwarePackages> pages = sofpackagesRepository.findByDeviceUid(deviceUid,
				PageRequest.of(page - 1, pageSize));

		List<OtaDetailResponse> otaHistory = pages.getContent().stream().map(p -> {
			OtaDetailResponse otaDetail = new OtaDetailResponse();
			otaDetail.setSoftwarePackageId(p.getId());
			otaDetail.setSoftwareName(p.getSoftware().getName());
			otaDetail.setAppVersion(p.getAppVersion());
			otaDetail.setPackageName(p.getPackageName());
			otaDetail.setAssignedSofVersion(p.getSoftwareVersion().getVersion());
			otaDetail.setAssignedBy(p.getAssignedBy());
			otaDetail.setAssignedTime(p.getAssignedTime());
			otaDetail.setOtaState(SoftwarePackages.convertToStateSoftwarePackages(p.getState()));
			otaDetail.setOtaRemark(p.getRemarks());
			otaDetail.setLastUpdated(p.getLastUpdated());

			return otaDetail;
		}).collect(Collectors.toList());

		return new Pagination<OtaDetailResponse>(pages.getTotalElements(), page, pageSize, otaHistory);
	}

	@Override
	public ExportDeviceResponse exportDevice(List<String> deviceUids, String deviceUid, String hardwareId, String model,
			Integer servicePlatformId, String simId, String imei, Long fromTime, Long toTime, List<Integer> states,
			List<Integer> otaStates, List<Integer> tags, String sortBy, String order) {

		DeviceSpecification specs = new DeviceSpecification(deviceUid, model, servicePlatformId, hardwareId, simId,
				imei, fromTime, toTime, states, otaStates, tags);
		Sort sort = Sort.by(Direction.fromString(order), sortBy);
		List<Device> listDevices = null;
		if (deviceUids != null && !deviceUids.isEmpty()) {
			listDevices = deviceRepository.findByIdIn(deviceUids);
		} else {
			listDevices = deviceRepository.findAll(specs, sort);
		}
		if (listDevices.isEmpty()) {
			throw new ServiceException(ErrorCode.DEVICE_NOT_FOUND);
		}

		List<DeviceReportRecord> records = listDevices.stream().map(x -> new DeviceReportRecord(x))
				.collect(Collectors.toList());

		ByteArrayOutputStream os = new ByteArrayOutputStream();
		new ExcelExporter().export(os, records, DeviceReportRecord.class, true);

		String filePath = AwsS3StorageServiceImpl.REPORT_FOLDER + "/" + String.valueOf(System.currentTimeMillis())
				+ Utils.generateToken(16) + ".xlsx";
		try {
			awsS3StorageService.storeFile(filePath, new ByteArrayInputStream(os.toByteArray()), os.size(),
					"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

			URL presignUrl = awsS3StorageService.requestDownloadUrl(filePath, 5000);

			return new ExportDeviceResponse(presignUrl.toString());
		} catch (IOException e) {
			throw new ServiceException(ErrorCode.UNKNOWN);
		}

	}
}
