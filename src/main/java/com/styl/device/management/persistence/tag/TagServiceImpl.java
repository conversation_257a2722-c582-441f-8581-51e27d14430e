/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.tag;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.persistence.tag.TagRepository.TagWithCountDevice;
import com.styl.device.management.rest.portal.admin.tag.TagAddRequest;
import com.styl.device.management.rest.portal.admin.tag.TagResponse;
import com.styl.device.management.rest.portal.admin.tag.TagUpdateRequest;
import com.styl.device.management.utils.Pagination;
import com.styl.device.management.utils.Utils;

/**
 * <AUTHOR> Lam
 *
 */
@Service
public class TagServiceImpl implements TagService {

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private TagRepository tagRepository;

	@Override
	@Transactional
	public TagResponse addTag(TagAddRequest tagAdd) {
		Optional<Tag> existingTag = tagRepository.findByName(tagAdd.getName());
		if (existingTag.isPresent()) {
			throw new ServiceException(ErrorCode.TAG_ALREADY_EXISTED);
		}
		Tag newTag = new Tag();
		newTag.setName(tagAdd.getName());
		newTag.setDescription(tagAdd.getDescription());
		entityManager.persist(newTag);
		return new TagResponse(newTag);
	}

	@Override
	@Transactional(readOnly = true)
	public Pagination<TagWithCountDevice> getPagination(String name, String description, String sortBy, String order,
			int page, int pageSize) {
		Utils.validatePagination(page, pageSize);
		Pageable paging = PageRequest.of(page, pageSize, Sort
				.by(StringUtils.equalsIgnoreCase(order, "DESC") ? Sort.Direction.DESC : Sort.Direction.ASC, sortBy));

		Page<TagWithCountDevice> findAll = tagRepository.getListTagWithCountDevice(name, description, paging);

		return new Pagination<TagWithCountDevice>(findAll.getTotalElements(), page + 1, pageSize, findAll.getContent());
	}

	@Override
	@Transactional
	public TagResponse updateTag(TagUpdateRequest tagUpdate) {
		Tag existingTag = tagRepository.findById(tagUpdate.getId())
				.orElseThrow(() -> new ServiceException(ErrorCode.TAG_NOT_FOUND));

		String name = tagUpdate.getName();
		if (!StringUtils.equals(name, existingTag.getName())) {
			Optional<Tag> tagByName = tagRepository.findByName(name);
			if (tagByName.isPresent()) {
				throw new ServiceException(ErrorCode.TAG_NAME_ALREADY_EXISTED);
			}
			existingTag.setName(name);
		}

		existingTag.setDescription(tagUpdate.getDescription());
		return new TagResponse(existingTag);
	}

	@Override
	@Transactional
	public boolean removeTag(int tagId) {
		tagRepository.removeTagAssignToDevice(tagId);
		tagRepository.deleteById(tagId);
		return true;
	}

	@Override
	public List<TagResponse> findAll() {
		return tagRepository.findAll().stream().map(t -> {
			return new TagResponse(t);
		}).collect(Collectors.toList());
	}
}
