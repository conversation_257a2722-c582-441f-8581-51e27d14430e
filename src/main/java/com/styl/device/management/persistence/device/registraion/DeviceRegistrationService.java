/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device.registraion;

import com.styl.device.management.rest.portal.admin.device.DevicePendingRegisterResponse;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
public interface DeviceRegistrationService {

	/**
	 * @param hardwareId
	 * @param model
	 * @param simId
	 * @param imei
	 * @param fromTime
	 * @param toTime
	 * @param sortBy
	 * @param order
	 * @param page
	 * @param pageSize
	 * @return
	 */
	Pagination<DevicePendingRegisterResponse> findDevicePendingRegister(String hardwareId, String model, String simId,
			String imei, Long fromTime, Long toTime, String sortBy, String order, int page, int pageSize);

	boolean deleteDeviceRegister(Long id);

}
