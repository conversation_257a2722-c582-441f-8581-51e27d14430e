/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.version;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

import org.springframework.lang.Nullable;

import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.update.mode.UpdateMode;
import com.styl.device.management.utils.CustomSpecification;

/**
 * <AUTHOR> Lam
 *
 */
public class SoftwareVersionSpecification extends CustomSpecification<SoftwareVersion> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -311037881463555352L;

	private Long softwareId;

	private String packageName;

	private String version;

	private Integer updateModeId;

	private String releaseNote;

	private Long fromTime;

	private Long toTime;

	/**
	 * @param softwareId
	 * @param packageId
	 * @param version
	 * @param updateModeId
	 * @param releaseNote
	 * @param fromTime
	 * @param toTime
	 */
	public SoftwareVersionSpecification(Long softwareId, String packageName, String version, Integer updateModeId,
			String releaseNote, Long fromTime, Long toTime) {
		this.softwareId = softwareId;
		this.packageName = packageName;
		this.version = version;
		this.updateModeId = updateModeId;
		this.releaseNote = releaseNote;
		this.fromTime = fromTime;
		this.toTime = toTime;
	}

	@Override
	@Nullable
	public Predicate toPredicate(Root<SoftwareVersion> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotNull(softwareId)) {
			Join<SoftwareVersion, Software> software = root.join("software");
			predicates.add(equals(cb, software.get("id"), softwareId));
		}

		if (isNotBlank(packageName)) {
			Join<SoftwareVersion, Software> software = root.join("software");
			predicates.add(like(cb, software.get("packageName"), packageName));
		}

		if (isNotBlank(version)) {
			predicates.add(like(cb, root.get("version"), version));
		}

		if (isNotNull(updateModeId)) {
			Join<SoftwareVersion, UpdateMode> updateMode = root.join("updateMode");
			predicates.add(equals(cb, updateMode.get("modeId"), updateModeId));
		}

		if (isNotBlank(releaseNote)) {
			predicates.add(like(cb, root.get("releaseNote"), releaseNote));
		}

		if (isNotNull(fromTime)) {
			predicates.add(greaterThanOrEqualTo(cb, root.get("createdTime"), fromTime));
		}

		if (isNotNull(toTime)) {
			predicates.add(lessThanOrEqualTo(cb, root.get("createdTime"), toTime));
		}

		return cb.and(predicates.toArray(new Predicate[predicates.size()]));

	}

}
