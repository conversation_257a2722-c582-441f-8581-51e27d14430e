/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.update.mode;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.styl.device.management.persistence.software.version.SoftwareVersion;

/**
 * <AUTHOR> Lam
 *
 */
@Entity
@Table(name = "tbl_update_mode")
public class UpdateMode {
	
	public static final String MANUAL_MODE = "M";
	public static final String STARTUP_MODE = "S";
	public static final String FORCE_MODE = "F";

	@Id
	@Column(name = "id", unique = true, nullable = false)
	private Integer modeId;
	
    @Column(name = "mode", unique = true, nullable = false)
    private String mode;
    
    @Column(name = "description", length = 256)
    private String description;
    
    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "updateMode")
    private Set<SoftwareVersion> softwareVersions = new HashSet<SoftwareVersion>(0);

    public Integer getModeId() {
		return modeId;
	}

	public void setModeId(Integer modeId) {
		this.modeId = modeId;
	}

	public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Set<SoftwareVersion> getSoftwareVersions() {
        return softwareVersions;
    }

    public void setSoftwareVersions(Set<SoftwareVersion> softwareVersions) {
        this.softwareVersions = softwareVersions;
    }
    
}
