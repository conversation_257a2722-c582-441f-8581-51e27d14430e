/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.persistence.api.key;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR> Yee
 *
 */
public interface ApiKeyRepository extends JpaRepository<ApiKey, Long>, JpaSpecificationExecutor<ApiKey> {

	@Query("SELECT k FROM ApiKey k WHERE lower(k.apiKey) = lower(:apiKey)")
	ApiKey findKeyByApiKey(String apiKey);
	
	@Query("SELECT k FROM ApiKey k WHERE lower(k.secretKey) = lower(:secretKey)")
	ApiKey findKeyBySecretKey(String secretKey);
	
	@Query("SELECT k FROM ApiKey k WHERE k.id =:id and k.servicePlatformId =:servicePlatformId")
	ApiKey findKeyByServicePlatformIdAndId(Integer servicePlatformId, Long id);
	
	@Query(value="SELECT * FROM tbl_api_key k join tbl_service_platform sp on sp.id =k.service_platform_id WHERE sp.short_name=:shortName order by k.expired_time desc ",nativeQuery = true)
	List<ApiKey> findKeyByServicePlatformShortName(String shortName);

}
