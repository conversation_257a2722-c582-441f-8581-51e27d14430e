/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device.model;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;

/**
 * <AUTHOR> Lam
 *
 */
@Service
public class DeviceModelServiceImpl implements DeviceModelService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private DeviceModelRepository deviceModelRepository;
    
    /**
     * Find By Model
     * if not exist -> throw service error
     */
    @Override
    public DeviceModel findByModel(String deviceModel) {
        return deviceModelRepository.findById(deviceModel)
                .orElseThrow(() -> new ServiceException(ErrorCode.DEVICE_MODEL_NOT_FOUND));
    }

}
