package com.styl.device.management.persistence.pki.certificate.authority;

import java.util.Optional;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.styl.device.management.rest.portal.admin.pki.ca.TruststoreConfiguration;

@Entity
@Table(name = "tbl_certificate_authority")
public class CertificateAuthority {

	@Id
	@Column(name = "ca_name", unique = true, length = 100)
	private String caName;

	@Column(name = "sync_enabled", nullable = false)
	private boolean autoSync;

	@Column(name = "bucketname", length = 200)
	private String bucket;

	@Column(name = "region", length = 100)
	private String region;

	@Column(name = "access_key_enabled")
	private boolean useAccessKey;

	@Column(name = "access_key_id", length = 200)
	private String accessKeyId;

	@Column(name = "secret_access_key", length = 200)
	private String secretAccessKey;

	public CertificateAuthority() {
		super();
	}

	public CertificateAuthority(String caName, TruststoreConfiguration config) {
		this.caName = caName;
		this.autoSync = config.isAutoSync();
		this.bucket = config.getBucket();
		this.region = config.getRegion();
		this.useAccessKey = Optional.ofNullable(config.getUseAccessKey()).orElse(false);
		this.accessKeyId = config.getAccessKeyId();
		this.secretAccessKey = config.getSecretAccesKey();
	}

	public void setConfig(TruststoreConfiguration config) {
		this.autoSync = config.isAutoSync();
		this.bucket = config.getBucket();
		this.region = config.getRegion();
		this.useAccessKey = config.getUseAccessKey();
		this.accessKeyId = config.getAccessKeyId();
		this.secretAccessKey = config.getSecretAccesKey();
	}

	public String getCaName() {
		return caName;
	}

	public void setCaName(String caName) {
		this.caName = caName;
	}

	public boolean isAutoSync() {
		return autoSync;
	}

	public void setAutoSync(boolean autoSync) {
		this.autoSync = autoSync;
	}

	public String getBucket() {
		return bucket;
	}

	public void setBucket(String bucket) {
		this.bucket = bucket;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public boolean isUseAccessKey() {
		return useAccessKey;
	}

	public void setUseAccessKey(boolean useAccessKey) {
		this.useAccessKey = useAccessKey;
	}

	public String getAccessKeyId() {
		return accessKeyId;
	}

	public void setAccessKeyId(String accessKeyId) {
		this.accessKeyId = accessKeyId;
	}

	public String getSecretAccessKey() {
		return secretAccessKey;
	}

	public void setSecretAccessKey(String secretAccessKey) {
		this.secretAccessKey = secretAccessKey;
	}

}
