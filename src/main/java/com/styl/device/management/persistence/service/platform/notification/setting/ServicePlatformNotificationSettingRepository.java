/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.persistence.service.platform.notification.setting;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface ServicePlatformNotificationSettingRepository
		extends JpaRepository<ServicePlatformNotificationSetting, Integer> {

	public Optional<ServicePlatformNotificationSetting> findById(Integer id);
	
    @Query("SELECT ns FROM ServicePlatformNotificationSetting ns WHERE ns.servicePlatform.shortName =:shortName")
	public ServicePlatformNotificationSetting findByServicePlatformShortName(String shortName);
}
