/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.persistence.service.platform.notification.setting;

import java.util.Collection;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;

import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.ServiceException;
import com.styl.device.management.kafka.KafkaTopicService;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.service.platform.ServicePlatformService;
import com.styl.device.management.rest.portal.admin.service.platform.notification.setting.ServicePlatformNotificationSettingUpdateRequest;
import com.styl.device.management.service.platform.event.publisher.event.common.EventVersion;

/**
 * <AUTHOR> Yee
 *
 */
@Service
public class ServicePlatformNotificationSettingServiceImpl implements ServicePlatformNotificationSettingService {

	private static final Logger logger = LoggerFactory.getLogger(ServicePlatformNotificationSettingServiceImpl.class);

	@Value("${com.styl.device.management.kafka.serviceplatform.event.topic.prefix}")
	private String eventTopicPrefix;

	@Autowired
	private ServicePlatformNotificationSettingRepository spNotificationSettingRepository;

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private ServicePlatformService spService;

	private static final int TOPIC_DUPLICATE_CHECK_ATTEMPT = 5;

	@Override
	public ServicePlatformNotificationSetting findById(Integer id) {
		return spNotificationSettingRepository.findById(id).orElse(new ServicePlatformNotificationSetting());
	}

	@Override
	@Transactional
	public ServicePlatformNotificationSetting manipulate(ServicePlatformNotificationSettingUpdateRequest request) {
		if (!EventVersion.exist(request.getEventVersion())) {
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_NOTIFICATION_SETTING_EVENT_VERSION_NOT_FOUND);
		}
		Pair<ServicePlatformNotificationSetting, Boolean> result = getElseAddNewNotificationSetting(
				request.getServicePlatformId());
		ServicePlatformNotificationSetting ns = result.getLeft();
		ns.setEventVersion(request.getEventVersion());
		ns.setEnabledWebhook(request.getEnabledWebhook() != null ? request.getEnabledWebhook() : false);
		ns.setWebhookUrl(request.getWebhookUrl());
		if (result.getRight()) {
			ns.setUpdatedTime(System.currentTimeMillis());
			ns.setUpdatedBy("");
			entityManager.merge(ns);
		}
		return ns;
	}

	/**
	 * @param servicePlatformId
	 * 
	 * @return ServicePlatform and isExistingItem
	 */
	private Pair<ServicePlatformNotificationSetting, Boolean> getElseAddNewNotificationSetting(
			Integer servicePlatformId) {
		Optional<ServicePlatformNotificationSetting> optionalNS = spNotificationSettingRepository
				.findById(servicePlatformId);
		ServicePlatformNotificationSetting ns = null;
		if (!optionalNS.isPresent()) {
			ns = new ServicePlatformNotificationSetting(servicePlatformId);
			entityManager.persist(ns);
			entityManager.flush();
			return Pair.of(ns, false);
		} else {
			ns = optionalNS.get();
			return Pair.of(ns, true);
		}

	}

	@Override
	@Transactional
	public ServicePlatformNotificationSetting createTopic(Integer id) {
		ServicePlatform sp = spService.findByServicePlatformId(id);
		ServicePlatformNotificationSetting ns = getElseAddNewNotificationSetting(id).getLeft();
		if (ns.getEventTopic() != null) {
			if (isTopicExist(ns.getEventTopic())) {
				return ns;
			}
		}
		ns.setEventTopic(createTopic(eventTopicPrefix, sp.getShortName()));
		return ns;
	}

	private boolean isTopicExist(String eventTopic) {
		try {
			return topicService.isTopicExist(eventTopic);
		} catch (InterruptedException | ExecutionException e) {
			logger.error("Failed to read topic from kafka, exception: " + e.getMessage());
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_TOPIC_READ_FAILED);
		}
	}

	private String createTopic(String eventTopicPrefix, String servicePlatform) {
		String eventTopic = String.format("%s.%s", eventTopicPrefix, servicePlatform);
		try {
			boolean isTopicExist = topicService.isTopicExist(eventTopic);
			int suffix = 1;
			while (isTopicExist && suffix <= TOPIC_DUPLICATE_CHECK_ATTEMPT) {
				eventTopic = String.format("%s.%s.%d", eventTopicPrefix, servicePlatform, suffix);
				isTopicExist = topicService.isTopicExist(eventTopic);
				suffix++;
			}
			if (isTopicExist) {
				logger.error("Failed to create topic in kafka, exceed duplicate checking max attempts: "
						+ TOPIC_DUPLICATE_CHECK_ATTEMPT);
				throw new ServiceException(ErrorCode.SERVICE_PLATFORM_TOPIC_CREATE_FAILED);
			}
			topicService.createTopic(eventTopic);
		} catch (InterruptedException | ExecutionException e) {
			logger.error("Failed to create topic in kafka, exception: " + e.getMessage());
			throw new ServiceException(ErrorCode.SERVICE_PLATFORM_TOPIC_CREATE_FAILED);
		}

		return eventTopic;
	}

	@Override
	public Collection<String> listEventVersion() {
		return EventVersion.getList();
	}

	@Override
	public ServicePlatformNotificationSetting findByServicePlatformShortName(String shortName) {
		return spNotificationSettingRepository.findByServicePlatformShortName(shortName);
	}

}
