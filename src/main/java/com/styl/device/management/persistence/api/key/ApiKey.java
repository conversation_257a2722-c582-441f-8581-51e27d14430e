/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.persistence.api.key;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * <AUTHOR> Yee
 *
 */
@Entity
@Table(name = "tbl_api_key", uniqueConstraints = { @UniqueConstraint(columnNames = "api_key"), @UniqueConstraint(columnNames = "secret_key")})
public class ApiKey {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	private Long id;

	@Column(name = "service_platform_id", nullable = false)
	private Integer servicePlatformId;

	@Column(name = "api_key", nullable = false)
	private String apiKey;

	@Column(name = "secret_key", nullable = false)
	private String secretKey;

	@Column(name = "created_time")
	private long createdTime;

	@Column(name = "created_by", nullable = false, length = 45)
	private String createdBy;

	@Column(name = "expired_time", nullable = false)
	private Long expiredTime;

	@JsonIgnore
	private boolean enabled;

	public ApiKey() {

	}
	
	public ApiKey(Integer servicePlatformId,String apiKey, String secretKey, Long expiredTime,String userId) {
		this.servicePlatformId=servicePlatformId;
		this.apiKey=apiKey;
		this.secretKey=secretKey;
		this.createdBy=userId;
		this.createdTime=System.currentTimeMillis();
		this.expiredTime=expiredTime; 
		this.enabled=true;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getServicePlatformId() {
		return servicePlatformId;
	}

	public void setServicePlatformId(Integer servicePlatformId) {
		this.servicePlatformId = servicePlatformId;
	}

	public String getApiKey() {
		return apiKey;
	}

	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}

	public String getSecretKey() {
		return secretKey;
	}

	public void setSecretKey(String secretKey) {
		this.secretKey = secretKey;
	}

	public long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Long getExpiredTime() {
		return expiredTime;
	}

	public void setExpiredTime(Long expiredTime) {
		this.expiredTime = expiredTime;
	}

	public boolean isEnabled() {
		return enabled;
	}

	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}

}
