/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.hibernate.exception.ConstraintViolationException;
import org.springframework.retry.annotation.Retryable;

import com.styl.device.management.persistence.device.registraion.DeviceRegistration;
import com.styl.device.management.rest.device.DeviceActivationStateResponse;
import com.styl.device.management.rest.device.DeviceCertRequest;
import com.styl.device.management.rest.device.DeviceCertResponse;
import com.styl.device.management.rest.device.DeviceRegister;
import com.styl.device.management.rest.device.DeviceRegisterResponse;
import com.styl.device.management.rest.device.DeviceResponse;
import com.styl.device.management.rest.device.DeviceStartupRequest;
import com.styl.device.management.rest.device.RenewCertificateRequest;
import com.styl.device.management.rest.device.RenewCertificateRespone;
import com.styl.device.management.rest.device.SignCertificateRequest;
import com.styl.device.management.rest.device.SignCertificateResponse;
import com.styl.device.management.rest.portal.admin.device.AllowTransportRequest;
import com.styl.device.management.rest.portal.admin.device.DeviceAssignRequest;
import com.styl.device.management.rest.portal.admin.device.DeviceAssignTagRequest;
import com.styl.device.management.rest.portal.admin.device.DeviceImportRecord;
import com.styl.device.management.rest.portal.admin.device.DevicePendingRegisterResponse;
import com.styl.device.management.rest.portal.admin.device.DevicePortalResponse;
import com.styl.device.management.rest.portal.admin.device.DeviceUnassignRequest;
import com.styl.device.management.rest.portal.admin.device.ExportDeviceResponse;
import com.styl.device.management.rest.portal.admin.device.ForceRenewRequest;
import com.styl.device.management.rest.portal.admin.device.OtaDetailResponse;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
public interface DeviceService {

	/**
	 * @param deviceUid
	 * @param hardwareId
	 * @param model
	 * @param servicePlatformId
	 * @param simId
	 * @param imei
	 * @param fromTime
	 * @param toTime
	 * @param states
	 * @param otaStates
	 * @param tags
	 * @param sortBy
	 * @param order
	 * @param page
	 * @param pageSize
	 * @return
	 */
	Pagination<DevicePortalResponse> listDevice(String deviceUid, String hardwareId, String model,
			Integer servicePlatformId, String simId, String imei, Long fromTime, Long toTime, List<Integer> states,
			List<Integer> otaStates, List<Integer> tags, String sortBy, String order, int page, int pageSize);

	List<Device> listByServicePlatformAndIds(Integer servicePlatformId, List<String> deviceIds);

	/**
	 * <p>
	 * This function device use self register
	 * <p>
	 * 
	 * @param deviceRegister
	 * @return
	 */
	@Retryable(value = ConstraintViolationException.class, maxAttempts = 3)
	DeviceRegisterResponse selfRegisterDevice(DeviceRegister deviceRegister);

	/**
	 * @param challenge
	 * @return
	 */
	Device activateDeviceWithChallenge(String challenge);

	/**
	 * @param startupRequest
	 * @return
	 */
	DeviceResponse startup(DeviceStartupRequest startupRequest);

	/**
	 * @param hardwareId
	 * @return Device activation Information
	 */
	DeviceActivationStateResponse deviceActivation(String hardwareId);

	/**
	 * @param hardwareId
	 * @return
	 */
	DevicePendingRegisterResponse registerOnPortal(String hardwareId);

	/**
	 * @param existingDeviceRegis
	 * @return
	 */
	Device activateDevice(DeviceRegistration existingDeviceRegis);

	void assignServicePlatform(DeviceAssignRequest assignSP);

	Device unassignServicePlatform(DeviceUnassignRequest request);

	SignCertificateResponse signCertificate(SignCertificateRequest signCertRequest);

	DeviceCertResponse getDeviceCert(DeviceCertRequest request);

	RenewCertificateRespone renewCert(RenewCertificateRequest renewRequest);

	boolean assignTag(DeviceAssignTagRequest assignTag);

	Device allowTransport(AllowTransportRequest request);

	Device forceRenew(ForceRenewRequest request);

	List<DeviceImportRecord> importWhiteListHardwareId(InputStream is, Integer spId)
			throws InstantiationException, IllegalAccessException, IllegalArgumentException, InvocationTargetException,
			NoSuchMethodException, SecurityException, NoSuchFieldException, IOException;

	/**
	 * @param deviceUid
	 * @param pageSize
	 * @param page
	 * @return
	 */
	Pagination<OtaDetailResponse> getOTAHistoryOfDevice(String deviceUid, int page, int pageSize);

	/**
	 * @param deviceUid
	 * @return
	 */
	DevicePortalResponse findDevice(String deviceUid);

	/**
	 * @param deviceUids
	 * @param deviceUid
	 * @param hardwareId
	 * @param model
	 * @param servicePlatformId
	 * @param simId
	 * @param imei
	 * @param fromTime
	 * @param toTime
	 * @param states
	 * @param asList
	 * @param tags
	 * @param sortBy
	 * @param order
	 * @return
	 */
	ExportDeviceResponse exportDevice(List<String> deviceUids, String deviceUid, String hardwareId, String model,
			Integer servicePlatformId, String simId, String imei, Long fromTime, Long toTime, List<Integer> states,
			List<Integer> asList, List<Integer> tags, String sortBy, String order);

}
