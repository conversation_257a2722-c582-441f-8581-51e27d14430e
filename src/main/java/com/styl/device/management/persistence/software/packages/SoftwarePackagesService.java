/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.packages;

import java.util.List;

import org.springframework.data.domain.Page;

import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.rest.portal.admin.software.ota.history.OTAHistoryResponse;
import com.styl.device.management.utils.Pagination;

/**
 * <AUTHOR> Lam
 *
 */
public interface SoftwarePackagesService {

	void assignBySoftwareVersion(SoftwareVersion softwareVersion, List<String> deviceUids, String assignBy);

	/**
	 * @param deviceUid
	 * @param softwarePackageId
	 */
	void cancel(String deviceUid, Long softwarePackageId, String cancelledBy);

	Page<SoftwarePackages> getSoftwarePackages(int page, int pageSize, String sortBy, String orderBy, String deviceId,
			String deviceModel, Integer servicePlatformId, Long softwareId, List<Integer> states, List<Integer> tags,
			String originalVersion, String assignedVersion, String assignedBy);
	
	public Pagination<OTAHistoryResponse> getOtaHistory(String deviceId, String deviceModel, Integer servicePlatformId,
			Long softwareId, List<Integer> otaState, List<Integer> tags, String originalVersion, String assignedVersion,
			String assignedBy, int page, int pageSize, String sortBy, String orderBy);
}
