/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;

import com.styl.device.management.utils.file.DataRecord;
import com.styl.device.management.utils.file.RecordHeader;

/**
 * <AUTHOR>
 *
 */
public class DeviceReportRecord implements DataRecord {

	@RecordHeader(name = "Device UID")
	private String id;

	@RecordHeader(name = "Hardware ID")
	private String hardwareId;

	@RecordHeader(name = "Model")
	private String model;

	@RecordHeader(name = "Service Platform")
	private String servicePlatform;

	@RecordHeader(name = "State")
	private String state;

	@RecordHeader(name = "SIM ID")
	private String simId;

	@RecordHeader(name = "IMEI")
	private String imei;

	@RecordHeader(name = "Registration Date")
	private String registrationDate;

	@RecordHeader(name = "Softwares")
	private String currentSoftwares;

	@RecordHeader(name = "Tags")
	private String tags;

	public DeviceReportRecord() {
		super();
	}

	public DeviceReportRecord(Device device) {
		super();
		this.id = device.getId();
		this.hardwareId = device.getHardwareId();
		if (device.getModel() != null) {
			this.model = device.getModel().getModel();
		}
		if (device.getServicePlatform() != null) {
			this.servicePlatform = device.getServicePlatform().getName();
		}
		this.state = Device.getStateString(device.getState());
		this.simId = device.getSimId();
		this.imei = getImei();
		this.registrationDate = Instant.ofEpochMilli(device.getFirstRegistrationTime())
				.atZone(ZoneId.of("Asia/Singapore")).format(DateTimeFormatter.ISO_DATE_TIME);
		if (device.getCurrentSoftwares() != null) {
			this.currentSoftwares = device.getCurrentSoftwares().stream()
					.map(sw -> sw.getSoftwareName() + "-" + sw.getVersion()).collect(Collectors.joining(";"));
		}
		if (device.getTags() != null) {
			this.tags = device.getTags().stream().map(t -> t.getName()).collect(Collectors.joining(","));
		}
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getHardwareId() {
		return hardwareId;
	}

	public void setHardwareId(String hardwareId) {
		this.hardwareId = hardwareId;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getServicePlatform() {
		return servicePlatform;
	}

	public void setServicePlatform(String servicePlatform) {
		this.servicePlatform = servicePlatform;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getSimId() {
		return simId;
	}

	public void setSimId(String simId) {
		this.simId = simId;
	}

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	public String getRegistrationDate() {
		return registrationDate;
	}

	public void setRegistrationDate(String registrationDate) {
		this.registrationDate = registrationDate;
	}

	public String getCurrentSoftwares() {
		return currentSoftwares;
	}

	public void setCurrentSoftwares(String currentSoftwares) {
		this.currentSoftwares = currentSoftwares;
	}

	public String getTags() {
		return tags;
	}

	public void setTags(String tags) {
		this.tags = tags;
	}

}
