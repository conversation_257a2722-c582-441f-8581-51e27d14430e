/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;

import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.tag.assign.TagAssign;
import com.styl.device.management.utils.CustomSpecification;

/**
 * <AUTHOR> Lam
 *
 */
public class DeviceSpecification extends CustomSpecification<Device> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5042978734087931834L;

	private String id;

	private String hardwareId;

	private String model;

	private Integer servicePlatformId;

	private String simId;

	private String imei;

	private Long fromTime;

	private Long toTime;

	private List<Integer> states;

	private List<Integer> otaStates;

	private List<Integer> tags;

	public DeviceSpecification(String id, String model, Integer servicePlatformId, String hardwareId, String simId,
			String imei, Long fromTime, Long toTIme, List<Integer> states, List<Integer> otaStates,
			List<Integer> tags) {
		this.id = id;
		this.model = model;
		this.servicePlatformId = servicePlatformId;
		this.hardwareId = hardwareId;
		this.simId = simId;
		this.imei = imei;
		this.fromTime = fromTime;
		this.toTime = toTIme;
		this.states = states;
		this.otaStates = otaStates;
		this.tags = tags;
	}

	public DeviceSpecification(String id, String model, Integer servicePlatformId, String hardwareId,
			List<Integer> states, String simId, String imei, Long registrationFrom, Long registrationTo) {
		this.id = id;
		this.model = model;
		this.servicePlatformId = servicePlatformId;
		this.hardwareId = hardwareId;
		this.states = states;
		this.simId = simId;
		this.imei = imei;
		this.fromTime = registrationFrom;
		this.toTime = registrationTo;
	}

	@Override
	public Predicate toPredicate(Root<Device> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
		List<Predicate> predicates = new ArrayList<>();
		if (isNotBlank(id)) {
			predicates.add(like(cb, root.get("id"), id));
		}

		if (isNotBlank(hardwareId)) {
			predicates.add(like(cb, root.get("hardwareId"), hardwareId));
		}

		if (isNotBlank(model)) {
			Join<Device, DeviceModel> deviceModel = root.join("model");
			predicates.add(equals(cb, deviceModel.get("model"), model));
		}

		if (isNotNull(servicePlatformId)) {
			Join<Device, ServicePlatform> servicePlatform = root.join("servicePlatform");
			predicates.add(equals(cb, servicePlatform.get("id"), servicePlatformId));
		}

		if (isNotBlank(simId)) {
			predicates.add(like(cb, root.get("simId"), simId));
		}

		if (isNotBlank(imei)) {
			predicates.add(like(cb, root.get("imei"), imei));
		}

		if (isNotNull(fromTime)) {
			predicates.add(greaterThanOrEqualTo(cb, root.get("createdTime"), fromTime));
		}

		if (isNotNull(toTime)) {
			predicates.add(lessThanOrEqualTo(cb, root.get("createdTime"), toTime));
		}

		if (isNotNull(states) && !states.isEmpty()) {
			predicates.add(root.get("state").in(states));
		}

		if (isNotNull(otaStates) && !otaStates.isEmpty()) {
			// query find max id when group by software
			Subquery<Long> subqueryFindMaxId = query.subquery(Long.class);
			Root<SoftwarePackages> sofPackages = subqueryFindMaxId.from(SoftwarePackages.class);
			subqueryFindMaxId.select(cb.max(sofPackages.get("id")));
			subqueryFindMaxId.where(sofPackages.get("state").in(otaStates));
			subqueryFindMaxId.groupBy(sofPackages.get("software"));

			Subquery<String> sq = query.subquery(String.class);
			Root<SoftwarePackages> sofPackageCurrentOTA = sq.from(SoftwarePackages.class);
			sq.select(sofPackageCurrentOTA.get("device").get("id"));
			sq.where(sofPackageCurrentOTA.get("id").in(subqueryFindMaxId));

			predicates.add(cb.in(root.get("id")).value(sq));
		}

		if (isNotNull(tags) && !tags.isEmpty()) {
			Subquery<Long> subqueryFindDeviceId = query.subquery(Long.class);
			Root<TagAssign> tagAssign = subqueryFindDeviceId.from(TagAssign.class);
			subqueryFindDeviceId.select(tagAssign.get("device").get("id"));
			subqueryFindDeviceId.where(tagAssign.get("tag").get("id").in(tags));
			subqueryFindDeviceId.groupBy(tagAssign.get("device"));
			predicates.add(cb.in(root.get("id")).value(subqueryFindDeviceId));
		}

		return cb.and(predicates.toArray(new Predicate[predicates.size()]));
	}

}
