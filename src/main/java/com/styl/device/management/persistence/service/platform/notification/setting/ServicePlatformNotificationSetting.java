/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.service.platform.notification.setting;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;

import com.styl.device.management.persistence.service.platform.ServicePlatform;

/**
 * <AUTHOR> Lam
 *
 */
@Entity
@Table(name = "tbl_service_platform_notification_setting")
public class ServicePlatformNotificationSetting {

	@Id
	@Column(name = "id", unique = true, nullable = false)
	private Integer id;

	@Column(name = "event_topic", length = 256)
	private String eventTopic;

	@Column(name = "event_version", length = 50)
	private String eventVersion;

	@Column(name = "enabled_webhook")
	private boolean enabledWebhook;

	@Column(name = "webhook_url", length = 2083)
	private String webhookUrl;

	@Column(name = "created_time", nullable = false)
	private long createdTime;

	@Column(name = "created_by", nullable = false, length = 45)
	private String createdBy;

	@Column(name = "updated_time")
	private Long updatedTime;

	@Column(name = "updated_by", length = 45)
	private String updatedBy;

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id", nullable = false)
	private ServicePlatform servicePlatform;

	public ServicePlatformNotificationSetting() {

	}

	public ServicePlatformNotificationSetting(int id) {
		this.id = id;
		this.createdTime = System.currentTimeMillis();
		this.createdBy = "";
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getEventTopic() {
		return eventTopic;
	}

	public void setEventTopic(String eventTopic) {
		this.eventTopic = eventTopic;
	}

	public String getEventVersion() {
		return eventVersion;
	}

	public void setEventVersion(String eventVersion) {
		this.eventVersion = eventVersion;
	}

	public boolean isEnabledWebhook() {
		return enabledWebhook;
	}

	public void setEnabledWebhook(boolean enabledWebhook) {
		this.enabledWebhook = enabledWebhook;
	}

	public String getWebhookUrl() {
		return webhookUrl;
	}

	public void setWebhookUrl(String webhookUrl) {
		this.webhookUrl = webhookUrl;
	}

	public long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Long getUpdatedTime() {
		return updatedTime;
	}

	public void setUpdatedTime(Long updatedTime) {
		this.updatedTime = updatedTime;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public ServicePlatform getServicePlatform() {
		return servicePlatform;
	}

	public void setServicePlatform(ServicePlatform servicePlatform) {
		this.servicePlatform = servicePlatform;
	}

}
