/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.model;

import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.software.Software;

import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

/**
 * <AUTHOR> Lam
 *
 */
@Entity
@Table(name = "tbl_software_device_model")
public class SoftwareModel {

	@EmbeddedId
	private SoftwareDeviceModelId id;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "software_id", nullable = false, insertable = false, updatable = false)
	private Software software;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "device_model", nullable = false, insertable = false, updatable = false)
	private DeviceModel deviceModel;

	public SoftwareDeviceModelId getId() {
		return id;
	}

	public void setId(SoftwareDeviceModelId id) {
		this.id = id;
	}

	public Software getSoftware() {
		return software;
	}

	public void setSoftware(Software software) {
		this.software = software;
	}

	public DeviceModel getDeviceModel() {
		return deviceModel;
	}

	public void setDeviceModel(DeviceModel deviceModel) {
		this.deviceModel = deviceModel;
	}
}
