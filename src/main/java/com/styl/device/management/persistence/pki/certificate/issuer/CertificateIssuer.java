/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.pki.certificate.issuer;

import java.security.cert.X509Certificate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;

import org.springframework.vault.support.Certificate;

import com.styl.device.management.service.vault.pki.VaultIssuerResponse;

/**
 * <AUTHOR>
 *
 */

@Entity
@Table(name = "tbl_certificate_issuer")
public class CertificateIssuer {

	@Id
	@Column(name = "issuer_id", unique = true, nullable = false, length = 36)
	private String issuerId;

	@Column(name = "serial_number", unique = true, length = 60)
	private String serialNumber;

	@Column(name = "issuer_name", unique = true, length = 200)
	private String issuerName;

	@Column(name = "ca_name", unique = true, length = 100)
	private String caName;

	@Column(name = "created_time", nullable = false)
	private long createdTime;

	@Column(name = "expiry_time", nullable = false)
	private long expiryTime;

	@Column(name = "revoked_time")
	private Long revokedTime;
	
	@Column(name = "object_key", unique = true)
	private String objectKey;

	@Transient
	private boolean isDefault;

	public CertificateIssuer() {
		super();
	}

	/**
	 * @param caName
	 * @param issuerName
	 * @param vaultIssuer
	 */
	public CertificateIssuer(String caName, String issuerName, VaultIssuerResponse vaultIssuer) {

		X509Certificate x509Cert = Certificate
				.of(vaultIssuer.getSerialNumber(), vaultIssuer.getCertificate(), vaultIssuer.getIssuingCaCertificate())
				.getX509Certificate();

		this.caName = caName;
		this.issuerName = issuerName;
		this.issuerId = vaultIssuer.getIssuerId();
		this.serialNumber = vaultIssuer.getSerialNumber();
		this.createdTime = x509Cert.getNotBefore().getTime();
		this.expiryTime = x509Cert.getNotAfter().getTime();
	}

//	public boolean isUploadedCaTruststore() {
//		return isUploadedCaTruststore;
//	}
//
//	public void setUploadedCaTruststore(boolean isUploadedCaTruststore) {
//		this.isUploadedCaTruststore = isUploadedCaTruststore;
//	}
//
//	public boolean isUploadedSystemTruststore() {
//		return isUploadedSystemTruststore;
//	}
//
//	public void setUploadedSystemTruststore(boolean isUploadedSystemTruststore) {
//		this.isUploadedSystemTruststore = isUploadedSystemTruststore;
//	}

	public String getIssuerId() {
		return issuerId;
	}

	public void setIssuerId(String issuerId) {
		this.issuerId = issuerId;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getIssuerName() {
		return issuerName;
	}

	public void setIssuerName(String issuerName) {
		this.issuerName = issuerName;
	}

	public String getCaName() {
		return caName;
	}

	public void setCaName(String caName) {
		this.caName = caName;
	}

	public long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(long createdTime) {
		this.createdTime = createdTime;
	}

	public long getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(long expiryTime) {
		this.expiryTime = expiryTime;
	}

	public boolean isDefault() {
		return isDefault;
	}

	public void setDefault(boolean isDefault) {
		this.isDefault = isDefault;
	}

	public Long getRevokedTime() {
		return revokedTime;
	}

	public void setRevokedTime(Long revokedTime) {
		this.revokedTime = revokedTime;
	}

	public boolean isRevoked() {
		return this.revokedTime != null;
	}

	public String getObjectKey() {
		return objectKey;
	}

	public void setObjectKey(String objectKey) {
		this.objectKey = objectKey;
	}
}
