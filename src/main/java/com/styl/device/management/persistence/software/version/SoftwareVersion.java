/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software.version;

import java.util.Base64;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import org.apache.commons.lang3.StringUtils;

import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.software.Software;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.software.update.mode.UpdateMode;
import com.styl.device.management.rest.portal.admin.software.version.SoftwareVersionAddRequest;
import com.styl.device.management.utils.CryptoUtils;

/**
 * <AUTHOR> Lam
 *
 */
@Entity
@Table(name = "tbl_software_version", uniqueConstraints = @UniqueConstraint(columnNames = { "software_id", "version" }))
public class SoftwareVersion {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	private Long id;

	@Column(name = "version", nullable = false, length = 45)
	private String version;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "software_id", nullable = false)
	private Software software;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "update_mode", nullable = false)
	private UpdateMode updateMode;

	@Column(name = "file_path", nullable = false, length = 256)
	private String filePath;

	@Column(name = "software_size")
	private Long softwareSize;

	@Column(name = "checksum")
	private String checksum;

	@Column(name = "signature")
	private String signature;

	@Column(name = "created_time", nullable = false)
	private Long createdTime;

	@Column(name = "updated_time")
	private Long updatedTime;

	@Column(name = "created_by", nullable = false, length = 45)
	private String createdBy;

	@Column(name = "updated_by", length = 45)
	private String updatedBy;

	@Column(name = "release_note", length = 256)
	private String releaseNote;

	@Column(name = "assignable")
	private boolean assignable;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "softwareVersion")
	private Set<SoftwarePackages> versionControls = new HashSet<SoftwarePackages>(0);
	
	

	public SoftwareVersion(String version, Software software, UpdateMode updateMode, String filePath, Long createdTime,
			String createdBy) {
		super();
		this.version = version;
		this.software = software;
		this.updateMode = updateMode;
		this.filePath = filePath;
		this.createdTime = createdTime;
		this.createdBy = createdBy;
	}

	public SoftwareVersion(SoftwareVersionAddRequest softwareAddVersion, Software software, UpdateMode updateMode) {
		this.version = softwareAddVersion.getVersion();
		this.software = software;
		this.updateMode = updateMode;
		this.filePath = softwareAddVersion.getFilePath();
		this.softwareSize = softwareAddVersion.getSoftwareSize();
		this.signature = softwareAddVersion.getSignature();
		if (StringUtils.isNotBlank(softwareAddVersion.getChecksum())) {
			String checksumHex = CryptoUtils.toHex(Base64.getDecoder().decode(softwareAddVersion.getChecksum()));
			this.checksum = checksumHex.toLowerCase();
		}
		this.createdTime = System.currentTimeMillis();
		this.createdBy = "";
		this.releaseNote = softwareAddVersion.getReleaseNote();
		this.assignable = true;
	}

	public SoftwareVersion() {

	}

	public SoftwareVersion(String version, Software software, UpdateMode updateMode, String filePath, long createdTime,
			String createdBy) {
		this.version = version;
		this.software = software;
		this.updateMode = updateMode;
		this.filePath = filePath;
		this.createdTime = createdTime;
		this.createdBy = createdBy;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public Software getSoftware() {
		return software;
	}

	public void setSoftware(Software software) {
		this.software = software;
	}

	public UpdateMode getUpdateMode() {
		return updateMode;
	}

	public void setUpdateMode(UpdateMode updateMode) {
		this.updateMode = updateMode;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public Long getSoftwareSize() {
		return softwareSize;
	}

	public void setSoftwareSize(Long softwareSize) {
		this.softwareSize = softwareSize;
	}

	public String getChecksum() {
		return checksum;
	}

	public void setChecksum(String checksum) {
		this.checksum = checksum;
	}

	public Long getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(Long createdTime) {
		this.createdTime = createdTime;
	}

	public Long getUpdatedTime() {
		return updatedTime;
	}

	public void setUpdatedTime(Long updatedTime) {
		this.updatedTime = updatedTime;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getReleaseNote() {
		return releaseNote;
	}

	public void setReleaseNote(String releaseNote) {
		this.releaseNote = releaseNote;
	}

	public Set<SoftwarePackages> getVersionControls() {
		return versionControls;
	}

	public void setVersionControls(Set<SoftwarePackages> versionControls) {
		this.versionControls = versionControls;
	}

	public String getPackageId() {
		if (software != null) {
			return software.getPackageName();
		}
		return null;
	}

	public Integer getServicePlatformId() {
		if (software != null) {
			return software.getServicePlatformId();
		}
		return null;
	}

	public ServicePlatform getServicePlatform() {
		if (software != null) {
			return software.getServicePlatform();
		}
		return null;
	}

	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

	public boolean isAssignable() {
		return assignable;
	}

	public void setAssignable(boolean assignable) {
		this.assignable = assignable;
	}

}
