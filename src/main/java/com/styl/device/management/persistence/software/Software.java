/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.software;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import org.hibernate.annotations.SQLDelete;

import com.styl.device.management.persistence.device.model.DeviceModel;
import com.styl.device.management.persistence.service.platform.ServicePlatform;
import com.styl.device.management.persistence.software.packages.SoftwarePackages;
import com.styl.device.management.persistence.software.version.SoftwareVersion;
import com.styl.device.management.rest.portal.admin.software.SoftwareAddRequest;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

/**
 * <AUTHOR> Lam
 *
 */
@Entity
@Table(name = "tbl_software", uniqueConstraints = { @UniqueConstraint(columnNames = "name"),
		@UniqueConstraint(columnNames = "package_name") })
@SQLDelete(sql = "UPDATE tbl_software SET deleted = true WHERE id=?")
public class Software {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	private Long id;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "service_platform_id", nullable = true)
	private ServicePlatform servicePlatform;

	@Column(name = "package_name", unique = true, length = 45)
	private String packageName;

	@Column(name = "name", unique = true, nullable = false, length = 45)
	private String name;

	@Column(name = "description", length = 200)
	private String description;

	@OneToMany(fetch = FetchType.EAGER, mappedBy = "software")
	private Set<SoftwareVersion> softwareVersions = new HashSet<SoftwareVersion>(0);

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "software")
	private Set<SoftwarePackages> versionControls = new HashSet<SoftwarePackages>(0);

	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = "tbl_software_device_model", joinColumns = {
			@JoinColumn(name = "software_id", nullable = false, updatable = false, insertable = false) }, inverseJoinColumns = {
					@JoinColumn(name = "device_model", nullable = false, updatable = false, insertable = false) })
	private Set<DeviceModel> deviceModels = new HashSet<DeviceModel>(0);

	private boolean deleted;

	/**
	 * @param softwareAdd
	 * @param deviceModel
	 */
	public Software(SoftwareAddRequest softwareAdd) {
		this.packageName = softwareAdd.getPackageName();
		this.name = softwareAdd.getName();
		this.description = softwareAdd.getDescription();
	}

	/**
	 * @param id
	 * @param deviceModel
	 * @param servicePlatform
	 * @param packageId
	 * @param name
	 * @param description
	 * @param softwareVersions
	 * @param versionControls
	 */
	public Software(Long id, ServicePlatform servicePlatform, String packageName, String name, String description,
			Set<SoftwareVersion> softwareVersions, Set<SoftwarePackages> versionControls) {
		this.id = id;
		this.servicePlatform = servicePlatform;
		this.packageName = packageName;
		this.name = name;
		this.description = description;
		this.softwareVersions = softwareVersions;
		this.versionControls = versionControls;
	}

	public Software(Set<DeviceModel> deviceModels, ServicePlatform servicePlatform, String packageName, String name,
			String description) {
		this.servicePlatform = servicePlatform;
		this.packageName = packageName;
		this.name = name;
		this.description = description;
		this.deviceModels = deviceModels;
	}

	/**
	 * 
	 */
	public Software() {

	}

	public long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getServicePlatformId() {
		if (servicePlatform == null) {
			return null;
		}
		return servicePlatform.getId().intValue();
	}

	public ServicePlatform getServicePlatform() {
		return servicePlatform;
	}

	public void setServicePlatform(ServicePlatform servicePlatform) {
		this.servicePlatform = servicePlatform;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Set<SoftwareVersion> getSoftwareVersions() {
		return softwareVersions;
	}

	public void setSoftwareVersions(Set<SoftwareVersion> softwareVersions) {
		this.softwareVersions = softwareVersions;
	}

	public Set<SoftwarePackages> getVersionControls() {
		return versionControls;
	}

	public void setVersionControls(Set<SoftwarePackages> versionControls) {
		this.versionControls = versionControls;
	}

	public Set<DeviceModel> getDeviceModels() {
		return deviceModels;
	}

	public Set<String> getListNameDeviceModels() {
		if (deviceModels == null || deviceModels.isEmpty()) {
			return new HashSet<String>();
		}
		return deviceModels.stream().map(i -> i.getModel()).collect(Collectors.toSet());
	}

	public void setDeviceModels(Set<DeviceModel> deviceModels) {
		this.deviceModels = deviceModels;
	}

	public boolean isDeleted() {
		return deleted;
	}

	public void setDeleted(boolean deleted) {
		this.deleted = deleted;
	}

}
