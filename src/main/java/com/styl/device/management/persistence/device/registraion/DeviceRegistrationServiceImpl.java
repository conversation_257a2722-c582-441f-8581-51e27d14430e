/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device.registraion;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.styl.device.management.rest.portal.admin.device.DevicePendingRegisterResponse;
import com.styl.device.management.utils.Pagination;
import com.styl.device.management.utils.Utils;

/**
 * <AUTHOR> Lam
 *
 */
@Service
public class DeviceRegistrationServiceImpl implements DeviceRegistrationService {

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private DeviceRegistrationRepository deviceResReposiroty;

	@Override
	public Pagination<DevicePendingRegisterResponse> findDevicePendingRegister(String hardwareId, String model,
			String simId, String imei, Long fromTime, Long toTime, String sortBy, String order, int page,
			int pageSize) {

		Utils.validatePagination(page, pageSize);
		Pageable paging = PageRequest.of(page, pageSize, Sort
				.by(StringUtils.equalsIgnoreCase(order, "DESC") ? Sort.Direction.DESC : Sort.Direction.ASC, sortBy));

		DeviceRegistrationSpecification specs = new DeviceRegistrationSpecification(hardwareId, model, simId, imei,
				fromTime, toTime);
		Page<DeviceRegistration> listDeviceRegis = deviceResReposiroty.findAll(specs, paging);
		Long totalItems = listDeviceRegis.getTotalElements();

		// transfer to DTO
		List<DevicePendingRegisterResponse> listResult = listDeviceRegis.stream().map(d -> {
			return new DevicePendingRegisterResponse(d);
		}).collect(Collectors.toList());
		return new Pagination<DevicePendingRegisterResponse>(totalItems, page + 1, pageSize, listResult);
	}

	@Override
	public boolean deleteDeviceRegister(Long id) {
		deviceResReposiroty.deleteById(id);
		return true;
	}
}
