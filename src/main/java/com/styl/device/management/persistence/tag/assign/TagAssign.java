/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.tag.assign;

import java.util.Objects;

import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;

import com.styl.device.management.persistence.device.Device;
import com.styl.device.management.persistence.tag.Tag;

/**
 * <AUTHOR> Lam
 *
 */
@Entity
@Table(name = "tbl_tag_assign")
public class TagAssign {

	@EmbeddedId
	private DeviceTagId id;
	
	@ManyToOne(fetch = FetchType.EAGER)
	@MapsId("deviceId")
	@JoinColumn(name = "device_id", nullable = false)
	private Device device;
	
	@ManyToOne(fetch = FetchType.EAGER)
	@MapsId("tagId")
	@JoinColumn(name = "tag_id", nullable = false)
	private Tag tag;
	
	@Override
	public int hashCode() {
		return Objects.hash(device, id, tag);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		TagAssign other = (TagAssign) obj;
		return Objects.equals(device, other.device) && Objects.equals(id, other.id) && Objects.equals(tag, other.tag);
	}

	public Device getDevice() {
		return device;
	}

	public void setDevice(Device device) {
		this.device = device;
	}

	public Tag getTag() {
		return tag;
	}

	public void setTag(Tag tag) {
		this.tag = tag;
	}
}
