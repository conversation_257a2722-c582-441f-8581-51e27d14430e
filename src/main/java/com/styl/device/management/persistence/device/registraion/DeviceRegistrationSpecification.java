/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.persistence.device.registraion;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

import org.springframework.lang.Nullable;

import com.styl.device.management.utils.CustomSpecification;

/**
 * <AUTHOR> Lam
 *
 */
public class DeviceRegistrationSpecification extends CustomSpecification<DeviceRegistration> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -13861674421957600L;

	private String hardwareId;

	private String model;

	private String simId;

	private String imei;

	private Long fromTime;

	private Long toTime;

	/**
	 * @param hardwareId
	 * @param model
	 * @param simId
	 * @param imei
	 * @param fromTime
	 * @param toTime
	 */
	public DeviceRegistrationSpecification(String hardwareId, String model, String simId, String imei, Long fromTime,
			Long toTime) {
		this.hardwareId = hardwareId;
		this.model = model;
		this.simId = simId;
		this.imei = imei;
		this.fromTime = fromTime;
		this.toTime = toTime;
	}

	@Override
	@Nullable
	public Predicate toPredicate(Root<DeviceRegistration> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
		List<Predicate> predicates = new ArrayList<>();
		if (isNotBlank(hardwareId)) {
			predicates.add(like(cb, root.get("hardwareId"), hardwareId));
		}

		if (isNotBlank(model)) {
			predicates.add(equals(cb, root.get("model"), model));
		}

		if (isNotBlank(simId)) {
			predicates.add(like(cb, root.get("simId"), simId));
		}

		if (isNotBlank(imei)) {
			predicates.add(like(cb, root.get("imei"), imei));
		}

		if (isNotNull(fromTime)) {
			predicates.add(greaterThanOrEqualTo(cb, root.get("registrationTime"), fromTime));
		}

		if (isNotNull(toTime)) {
			predicates.add(lessThanOrEqualTo(cb, root.get("registrationTime"), toTime));
		}

		return cb.and(predicates.toArray(new Predicate[predicates.size()]));
	}

}
