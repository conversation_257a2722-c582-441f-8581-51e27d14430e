/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.kafka;

import java.util.HashMap;
import java.util.Map;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;

import com.styl.device.management.service.platform.event.publisher.event.common.Event;

/**
 * <AUTHOR>
 *
 */

@Configuration
public class KafkaProducerConfig {

	@Value(value = "${com.styl.device.management.kafka.bootstrap-servers}")
	private String bootstrapAddress;

	@Value(value = "${com.styl.device.management.kafka.groupId}")
	private String groupId;

	@Value(value = "${com.styl.device.management.kafka.ssl.enabled:false}")
	private boolean sslEnabled;

	@Value(value = "${com.styl.device.management.kafka.ssl.truststore.location}")
	private String sslTrustoreLocation;

	@Value(value = "${com.styl.device.management.kafka.ssl.truststore.password}")
	private String sslTrustorePassword;

	@Value(value = "${com.styl.device.management.kafka.security.protocol:SASL_SSL}")
	private String securityProtocol;

	@Value(value = "${com.styl.device.management.kafka.sasl.mechanism:SCRAM-SHA-512}")
	private String saslMechanism;

	@Value(value = "${com.styl.device.management.kafka.ssl.producer.user}")
	private String sslProducerUser;

	@Value(value = "${com.styl.device.management.kafka.ssl.producer.password}")
	private String sslProducerPassword;

	private final String jaasTemplate = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"%s\" password=\"%s\";";

	@Bean
	public ProducerFactory<String, Event<?>> eventProducerFactory() {
		Map<String, Object> configProps = new HashMap<>();
		configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
		configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
		configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);

		if (sslEnabled) {
			configProps.put("sasl.mechanism", saslMechanism);
			configProps.put("sasl.jaas.config", String.format(jaasTemplate, sslProducerUser, sslProducerPassword));
			configProps.put("security.protocol", securityProtocol);
			configProps.put("ssl.truststore.location", sslTrustoreLocation);
			configProps.put("ssl.truststore.password", sslTrustorePassword);
			configProps.put("ssl.endpoint.identification.algorithm", "");
		}

		return new DefaultKafkaProducerFactory<>(configProps);
	}

	@Bean
	public KafkaTemplate<String, Event<?>> eventKafkaTemplate() {
		return new KafkaTemplate<>(eventProducerFactory());
	}
}
