/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.kafka;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ExecutionException;

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.admin.TopicListing;
import org.apache.kafka.common.acl.AccessControlEntry;
import org.apache.kafka.common.acl.AclBinding;
import org.apache.kafka.common.acl.AclOperation;
import org.apache.kafka.common.acl.AclPermissionType;
import org.apache.kafka.common.resource.PatternType;
import org.apache.kafka.common.resource.ResourcePattern;
import org.apache.kafka.common.resource.ResourceType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Yee
 *
 */
@Component
public class KafkaTopicService {

	@Value(value = "${com.styl.device.management.kafka.bootstrap-servers}")
	private String bootstrapAddress;

	private AdminClient adminClient;

	@Value(value = "${com.styl.device.management.kafka.ssl.enabled:true}")
	private boolean sslEnabled;

	@Value(value = "${com.styl.device.management.kafka.acl.enabled:true}")
	private boolean aclEnabled;

	@Value(value = "${com.styl.device.management.kafka.ssl.truststore.location}")
	private String sslTrustoreLocation;

	@Value(value = "${com.styl.device.management.kafka.ssl.truststore.password}")
	private String sslTrustorePassword;

	@Value(value = "${com.styl.device.management.kafka.security.protocol:SASL_SSL}")
	private String securityProtocol;

	@Value(value = "${com.styl.device.management.kafka.sasl.mechanism:SCRAM-SHA-512}")
	private String saslMechanism;

	@Value(value = "${com.styl.device.management.kafka.ssl.admin.user:demouser}")
	private String sslAdminUser;

	@Value(value = "${com.styl.device.management.kafka.ssl.admin.password:secret}")
	private String sslAdminPassword;

	@Value(value = "${com.styl.device.management.kafka.hostname}")
	private String hostName;

	private final String jaasTemplate = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"%s\" password=\"%s\";";

	public void createTopic(String topicName) {
		getAdminClient().createTopics(Collections.singletonList(new NewTopic(topicName, 1, (short) 1)));
		if (aclEnabled) {
			authorizedForTopic(topicName);
		}
	}

	public void authorizedForTopic(String topicName) {
		String aclPrincipal = String.format("User:%s", sslAdminUser);

		List<AclBinding> acls = Collections
				.singletonList(new AclBinding(new ResourcePattern(ResourceType.TOPIC, topicName, PatternType.LITERAL),
						new AccessControlEntry(aclPrincipal, hostName, AclOperation.ALL, AclPermissionType.ALLOW)));
		getAdminClient().createAcls(acls);
	}

	public void authorizedForAdminTopicCreation() {
		String aclPrincipal = String.format("User:%s", sslAdminUser);

		List<AclBinding> acls = new ArrayList<>();
		acls.add(new AclBinding(new ResourcePattern(ResourceType.TOPIC, "*", PatternType.LITERAL),
				new AccessControlEntry(aclPrincipal, hostName, AclOperation.READ, AclPermissionType.ALLOW)));
		acls.add(new AclBinding(new ResourcePattern(ResourceType.TOPIC, "*", PatternType.LITERAL),
				new AccessControlEntry(aclPrincipal, hostName, AclOperation.CREATE, AclPermissionType.ALLOW)));
		acls.add(new AclBinding(new ResourcePattern(ResourceType.GROUP, "*", PatternType.LITERAL),
				new AccessControlEntry(aclPrincipal, hostName, AclOperation.ALL, AclPermissionType.ALLOW)));

		getAdminClient().createAcls(acls);
	}

	public void authorizedForAdminOnGroup(String groupName) {
		String aclPrincipal = String.format("User:%s", sslAdminUser);

		List<AclBinding> acls = new ArrayList<>();
		acls.add(new AclBinding(new ResourcePattern(ResourceType.GROUP, groupName, PatternType.LITERAL),
				new AccessControlEntry(aclPrincipal, hostName, AclOperation.ALL, AclPermissionType.ALLOW)));

		getAdminClient().createAcls(acls);
	}

	public TopicListing findTopic(String topicName) throws InterruptedException, ExecutionException {
		Map<String, TopicListing> topicList = getAdminClient().listTopics().namesToListings().get();
		return topicList.get(topicName);
	}

	public boolean isTopicExist(String topicName) throws InterruptedException, ExecutionException {
		return findTopic(topicName) != null;
	}

	public AdminClient getAdminClient() {
		if (adminClient == null) {
			Properties config = new Properties();
			config.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);

			if (sslEnabled) {
				config.put("sasl.mechanism", saslMechanism);
				config.put("sasl.jaas.config", String.format(jaasTemplate, sslAdminUser, sslAdminPassword));
				config.put("security.protocol", securityProtocol);
				config.put("ssl.truststore.location", sslTrustoreLocation);
				config.put("ssl.truststore.password", sslTrustorePassword);
				config.put("ssl.endpoint.identification.algorithm", "");
			}
			synchronized (AdminClient.class) {
				adminClient = AdminClient.create(config);
			}
		}
		return adminClient;
	}

}
