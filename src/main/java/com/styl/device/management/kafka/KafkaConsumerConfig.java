/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.kafka;

import java.util.HashMap;
import java.util.Map;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.support.serializer.JsonDeserializer;

import com.styl.device.management.service.platform.event.publisher.event.common.Event;

/**
 * <AUTHOR>
 *
 */
@EnableKafka
@Configuration
public class KafkaConsumerConfig {

	@Value(value = "${com.styl.device.management.kafka.bootstrap-servers}")
	private String bootstrapAddress;

	@Value(value = "${com.styl.device.management.kafka.groupId}")
	private String groupId;

	@Value(value = "${com.styl.device.management.kafka.ssl.enabled:false}")
	private boolean sslEnabled;

	@Value(value = "${com.styl.device.management.kafka.ssl.truststore.location}")
	private String sslTrustoreLocation;

	@Value(value = "${com.styl.device.management.kafka.ssl.truststore.password}")
	private String sslTrustorePassword;

	@Value(value = "${com.styl.device.management.kafka.security.protocol:SASL_SSL}")
	private String securityProtocol;

	@Value(value = "${com.styl.device.management.kafka.sasl.mechanism:SCRAM-SHA-512}")
	private String saslMechanism;

	@Value(value = "${com.styl.device.management.kafka.ssl.consumer.user}")
	private String sslConsumerUser;

	@Value(value = "${com.styl.device.management.kafka.ssl.consumer.password}")
	private String sslConsumerPassword;

	private final String jaasTemplate = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"%s\" password=\"%s\";";

	@Bean
	public ConsumerFactory<String, Event<?>> consumerFactory() {
		JsonDeserializer<Event<?>> deserializer = new JsonDeserializer<>(Event.class);
		deserializer.setRemoveTypeHeaders(false);
		deserializer.addTrustedPackages("*");
		deserializer.setUseTypeMapperForKey(true);

		Map<String, Object> props = new HashMap<>();
		props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
		props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
		props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
		props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);

		if (sslEnabled) {
			props.put("sasl.mechanism", saslMechanism);
			props.put("sasl.jaas.config", String.format(jaasTemplate, sslConsumerUser, sslConsumerPassword));
			props.put("security.protocol", securityProtocol);
			props.put("ssl.truststore.location", sslTrustoreLocation);
			props.put("ssl.truststore.password", sslTrustorePassword);
			props.put("ssl.endpoint.identification.algorithm", "");
		}

		return new DefaultKafkaConsumerFactory<>(props, new StringDeserializer(), deserializer);
	}

	@Bean
	public ConcurrentKafkaListenerContainerFactory<String, Event<?>> kafkaListenerContainerFactory() {

		ConcurrentKafkaListenerContainerFactory<String, Event<?>> factory = new ConcurrentKafkaListenerContainerFactory<>();
		factory.setConsumerFactory(consumerFactory());
		return factory;
	}
}