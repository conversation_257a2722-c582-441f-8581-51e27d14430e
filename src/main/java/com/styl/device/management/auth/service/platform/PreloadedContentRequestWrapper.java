/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.auth.service.platform;

import java.io.ByteArrayInputStream;
import java.io.IOException;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;

/**
 * <AUTHOR>
 *
 */
public class PreloadedContentRequestWrapper extends HttpServletRequestWrapper {

	private PreloadedContentInputStream inputStream;

	public PreloadedContentRequestWrapper(HttpServletRequest request, byte[] content) {
		super(request);
		this.inputStream = new PreloadedContentInputStream(content);
	}

	@Override
	public ServletInputStream getInputStream() throws IOException {
		return this.inputStream;
	}

	private class PreloadedContentInputStream extends ServletInputStream {
		private ByteArrayInputStream inputStream;

		public PreloadedContentInputStream(byte[] content) {
			this.inputStream = new ByteArrayInputStream(content);
		}

		@Override
		public boolean isFinished() {
			return (inputStream.available() == 0);
		}

		@Override
		public boolean isReady() {
			return true;
		}

		@Override
		public void setReadListener(ReadListener readListener) {
			try {
				readListener.onDataAvailable();
			} catch (Exception e) {
				
			}
		}

		@Override
		public int read() throws IOException {
			return inputStream.read();
		}
	}
}
