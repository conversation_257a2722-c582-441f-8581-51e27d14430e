/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.auth.service.platform;

import java.io.IOException;
import java.nio.charset.Charset;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.error.ErrorResponse;
import com.styl.device.management.exception.AuthenticationException;
import com.styl.device.management.persistence.api.key.ApiKey;
import com.styl.device.management.persistence.api.key.ApiKeyService;
import com.styl.device.management.persistence.service.platform.ServicePlatformService;
import com.styl.device.management.utils.CryptoUtils;

/**
 * <AUTHOR> Yee
 *
 */
@Component
public class ServicePlatformAuthenticationFilter implements Filter {

	private static final Logger logger = LoggerFactory.getLogger(ServicePlatformAuthenticationFilter.class);

	private static final String API_KEY = "DMS-ApiKey";
	private static final String NONCE = "DMS-Nonce";
	private static final String SIGNATURE = "DMS-Signature";

	@Value("${service.platform.auth.nounce.timeout:1200000}") // 120 seconds
	private long nounceTimeOut;

	@Value("${service.platform.auth.nounce.random.min.length:16}")
	private int randomMinLen;

	@Value("${service.platform.auth.nounce.random.max.length:64}")
	private int randomMaxLen;

	private long getNonceTimeout() {
		return nounceTimeOut;
	}

	private int getRandomMinLength() {
		return randomMinLen;
	}

	private int getRandomMaxLength() {
		return randomMaxLen;
	}

	@Autowired
	private ApiKeyService apiKeyService;

	@Autowired
	private ServicePlatformService servicePlatformService;

	@Override
	public void init(FilterConfig arg0) throws ServletException {
		logger.info("ServicePlatformFilter init");
	}

	@Override
	public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain)
			throws IOException, ServletException {
		HttpServletRequest request = (HttpServletRequest) req;
		HttpServletResponse response = (HttpServletResponse) resp;
		String requestUri = request.getRequestURI().replaceAll("\\/+", "/");
		if (requestUri.startsWith("/serviceplatform")) {
			try {
				byte[] content = authenticate(request);
				chain.doFilter(new PreloadedContentRequestWrapper(request, content), resp);
			} catch (AuthenticationException e) {
				response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
				response.setHeader("Content-Type", "application/json");
				response.getWriter()
						.write(convertObjectToJson(new ErrorResponse(e.getErrorCode(), e.getErrorMessage())));

			} catch (Exception e) {
				AuthenticationException ex = new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_FAILED);
				logger.error("Request Authenticate failed", e);
				response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
				response.setHeader("Content-Type", "application/json");
				response.getWriter()
						.write(convertObjectToJson(new ErrorResponse(ex.getErrorCode(), ex.getErrorMessage())));
			}
		} else {
			chain.doFilter(req, resp);
		}
	}

	public String convertObjectToJson(Object object) throws JsonProcessingException {
		if (object == null) {
			return null;
		}
		ObjectMapper mapper = new ObjectMapper();
		return mapper.writeValueAsString(object);
	}

	private byte[] authenticate(HttpServletRequest request)
			throws AuthenticationException, IOException, InvalidKeyException, NoSuchAlgorithmException {

		String apiKey = request.getHeader(API_KEY);
		logger.debug(API_KEY + ": " + apiKey);
		if (StringUtils.isEmpty(apiKey)) {
			throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_API_KEY_EMPTY);
		}

		String signature = request.getHeader(SIGNATURE);
		logger.debug(SIGNATURE + ": " + signature);
		if (StringUtils.isEmpty(signature)) {
			throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_SIGNATURE_EMPTY);
		}

		String nonce = request.getHeader(NONCE);
		logger.debug(NONCE + ": " + nonce);
		if (StringUtils.isEmpty(nonce)) {
			throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_NONCE_EMPTY);
		}

		String[] nonces = nonce.split("#", 2);
		if (nonces.length < 2) {
			throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_NONCE_INVALID);
		}

		try {
			long timestamp = Long.parseLong(nonces[0]);
			if ((System.currentTimeMillis() - timestamp > getNonceTimeout())
					|| (timestamp - System.currentTimeMillis() > getNonceTimeout())) {
				throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_NONCE_TIMEOUT);
			}
		} catch (NumberFormatException e) {
			throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_NONCE_INVALID);
		}

		if (nonces[1].length() < getRandomMinLength() || nonces[1].length() > getRandomMaxLength()) {
			throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_NONCE_RANDOM_LEN_INVALID);
		}

		ApiKey spApiKey = apiKeyService.getByApiKey(apiKey);

		if (spApiKey == null) {
			throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_API_KEY_INVALID);
		}
		if (servicePlatformService.findByServicePlatformId(spApiKey.getServicePlatformId()) == null) {
			throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_API_KEY_INVALID);
		}
		if (spApiKey.getExpiredTime() != null && spApiKey.getExpiredTime() >= 0
				&& spApiKey.getExpiredTime() < System.currentTimeMillis()) {
			throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_API_EXPIRED);
		}
		if (spApiKey.getSecretKey() == null) {
			throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_API_KEY_INVALID);
		}

		AuthenticationContextHolder.init(spApiKey.getServicePlatformId());

		String queryString = request.getQueryString();
		logger.debug("Query String: " + queryString);

		byte[] content = IOUtils.toString(request.getInputStream(), Charset.defaultCharset()).getBytes();
		if (logger.isDebugEnabled()) {
			logger.debug("Body: " + new String(content));
		}

		if (!verifySignature(spApiKey.getSecretKey(), signature, content, queryString, nonce)) {
			throw new AuthenticationException(ErrorCode.SERVICE_PLATFORM_AUTH_SIGNATURE_NOT_MATCH);
		}

		return content;
	}

	protected boolean verifySignature(String secretKey, String signature, byte[] content, String queryString,
			String nonce) throws InvalidKeyException, NoSuchAlgorithmException {

		StringBuilder signatureData = new StringBuilder();
		if (content != null && content.length > 0) {
			signatureData.append(new String(content));
		}
		if (queryString != null) {
			signatureData.append(queryString);
		}
		signatureData.append(nonce);

		String verify = CryptoUtils.hmacSHA256(secretKey, signatureData.toString());

		logger.debug("Calculated Siganure: " + verify);
		return signature.equalsIgnoreCase(verify);
	}

	@Override
	public void destroy() {
		logger.info("ServicePlatformFilter destroy");
	}

}
