/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.auth.service.platform;

/**
 * <AUTHOR>
 *
 */

public class AuthenticationContextHolder {

	private static final ThreadLocal<Integer> servicePlatformIdContext = new ThreadLocal<Integer>();

	public static void initialize() {
		servicePlatformIdContext.remove();
	}

	public static void init(Integer servicePlatformId) {
		servicePlatformIdContext.set(servicePlatformId);

	}

	public static void cleanup() {
		servicePlatformIdContext.remove();
	}

	public static Integer getServicePlatformId() {
		return servicePlatformIdContext.get();
	}


}
