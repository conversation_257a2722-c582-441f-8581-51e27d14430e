/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.exception;

import java.util.List;

import com.styl.device.management.error.ErrorCode;

/**
 * This class represent response to client when there is one or more value in
 * the request doesn't pass validation. This class contain a list of
 * {@link FieldError} object, each object contain information to describe one
 * violation.
 * 
 * <AUTHOR>
 * @see ConstraintViolationExceptionMapper
 * @see FieldError
 *
 */
public class FieldValidationError {

	private List<FieldError> errors;

	/**
	 * Default constructor
	 */
	public FieldValidationError() {
		super();
	}

	/**
	 * Constructor from list of {@link FieldError}
	 * 
	 * @param errors list of errors
	 */
	public FieldValidationError(List<FieldError> errors) {
		super();
		this.errors = errors;
	}

	public List<FieldError> getErrors() {
		return errors;
	}

	public void setErrors(List<FieldError> errors) {
		this.errors = errors;
	}

	/**
	 * Provide information to describe about 1 validation
	 * 
	 * <AUTHOR>
	 *
	 */
	public static class FieldError {
		private String field;
		private String errorCode;
		private String errorMessage;

		/**
		 * Default constructor
		 */
		public FieldError() {
		}

		/**
		 * Constructor from fields
		 * 
		 * @param field        indicate the field whose value doesn't pass validation.
		 *                     the field can be hierarchical. E.g object1.field1
		 *                     indicate field1 inside object1 in the request
		 * @param errorCode    describe the reason of the validation
		 * @param errorMessage error message for reference. This error message is for
		 *                     reference only. Client should display error-message by
		 *                     itself instead of displaying this error-message directly
		 * 
		 * @see FieldValidationError#ERROR_EMPTY
		 * @see FieldValidationError#ERROR_INVALID
		 * @see FieldValidationError#ERROR_DUPLICATE
		 * @see FieldValidationError#ERROR_TOO_SHORT
		 * @see FieldValidationError#ERROR_TOO_LONG
		 * @see FieldValidationError#ERROR_TOO_SHORT_OR_LONG
		 */
		public FieldError(String field, String errorCode, String errorMessage) {
			super();
			this.field = field;
			this.errorCode = errorCode;
			this.errorMessage = errorMessage;
		}
		
		public FieldError(String field, ErrorCode error) {
			super();
			this.field = field;
			this.errorCode = error.getErrorCode();
			this.errorMessage = error.getErrorMessage();
		}

		/**
		 * Indicate the field whose value doesn't pass validation. the field can be
		 * hierarchical. E.g object1.field1 indicate field1 inside object1 in the
		 * request
		 * 
		 * @return field name
		 */
		public String getField() {
			return field;
		}

		/**
		 * Set the field name to indicate the field whose value doesn't pass validation.
		 * the field can be hierarchical. E.g object1.field1 indicate field1 inside
		 * object1 in the request
		 * 
		 * @param field field-name
		 */
		public void setField(String field) {
			this.field = field;
		}

		/**
		 * Describe the reason of the validation
		 * 
		 * @return errorCode
		 * 
		 * @see FieldValidationError#ERROR_EMPTY
		 * @see FieldValidationError#ERROR_INVALID
		 * @see FieldValidationError#ERROR_DUPLICATE
		 * @see FieldValidationError#ERROR_TOO_SHORT
		 * @see FieldValidationError#ERROR_TOO_LONG
		 * @see FieldValidationError#ERROR_TOO_SHORT_OR_LONG
		 */
		public String getErrorCode() {
			return errorCode;
		}

		/**
		 * @param errorCode Describe the reason of the validation
		 * 
		 * @see FieldValidationError#ERROR_EMPTY
		 * @see FieldValidationError#ERROR_INVALID
		 * @see FieldValidationError#ERROR_DUPLICATE
		 * @see FieldValidationError#ERROR_TOO_SHORT
		 * @see FieldValidationError#ERROR_TOO_LONG
		 * @see FieldValidationError#ERROR_TOO_SHORT_OR_LONG
		 */
		public void setErrorCode(String errorCode) {
			this.errorCode = errorCode;
		}

		/**
		 * Error message for reference. This error message is for reference only. Client
		 * should display error-message by itself instead of displaying this
		 * error-message directly
		 * 
		 * @return errorMessage
		 */
		public String getErrorMessage() {
			return errorMessage;
		}

		/**
		 * @param errorMessage Error message for reference. This error message is for
		 *                     reference only. Client should display error-message by
		 *                     itself instead of displaying this error-message directly
		 */
		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}

		@Override
		public String toString() {
			return "FieldError [field=" + field + ", errorCode=" + errorCode + ", errorMessage=" + errorMessage + "]";
		}
	}
}
