/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.exception;

import java.util.ArrayList;
import java.util.List;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.exception.FieldValidationError.FieldError;


/**
 * <AUTHOR>
 *
 */
public class InvalidFieldException extends ServiceException {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6873267362167719944L;

	private final List<FieldError> fieldErrors;

	/**
	 * Construct {@link InvalidFieldException} from a {@link FieldError}
	 * 
	 * @param fieldError {@link FieldError} object
	 */
	public InvalidFieldException(FieldError fieldError) {
		super(ErrorCode.INVALID_PARAM);
		this.fieldErrors = new ArrayList<>(1);
		this.fieldErrors.add(fieldError);
	}

	/**
	 * Construct {@link InvalidFieldException} from a list of {@link FieldError}
	 * 
	 * @param fieldErrors list of {@link FieldError}
	 */
	public InvalidFieldException(List<FieldError> fieldErrors) {
		super(ErrorCode.INVALID_PARAM);
		this.fieldErrors = fieldErrors;
	}

	public List<FieldError> getFieldErrors() {
		return fieldErrors;
	}

	public FieldError getFieldError() {
		if ((fieldErrors != null) && (fieldErrors.size() > 0)) {
			return fieldErrors.get(0);
		}
		return null;
	}
}
