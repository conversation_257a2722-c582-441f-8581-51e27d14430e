/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/

package com.styl.device.management.exception;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.error.ErrorProperties;

/**
 * <AUTHOR> <PERSON>
 *
 */
public class AuthenticationException extends Exception{

	/**
	 * 
	 */
	private static final long serialVersionUID = -3270823970520011974L;

	private String errorCode;	
	
	private String errorMessage;
	
	public AuthenticationException(String errorCode) {
		super();
		this.errorCode = errorCode;
		this.errorMessage = ErrorProperties.errors.getOrDefault(errorCode, "Error message is not defined");
	}
	
	public AuthenticationException(ErrorCode errorCode) {
	    this.errorMessage = ErrorProperties.errors.getOrDefault(errorCode.getErrorCode(), "Error message is not defined");
        this.errorCode = errorCode.getErrorCode();
    }
	
	/**
     * @param errorCode
     * @param errorMessage
     */
    public AuthenticationException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }
	
    /**
     * @param message
     * @param cause
     */
    public AuthenticationException(String message, Throwable cause) {
        super(message, cause);
        this.errorMessage = message;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

}
