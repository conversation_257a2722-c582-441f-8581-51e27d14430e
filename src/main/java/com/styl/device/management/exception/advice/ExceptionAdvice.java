/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.device.management.exception.advice;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.vault.VaultException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import com.styl.device.management.error.ErrorCode;
import com.styl.device.management.error.ErrorResponse;
import com.styl.device.management.exception.AuthenticationException;
import com.styl.device.management.exception.ServiceException;

/**
 * <AUTHOR> Lam
 *
 */
@ControllerAdvice
public class ExceptionAdvice {

	private static final Logger logger = LoggerFactory.getLogger(ExceptionAdvice.class);

	private Pattern vaultErrorMessagePattern;

	public ExceptionAdvice() {
		super();
		vaultErrorMessagePattern = Pattern.compile("\\{ *?\\\"errors\\\" *?\\:\\[ *?\\\"(.+)\\\" *?\\] *?\\}");
	}

	@ExceptionHandler(value = ServiceException.class)
	public ResponseEntity<ErrorResponse> serviceException(ServiceException e) {
		logger.info("Service Exception : {} , {}", e.getErrorCode(), e.getErrorMessage());
		logger.debug("Service Exception", e);
		return new ResponseEntity<>(new ErrorResponse(e.getCode(), e.getErrorCode(), e.getErrorMessage()),
				HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(value = VaultException.class)
	public ResponseEntity<ErrorResponse> vaultException(VaultException e) {
		String errorMessage = e.getMessage();
		Matcher errorMessageMatcher = vaultErrorMessagePattern.matcher(e.getMessage());
		if (errorMessageMatcher.find()) {
			errorMessage = errorMessageMatcher.group(1);
		}

		if (StringUtils.isNotBlank(errorMessage)) {
			errorMessage = "Vault PKI - " + errorMessage;
		}

		logger.info("Service Exception : {} , {}", ErrorCode.PKI_VAULT_EXCEPTION.getErrorCode(), errorMessage);
		logger.debug("Service Exception", e);
		return new ResponseEntity<>(new ErrorResponse(ErrorCode.PKI_VAULT_EXCEPTION.getCode(),
				ErrorCode.PKI_VAULT_EXCEPTION.getErrorCode(), errorMessage), HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(value = MethodArgumentNotValidException.class)
	public ResponseEntity<ErrorResponse> invalidFieldException(MethodArgumentNotValidException e) {
		List<FieldError> errors = e.getBindingResult().getFieldErrors();
		List<ErrorResponse.Error> errs = errors.stream().map(x -> new ErrorResponse.Error(x.getField(),
				ErrorCode.INVALID_FIELD.getCode(), ErrorCode.INVALID_FIELD.getErrorCode(), x.getDefaultMessage()))
				.toList();
		ErrorResponse response = new ErrorResponse(errs);

		return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(value = MissingServletRequestParameterException.class)
	public ResponseEntity<ErrorResponse> invalidParameterException(MissingServletRequestParameterException e) {
		ErrorResponse response = new ErrorResponse(e.getParameterName(), ErrorCode.INVALID_PARAM);

		return ResponseEntity.badRequest().body(response);
	}

	@ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
	public ResponseEntity<ErrorResponse> invalidParameterException(MethodArgumentTypeMismatchException e) {
		ErrorResponse response = new ErrorResponse(e.getName(), ErrorCode.INVALID_PARAM);

		return ResponseEntity.badRequest().body(response);
	}

	@ExceptionHandler(value = HttpMessageNotReadableException.class)
	public ResponseEntity<ErrorResponse> invalidFormatException(HttpMessageNotReadableException e) {
		logger.debug("Invalid Format Exception", e);
		return new ResponseEntity<>(new ErrorResponse(ErrorCode.INVALID_FORMAT), HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(value = { HttpRequestMethodNotSupportedException.class, NoHandlerFoundException.class,
			NoSuchElementException.class })
	public ResponseEntity<ErrorResponse> notfoundException(Exception e) {
		logger.info("notfoundException", e);
		return new ResponseEntity<>(HttpStatus.NOT_FOUND);
	}

	@ExceptionHandler(value = { HttpMediaTypeNotSupportedException.class })
	public ResponseEntity<ErrorResponse> httpMediaTypeNotSupportedException(Exception e) {
		logger.info("httpMediaTypeNotSupportedException", e);
		return new ResponseEntity<>(HttpStatus.UNSUPPORTED_MEDIA_TYPE);
	}

	@ExceptionHandler(value = Exception.class)
	public ResponseEntity<ErrorResponse> unknownException(Exception e) {
		logger.error("Unknown error ", e);
		return ResponseEntity.internalServerError().body(new ErrorResponse(ErrorCode.UNKNOWN));
	}

	@ExceptionHandler(value = AuthenticationException.class)
	public ResponseEntity<ErrorResponse> authenticationException(AuthenticationException e) {
		logger.info("Service platform authentication exception : {} , {}", e.getErrorCode(), e.getErrorMessage());
		logger.debug("Service platform authentication exception : {}", e);
		return new ResponseEntity<>(new ErrorResponse(e.getErrorCode(), e.getErrorMessage()), HttpStatus.BAD_REQUEST);
	}

}
