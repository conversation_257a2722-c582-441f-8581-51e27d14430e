version: "3.4"
services:
  postgres: 
    image: postgres:14.5
    hostname: postgresql
    container_name: dms_postgres
    environment:
      POSTGRES_USER: ${DATABASE_USER}
      POSTGRES_PASSWORD: ${DATABASE_PASSWD}
      POSTGRES_DB: dms-service
    volumes:
      - ./postgresql:/var/lib/postgresql:rw
      - ./postgresql_data:/var/lib/postgresql/data
    restart: always
  dms:
    image: nexus-docker.styl.solutions/repository/docker-hosted/caribbean-rebuild/dev-image/styl-dms/develop:latest
    hostname: dms
    container_name: dms
    restart: always
    depends_on:
      - postgres
    ports:
      - 8080
    environment:
      DATABASE_USER: ${DATABASE_USER}
      DATABASE_PASSWORD: ${DATABASE_PASSWD}
      DATABASE_URL: postgres
      DATABASE_NAME: dms-service
      ACTIVE_DEVICE_URL: "https://oceans-dev.styl.solutions/portal"
      DATABASE_PORT: "5432"
      AWS_BUCKET: ${AWS_BUCKET}
      AWS_KEY: ${AWS_KEY}
      AWS_KEY_SECRET: ${AWS_KEY_SECRET}
      AWS_REGION: "ap-southeast-1"
      AWS_ENDPOINT: http://*************:9000
      AWS_ENABLED_KEY: true
      UPLOAD_FILE_TIMEOUT: "3600000"
      MAX_FILE_SIZE: ${MAX_FILE_SIZE}
      MAX_REQUEST_SIZE: ${MAX_REQUEST_SIZE}
      SWAGGER_UI_ENABLED: ${SWAGGER_UI_ENABLE}
      VAULT_ENPOINT: http://*************:8200/
      VAULT_TOKEN: ${VAULT_TOKEN}
      KAFKA_SERVER: http://*************:9092
      keycloak.realm: Ocean-admin
      Keycloak.resource: congle
      keycloak.auth-server-url: https://ciam-dev.styl.solutions/auth
      keycloak.ssl-required: external
      keycloak.public-client: true
      keycloak.bearer-only: true
      logging.level.org.keycloak: TRACE
      keycloak.security-constraints[0].authRoles[0]: default-roles-ocean-admin
      keycloak.security-constraints[0].securityCollections[0].patterns[0]: /api/*
    healthcheck:
      test: ["curl", "http://localhost:8080/actuator/health/liveness"]
      interval: 1m30s
      timeout: 30s
      retries: 5
      start_period: 30s
  api-gateway-mtls:
    # The :watch image restarts the service automatically when the configuration files change.
    # Do not use this image in production, it's meant to speed up your testing and development.
    image: devopsfaith/krakend:watch
    hostname: api-gateway-mtls
    container_name: api-gateway-mtls
    restart: always
    command: ["run", "-d", "-c", "/etc/krakend/krakend.json"]
    depends_on:
      - dms
    ports:
      - 1234:1234
      - 443:443
      - 8090:8090
    volumes:
      - ./krakend-mtls:/etc/krakend/
  api-gateway:
    # The :watch image restarts the service automatically when the configuration files change.
    # Do not use this image in production, it's meant to speed up your testing and development.
    image: devopsfaith/krakend:watch
    hostname: api-gateway
    container_name: api-gateway
    restart: always
    command: ["run", "-d", "-c", "/etc/krakend/krakend.json"]
    depends_on:
      - dms
    ports:
      - 8000:8000
    volumes:
      - ./krakend:/etc/krakend/