{"$schema": "https://www.krakend.io/schema/v3.json", "version": 3, "name": "KrakenD - API Gateway DMS", "allow_insecure_connections": true, "extra_config": {"security/cors": {"allow_origins": ["*"], "allow_methods": ["GET", "HEAD", "POST", "OPTIONS", "DELETE", "PUT"], "expose_headers": ["Content-Length", "Content-Type"], "allow_headers": ["Accept-Language", "Origin", "Authorization", "Content-Type", "Cache-Control", "Pragma", "X-Token"], "max_age": "12h", "allow_credentials": false, "debug": true}}, "timeout": "3000ms", "cache_ttl": "300s", "output_encoding": "no-op", "endpoints": [{"endpoint": "/dms/api/management/tag", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/tag", "encoding": "json", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/tag", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/tag", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/tag", "method": "PUT", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/tag", "encoding": "no-op", "sd": "static", "method": "PUT", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/tag/{id}", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/tag/{id}", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/tag/{id}", "method": "DELETE", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/tag/{id}", "encoding": "no-op", "sd": "static", "method": "DELETE", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software", "method": "PUT", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software", "encoding": "no-op", "sd": "static", "method": "PUT", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software/{id}", "method": "DELETE", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software/{id}", "encoding": "no-op", "sd": "static", "method": "DELETE", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/service-platform", "method": "GET", "backend": [{"url_pattern": "/api/management/service-platform", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/service-platform/{servicePlatformId}/apikey/list", "method": "GET", "backend": [{"url_pattern": "/api/management/service-platform/{servicePlatformId}/apikey/list", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/service-platform", "method": "PUT", "backend": [{"url_pattern": "/api/management/service-platform", "sd": "static", "method": "PUT", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/service-platform", "method": "POST", "backend": [{"url_pattern": "/api/management/service-platform", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/service-platform/{servicePlatformId}/apikey/add", "method": "POST", "backend": [{"url_pattern": "/api/management/service-platform/{servicePlatformId}/apikey/add", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/service-platform/all", "method": "GET", "backend": [{"url_pattern": "/api/management/service-platform/all", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/service-platform/{servicePlatformId}", "method": "DELETE", "backend": [{"url_pattern": "/api/management/service-platform/{servicePlatformId}", "sd": "static", "method": "DELETE", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/service-platform/{servicePlatformId}/apikey/delete/{id}", "method": "DELETE", "backend": [{"url_pattern": "/api/management/service-platform/{servicePlatformId}/apikey/delete/{id}", "sd": "static", "method": "DELETE", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/service-platform/notification-setting/{servicePlatformId}", "method": "GET", "backend": [{"url_pattern": "/api/management/service-platform/notification-setting/{servicePlatformId}", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/service-platform/notification-setting/{servicePlatformId}", "method": "POST", "backend": [{"url_pattern": "/api/management/service-platform/notification-setting/{servicePlatformId}", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/service-platform/notification-setting/{servicePlatformId}/createtopic", "method": "POST", "backend": [{"url_pattern": "/api/management/service-platform/notification-setting/{servicePlatformId}/createtopic", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software-version", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software-version", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software-version", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software-version", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software-version", "method": "PUT", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software-version", "encoding": "no-op", "sd": "static", "method": "PUT", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software-version/unassign", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software-version/unassign", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software-version/assign", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software-version/assign", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software-version/assign-tag", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software-version/assign-tag", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software-version/{id}/unassign-devices", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software-version/{id}/unassign-devices", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software-version/{id}/assign-devices", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software-version/{id}/assign-devices", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software-version/upload", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software-version/upload", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software-version/update-mode", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software-version/update-mode", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/software-version/{id}", "method": "DELETE", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/software-version/{id}", "encoding": "no-op", "sd": "static", "method": "DELETE", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device/register", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device/register", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device/unassign/service-platform", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device/unassign/service-platform", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device/assign/{service-platform}", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device/assign/{service-platform}", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device/{id}/software", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device/{id}/software", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device/summary/download", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device/summary/download", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device/pending", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device/pending", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device/pending/{id}", "method": "DELETE", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device/pending/{id}", "encoding": "no-op", "sd": "static", "method": "DELETE", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device/model", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device/model", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device/download", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device/download", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device/force-renew", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device/force-renew", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/device/allow-transport", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/device/allow-transport", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/device/{status}", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/device/{status}", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/device/{time}", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/device/{time}", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/device/software/{ota}", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/device/software/{ota}", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/service-platform", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/service-platform", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/service-platform/devices", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/service-platform/devices", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/issuer", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/issuer", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/certificate", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/certificate", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/issuer/{issuerId}/pem", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/issuer/{issuerId}/pem", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/certificate/{serialNumber}/pem", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/certificate/{serialNumber}/pem", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/config", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/config", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/issuer", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/issuer", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/certificate/sign", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/certificate/sign", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/root-ca", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/root-ca", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/inter-ca", "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/inter-ca", "encoding": "no-op", "sd": "static", "method": "POST", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/issuer/{issuerId}", "method": "PUT", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/issuer/{issuerId}", "encoding": "no-op", "sd": "static", "method": "PUT", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/issuer/{issuerId}/default", "method": "PUT", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/issuer/{issuerId}/default", "encoding": "no-op", "sd": "static", "method": "PUT", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/config", "method": "PUT", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/config", "encoding": "no-op", "sd": "static", "method": "PUT", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/issuer/{issuerId}", "method": "DELETE", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/issuer/{issuerId}", "encoding": "no-op", "sd": "static", "method": "DELETE", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/management/pki/authority/{caName}/certificate/{serialNumber}", "method": "DELETE", "output_encoding": "no-op", "backend": [{"url_pattern": "/api/management/pki/authority/{caName}/certificate/{serialNumber}", "encoding": "no-op", "sd": "static", "method": "DELETE", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/swagger-ui/{all}", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/swagger-ui/{all}", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/v3/api-docs/{all}", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/v3/api-docs/{all}", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/v3/api-docs", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/v3/api-docs", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}, {"endpoint": "/dms/api/api-docs", "method": "GET", "output_encoding": "no-op", "backend": [{"url_pattern": "/v3/api-docs", "encoding": "no-op", "sd": "static", "method": "GET", "host": ["http://dms:8080"], "disable_host_sanitize": false}], "input_query_strings": ["*"], "input_headers": ["*"]}], "port": 8000, "disable_keep_alives": false}