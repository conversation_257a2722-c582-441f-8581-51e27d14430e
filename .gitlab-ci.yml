image: docker:18-dind
services:
  - name: docker:18-dind
    entrypoint: ["env", "-u", "DOCKER_HOST"]
    command: ["dockerd-entrypoint.sh"]

workflow:
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH == "upgrade/spring-boot-3"
    - if: $CI_COMMIT_TAG =~ "/^[vV]\d+\.\d+\.\d+(-rc\d+)?$/"
    

variables: &default_vars
  DOCKER_HOST: tcp://docker:2375/
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  SPRING_PROFILES_ACTIVE: gitlab-ci
  # maven cache
  MAVEN_OPTS: "-Djava.awt.headless=true -Dmaven.repo.local=./.m2/repository"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version"
  ARTIFACT_PATH: $CI_PROJECT_DIR/artifacts

cache: &maven_caching
  paths:
    - ./.m2/repository
  key: $CI_PROJECT_NAME
  policy: pull-push

stages:
  - package
  - analysis
  - build_and_push_dev
  - deploy_dev
  - SCC
  - deploy_uat
  - deploy_prod


test_app:
  image: maven:3.8.5-openjdk-17
  stage: package
  script: "mvn test -Dskip.npm"
  tags:
    - SCA
  artifacts:
    paths:
      - ./target/site/jacoco/jacoco.xml
    expire_in: "1 day"
  allow_failure: true

sonarqube-check:
  image:
    name: maven:3.8.5-openjdk-17
  stage: analysis
  tags:
    - SCA
  variables:
    <<: *default_vars 
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.m2"
    GIT_DEPTH: "0"
  script:
    - echo "************* sonar.styl.solutions" >> /etc/hosts
    - |
      mvn verify sonar:sonar \
        -Dsonar.qualitygate.wait=true \
        -Dsonar.qualitygate.timeout=3000 \
        -Dmaven.test.skip=true \
        -Dsonar.projectKey=dms_backend_styl-dms_AYrVgamsICv5zo8reSlY \
        -Dsonar.projectVersion=$(cat pom.xml | grep "<app.version>" | cut -d ">" -f 2 | cut -d "<" -f 1)
  allow_failure: true
  needs:
    - test_app

  cache: 
    <<: *maven_caching

maven-package:
  image: maven:3.8.5-openjdk-17
  stage: package
  only:
   - tags
   - develop
   - upgrade/spring-boot-3
  tags:
    - package
  script: 
    - "mvn clean package -Dmaven.test.skip=true"
  artifacts:
    paths:
      - ./target/*.jar
    expire_in: "30 days"

docker-build-dev:
  stage: build_and_push_dev
  image: 
    name: gcr.io/kaniko-project/executor:v1.9.0-debug
    entrypoint: [""]
  variables:
    REGISTRY: "nexus-docker.styl.solutions/repository/docker-hosted/dms/backend"
    TAG_LATEST: $REGISTRY/$CI_COMMIT_REF_NAME:latest
    TAG_COMMIT: $REGISTRY/$CI_COMMIT_REF_NAME:$CI_COMMIT_SHORT_SHA
  tags:
    - build
  script:
    - |
      mkdir -p /kaniko/.docker
      echo "{\"auths\":{\"${CI_REGISTRY}\":{\"auth\":\"$(printf "%s:%s" "${CI_REGISTRY_USER}" "${CI_REGISTRY_PASSWORD}" | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor
      --context "$CI_PROJECT_DIR"
      --dockerfile "$CI_PROJECT_DIR/docker/Dockerfile"
      --destination "${TAG_LATEST}"
      --destination "${TAG_COMMIT}"
      --cache=true
      --cache-repo "${TAG_LATEST}"
    - mkdir -p /workspace
  dependencies:
    - maven-package
  needs:
    - maven-package

deploy-app-dev:
  image: 
    name: alpine:latest
    entrypoint: [""]
  stage: deploy_dev
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH == "upgrade/spring-boot-3"
    - if: $CI_COMMIT_TAG =~ "/^[vV]\d+\.\d+\.\d+(-rc\d+)?$/"
  tags:
    - deploy
  script:
    - scp docker/docker-compose.yml $SSH_USER@$VM_IPADDRESS:/home/<USER>/dms-data
    - scp docker/docker-start.sh $SSH_USER@$VM_IPADDRESS:/home/<USER>/dms-data
    - scp docker/krakend.json $SSH_USER@$VM_IPADDRESS:/home/<USER>/dms-data/krakend
    - ssh $SSH_USER@$VM_IPADDRESS "sudo docker login nexus-docker.styl.solutions -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}"
    - ssh $SSH_USER@$VM_IPADDRESS "cd /home/<USER>/dms-data && sh docker-start.sh"
  environment:
    name: development
    url: https://oceans-dev.styl.solutions/portal
  needs:
    - docker-build-dev

docker-build-aws-uat:
  stage: deploy_uat
  image: 
    name: gcr.io/kaniko-project/executor:v1.9.0-debug
    entrypoint: [""]
  only:
   - tags
   - upgrade/spring-boot-3
  variables:
    AWS_DEFAULT_REGION: "ap-southeast-1"
    ECR_NAME: dms
    ECR_URL: "406972242998.dkr.ecr.ap-southeast-1.amazonaws.com"
    AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID_SIT}
    AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY_SIT}
    TAG_LATEST: $ECR_URL/$ECR_NAME:uat
    TAG_COMMIT: $ECR_URL/$ECR_NAME:$CI_COMMIT_TAG
  tags:
    - build
  script:
    - |
      mkdir -p /kaniko/.docker
      echo "{\"credHelpers\":{\"${ECR_URL}\":\"ecr-login\"}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor
      --context "$CI_PROJECT_DIR"
      --dockerfile "$CI_PROJECT_DIR/docker/Dockerfile"
      --destination "${TAG_LATEST}"
      --destination "${TAG_COMMIT}"
      --cache=true
      --cache-repo "${TAG_LATEST}"
    - mkdir -p /workspace
  dependencies:
    - maven-package
  needs:
    - maven-package
    - test_app
  when: manual

deploy-app-uat:
  stage: deploy_uat
  image: 
    name: amazon/aws-cli:2.11.0
  variables:
    AWS_EZ_CLUSTER_NAME: DMS-SIT-Cluster
    AWS_EZ_SERVICE_NAME: DMS-SIT-Service
    AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID_SIT}
    AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY_SIT}
    AWS_DEFAULT_REGION: "ap-southeast-1"
    AWS_EZ_TASK_NAME: dms
    ECS_IMAGE_PULL_BEHAVIOR: always
  rules:
    - if: $CI_COMMIT_TAG =~ "/^[vV]\d+\.\d+\.\d+(-rc\d+)?$/"
    - if: $CI_COMMIT_BRANCH == "upgrade/spring-boot-3"
  tags:
    - deploy
  script:
    - |
      aws ecs update-service \
          --cluster $AWS_EZ_CLUSTER_NAME \
          --service $AWS_EZ_SERVICE_NAME \
          --task-definition $AWS_EZ_TASK_NAME \
          --force-new-deployment \
          --region $AWS_DEFAULT_REGION
  environment:
    name: uat
    url: https://oceans-uat.styl.solutions
  needs:
    - docker-build-aws-uat

docker-build-aws-prod:
  stage: deploy_prod
  image: 
    name: gcr.io/kaniko-project/executor:v1.9.0-debug
    entrypoint: [""]
  rules:
    - if: $CI_COMMIT_TAG =~ "/^[vV]\d+\.\d+\.\d+$/"
  variables:
    AWS_DEFAULT_REGION: "ap-southeast-1"
    ECR_NAME: dms
    ECR_URL: "406972242998.dkr.ecr.ap-southeast-1.amazonaws.com"
    AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID_SIT}
    AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY_SIT}
    TAG_LATEST: $ECR_URL/$ECR_NAME:latest
    TAG_COMMIT: $ECR_URL/$ECR_NAME:$CI_COMMIT_TAG
  tags:
    - build
  script:
    - |
      mkdir -p /kaniko/.docker
      echo "{\"credHelpers\":{\"${ECR_URL}\":\"ecr-login\"}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor
      --context "$CI_PROJECT_DIR"
      --dockerfile "$CI_PROJECT_DIR/docker/Dockerfile"
      --destination "${TAG_LATEST}"
      --destination "${TAG_COMMIT}"
      --cache=true
      --cache-repo "${TAG_LATEST}"
    - mkdir -p /workspace
  dependencies:
    - maven-package
  needs:
    - maven-package
    - test_app
    - trivy_container_scanning
    - sonarqube-check
  when: manual

deploy-app-prod:
  stage: deploy_prod
  image: 
    name: amazon/aws-cli:2.11.0
  variables:
    AWS_EZ_CLUSTER_NAME: DMS-PROD-Cluster
    AWS_EZ_SERVICE_NAME: DMS-PROD-Service
    AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID_PROD}
    AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY_PROD}
    AWS_DEFAULT_REGION: "ap-southeast-1"
    AWS_EZ_TASK_NAME: dms
    ECS_IMAGE_PULL_BEHAVIOR: always
  rules:
    - if: $CI_COMMIT_TAG =~ "/^[vV]\d+\.\d+\.\d+$/"
  tags:
    - deploy
  script:
    - |
      aws ecs update-service \
          --cluster $AWS_EZ_CLUSTER_NAME \
          --service $AWS_EZ_SERVICE_NAME \
          --task-definition $AWS_EZ_TASK_NAME \
          --force-new-deployment \
          --region $AWS_DEFAULT_REGION
  environment:
    name: prod
    url: https://oceans.styl.solutions
  needs:
    - deploy-app-uat
  when: manual

trivy_container_scanning:
  stage: SCC 
  image: alpine:3.11
  tags:
    - SCA
  rules:
    - if: $CI_COMMIT_TAG =~ "/^[vV]\d+\.\d+\.\d+(-rc\d+)?$/"
    - if: $CI_COMMIT_TAG =~ "/^[vV]\d+\.\d+\.\d+$/"
    
  script:
    - export TRIVY_VERSION=${TRIVY_VERSION:-v0.19.2}
    - apk add --no-cache curl docker-cli
    - docker login -u "${CI_REGISTRY_USER}" -p "${CI_REGISTRY_PASSWORD}" ${CI_REGISTRY}
    - curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin ${TRIVY_VERSION}
    - curl -sSL -o /tmp/trivy-gitlab.tpl https://github.com/aquasecurity/trivy/raw/${TRIVY_VERSION}/contrib/gitlab.tpl
    # - trivy exit-code 0 --cache-dir .trivycache/ --no-progress --format template --template "@/tmp/trivy-gitlab.tpl" -o ${CI_PROJECT_NAME}-container-scanning-report.json ${TAG_LATEST}
    - trivy image --no-progress --exit-code 0 --format template --template "@/tmp/trivy-gitlab.tpl" -o ${CI_PROJECT_NAME}-container-scanning-report.json ${TAG_LATEST}
    # - trivy image --no-progress --exit-code 0 --format template --template "@/usr/local/share/trivy/templates/html.tpl" -o ${CI_PROJECT_NAME}-container-scanning-report.htlm ${TAG_LATEST}
  artifacts:
    reports:
      container_scanning: ${CI_PROJECT_NAME}-container-scanning-report.json
    expire_in: "30 days"
  dependencies: []
  needs:
    - docker-build-dev